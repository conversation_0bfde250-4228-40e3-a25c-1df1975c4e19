# -*- coding: utf-8 -*-
"""
Codec Installation Helper and Troubleshooting Utilities
"""

import os
import platform
import subprocess
import webbrowser
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit, QTabWidget, QWidget, QMessageBox
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon


class CodecInstallationDialog(QDialog):
    """Comprehensive codec installation and troubleshooting dialog"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🎬 Video Codec Installation Guide")
        self.setMinimumSize(600, 500)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("🎬 Video Playback Troubleshooting Guide")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2196F3; margin: 10px;")
        layout.addWidget(title)
        
        # Tab widget for different platforms
        self.tabs = QTabWidget()
        
        # Windows tab
        self.tabs.addTab(self.create_windows_tab(), "🪟 Windows")
        
        # macOS tab
        self.tabs.addTab(self.create_macos_tab(), "🍎 macOS")
        
        # Linux tab
        self.tabs.addTab(self.create_linux_tab(), "🐧 Linux")
        
        # General tips tab
        self.tabs.addTab(self.create_general_tab(), "💡 General Tips")
        
        layout.addWidget(self.tabs)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("🧪 Test Codecs")
        self.test_btn.clicked.connect(self.test_codecs)
        
        self.download_btn = QPushButton("📥 Download K-Lite")
        self.download_btn.clicked.connect(self.download_klite)
        
        self.close_btn = QPushButton("✅ Close")
        self.close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(self.test_btn)
        button_layout.addWidget(self.download_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
    def create_windows_tab(self):
        """Create Windows-specific instructions"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h3>🔧 Windows Codec Installation</h3>
        
        <h4>Step 1: Install Windows Media Feature Pack (if needed)</h4>
        <p>If you're using Windows N or KN edition:</p>
        <ul>
            <li>Go to Settings → Apps → Optional Features</li>
            <li>Click "Add a feature"</li>
            <li>Search for "Media Feature Pack"</li>
            <li>Install it and restart your computer</li>
        </ul>
        
        <h4>Step 2: Install K-Lite Codec Pack (Recommended)</h4>
        <p>K-Lite Codec Pack includes all necessary codecs:</p>
        <ul>
            <li>Download from: <a href="https://codecguide.com/download_k-lite_codec_pack_standard.htm">codecguide.com</a></li>
            <li>Choose "Standard" version for most users</li>
            <li>Run installer with default settings</li>
            <li>Restart your computer after installation</li>
        </ul>
        
        <h4>Step 3: Alternative - Install VLC Media Player</h4>
        <p>VLC includes its own codecs:</p>
        <ul>
            <li>Download from: <a href="https://www.videolan.org/">videolan.org</a></li>
            <li>Install with default settings</li>
            <li>VLC codecs will be available system-wide</li>
        </ul>
        
        <h4>🔧 Troubleshooting</h4>
        <ul>
            <li>Update Windows to latest version</li>
            <li>Update graphics drivers</li>
            <li>Run Windows Media Player troubleshooter</li>
            <li>Check if antivirus is blocking media files</li>
        </ul>
        """)
        
        layout.addWidget(content)
        return widget
        
    def create_macos_tab(self):
        """Create macOS-specific instructions"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h3>🍎 macOS Codec Installation</h3>
        
        <h4>Step 1: Install VLC Media Player</h4>
        <p>VLC is the best solution for macOS:</p>
        <ul>
            <li>Download from: <a href="https://www.videolan.org/">videolan.org</a></li>
            <li>Drag VLC to Applications folder</li>
            <li>VLC includes comprehensive codec support</li>
        </ul>
        
        <h4>Step 2: Install Homebrew (Optional)</h4>
        <p>For advanced users who want FFmpeg:</p>
        <ul>
            <li>Install Homebrew: <code>/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"</code></li>
            <li>Install FFmpeg: <code>brew install ffmpeg</code></li>
        </ul>
        
        <h4>Step 3: System Preferences</h4>
        <ul>
            <li>Go to System Preferences → Security & Privacy</li>
            <li>Allow apps downloaded from App Store and identified developers</li>
            <li>Grant necessary permissions to video applications</li>
        </ul>
        
        <h4>🔧 Troubleshooting</h4>
        <ul>
            <li>Update macOS to latest version</li>
            <li>Reset NVRAM/PRAM if having hardware issues</li>
            <li>Check Activity Monitor for conflicting processes</li>
            <li>Try playing videos in QuickTime Player first</li>
        </ul>
        """)
        
        layout.addWidget(content)
        return widget
        
    def create_linux_tab(self):
        """Create Linux-specific instructions"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h3>🐧 Linux Codec Installation</h3>
        
        <h4>Ubuntu/Debian:</h4>
        <pre>
sudo apt update
sudo apt install ubuntu-restricted-extras
sudo apt install vlc
sudo apt install gstreamer1.0-plugins-{bad,good,ugly}
sudo apt install libavcodec-extra
        </pre>
        
        <h4>Fedora/RHEL:</h4>
        <pre>
sudo dnf install gstreamer1-plugins-{bad,good,ugly}
sudo dnf install vlc
sudo dnf install ffmpeg
        </pre>
        
        <h4>Arch Linux:</h4>
        <pre>
sudo pacman -S gstreamer-vaapi
sudo pacman -S vlc
sudo pacman -S ffmpeg
        </pre>
        
        <h4>openSUSE:</h4>
        <pre>
sudo zypper install vlc
sudo zypper install gstreamer-plugins-{bad,good,ugly}
sudo zypper install ffmpeg
        </pre>
        
        <h4>🔧 Troubleshooting</h4>
        <ul>
            <li>Check if you have proper graphics drivers installed</li>
            <li>Verify GStreamer plugins: <code>gst-inspect-1.0</code></li>
            <li>Test with: <code>ffplay your_video.mp4</code></li>
            <li>Check audio system: <code>pulseaudio --check</code></li>
        </ul>
        """)
        
        layout.addWidget(content)
        return widget
        
    def create_general_tab(self):
        """Create general tips and troubleshooting"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <h3>💡 General Tips & Best Practices</h3>
        
        <h4>🎬 Recommended Video Formats</h4>
        <ul>
            <li><strong>MP4 (H.264 + AAC)</strong> - Best compatibility</li>
            <li><strong>WebM (VP8/VP9 + Vorbis)</strong> - Good for web</li>
            <li><strong>AVI (with standard codecs)</strong> - Widely supported</li>
        </ul>
        
        <h4>🔧 Video Conversion Tools</h4>
        <ul>
            <li><strong>HandBrake</strong> - Free, user-friendly converter</li>
            <li><strong>FFmpeg</strong> - Command-line powerhouse</li>
            <li><strong>VLC Media Player</strong> - Built-in conversion</li>
        </ul>
        
        <h4>⚡ Performance Tips</h4>
        <ul>
            <li>Use hardware acceleration when available</li>
            <li>Close unnecessary applications</li>
            <li>Ensure sufficient RAM and storage space</li>
            <li>Keep your system and drivers updated</li>
        </ul>
        
        <h4>🐛 Common Issues & Solutions</h4>
        <ul>
            <li><strong>No audio:</strong> Check audio codec (prefer AAC)</li>
            <li><strong>Green screen:</strong> Update graphics drivers</li>
            <li><strong>Choppy playback:</strong> Lower video quality or resolution</li>
            <li><strong>File won't open:</strong> Try different media player</li>
        </ul>
        
        <h4>📱 Online Video Issues</h4>
        <ul>
            <li>Check internet connection speed</li>
            <li>Clear browser cache and cookies</li>
            <li>Disable browser extensions temporarily</li>
            <li>Try incognito/private browsing mode</li>
        </ul>
        """)
        
        layout.addWidget(content)
        return widget
        
    def test_codecs(self):
        """Test available codecs on the system"""
        QMessageBox.information(self, "Codec Test", 
                              "Codec testing feature will be implemented in a future update.\n\n"
                              "For now, try playing a sample video file to test your codecs.")
        
    def download_klite(self):
        """Open K-Lite Codec Pack download page"""
        webbrowser.open("https://codecguide.com/download_k-lite_codec_pack_standard.htm")


class CodecHelper:
    """Static helper class for codec-related utilities"""
    
    @staticmethod
    def show_installation_guide(parent=None):
        """Show the codec installation guide dialog"""
        dialog = CodecInstallationDialog(parent)
        dialog.exec_()
    
    @staticmethod
    def get_system_info():
        """Get system information for troubleshooting"""
        info = {
            'platform': platform.system(),
            'platform_version': platform.version(),
            'architecture': platform.architecture()[0],
            'python_version': platform.python_version(),
        }
        
        # Add platform-specific info
        if info['platform'] == 'Windows':
            info['windows_edition'] = platform.win32_edition()
        elif info['platform'] == 'Darwin':
            info['macos_version'] = platform.mac_ver()[0]
        elif info['platform'] == 'Linux':
            info['linux_distribution'] = platform.linux_distribution()
            
        return info
    
    @staticmethod
    def check_codec_availability():
        """Check if common codecs are available"""
        # This is a placeholder for future implementation
        # Could check for FFmpeg, GStreamer plugins, etc.
        return {
            'ffmpeg': False,  # Would check if FFmpeg is available
            'gstreamer': False,  # Would check GStreamer plugins
            'vlc': False,  # Would check if VLC is installed
        }
    
    @staticmethod
    def get_error_solution(error_message):
        """Get suggested solutions for common error messages"""
        error_lower = error_message.lower()
        
        if 'codec' in error_lower or 'format' in error_lower:
            return (
                "This appears to be a codec-related issue.\n\n"
                "Solutions:\n"
                "1. Install K-Lite Codec Pack (Windows)\n"
                "2. Install VLC Media Player\n"
                "3. Convert video to MP4 format\n"
                "4. Update your media player"
            )
        elif 'network' in error_lower or 'connection' in error_lower:
            return (
                "This appears to be a network-related issue.\n\n"
                "Solutions:\n"
                "1. Check your internet connection\n"
                "2. Try a different network\n"
                "3. Disable VPN temporarily\n"
                "4. Check firewall settings"
            )
        elif 'permission' in error_lower or 'access' in error_lower:
            return (
                "This appears to be a permissions issue.\n\n"
                "Solutions:\n"
                "1. Run the application as administrator\n"
                "2. Check file permissions\n"
                "3. Move file to a different location\n"
                "4. Check antivirus settings"
            )
        else:
            return (
                "For general video playback issues:\n\n"
                "1. Install latest codecs (K-Lite Codec Pack)\n"
                "2. Update graphics drivers\n"
                "3. Try playing in VLC Media Player\n"
                "4. Convert video to MP4 format\n"
                "5. Check system requirements"
            )
