# -*- coding: utf-8 -*-
"""
Audio mixer widget for managing multiple audio tracks
"""

from core.libraries import *
from core.theme import *
import os


class AudioTrackWidget(QWidget):
    """Widget representing a single audio track in the mixer"""
    
    # Signals
    volume_changed = pyqtSignal(str, int)  # path, volume
    mute_toggled = pyqtSignal(str, bool)   # path, muted
    remove_requested = pyqtSignal(str)     # path
    
    def __init__(self, audio_path, parent=None):
        super().__init__(parent)
        self.audio_path = audio_path
        self.is_muted = False
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the track widget UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Track name
        filename = os.path.basename(self.audio_path)
        self.name_label = QLabel(filename)
        self.name_label.setToolTip(self.audio_path)
        self.name_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-size: 11px;
                min-width: 120px;
                max-width: 120px;
            }}
        """)
        
        # Mute button
        self.mute_btn = QPushButton("🔊")
        self.mute_btn.setFixedSize(30, 30)
        self.mute_btn.setCheckable(True)
        self.mute_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 15px;
                font-size: 12px;
            }}
            QPushButton:checked {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        # Volume slider
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)
        self.volume_slider.setFixedWidth(100)
        self.volume_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {PANEL_COLOR};
                height: 6px;
                background: {BACKGROUND_COLOR};
                margin: 2px 0;
                border-radius: 3px;
            }}
            QSlider::handle:horizontal {{
                background: {ACCENT_COLOR};
                border: 1px solid {PANEL_COLOR};
                width: 14px;
                margin: -2px 0;
                border-radius: 7px;
            }}
            QSlider::sub-page:horizontal {{
                background: {ACCENT_COLOR};
                border-radius: 3px;
            }}
        """)
        
        # Volume label
        self.volume_label = QLabel("80%")
        self.volume_label.setFixedWidth(35)
        self.volume_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-size: 10px;
                font-family: monospace;
            }}
        """)
        
        # Remove button
        self.remove_btn = QPushButton("×")
        self.remove_btn.setFixedSize(25, 25)
        self.remove_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #b91c1c;
            }}
        """)
        
        # Add widgets to layout
        layout.addWidget(self.name_label)
        layout.addWidget(self.mute_btn)
        layout.addWidget(self.volume_slider)
        layout.addWidget(self.volume_label)
        layout.addWidget(self.remove_btn)
        
        # Apply track styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 5px;
                margin: 2px;
            }}
        """)
    
    def connect_signals(self):
        """Connect widget signals"""
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
        self.mute_btn.toggled.connect(self.on_mute_toggled)
        self.remove_btn.clicked.connect(lambda: self.remove_requested.emit(self.audio_path))
    
    def on_volume_changed(self, volume):
        """Handle volume slider changes"""
        self.volume_label.setText(f"{volume}%")
        self.volume_changed.emit(self.audio_path, volume)
    
    def on_mute_toggled(self, muted):
        """Handle mute button toggle"""
        self.is_muted = muted
        if muted:
            self.mute_btn.setText("🔇")
        else:
            self.mute_btn.setText("🔊")
        self.mute_toggled.emit(self.audio_path, muted)
    
    def set_volume(self, volume):
        """Set volume programmatically"""
        self.volume_slider.setValue(volume)
    
    def get_volume(self):
        """Get current volume"""
        return self.volume_slider.value()


class AudioMixer(QWidget):
    """Audio mixer widget for managing multiple audio tracks"""
    
    # Signals
    track_added = pyqtSignal(str)           # path
    track_removed = pyqtSignal(str)         # path
    track_volume_changed = pyqtSignal(str, int)  # path, volume
    track_mute_toggled = pyqtSignal(str, bool)   # path, muted
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.audio_tracks = {}  # path -> AudioTrackWidget
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the mixer UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("Audio Mixer")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {ACCENT_COLOR};
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 10px;
            }}
        """)
        
        # Tracks container
        self.tracks_widget = QWidget()
        self.tracks_layout = QVBoxLayout(self.tracks_widget)
        self.tracks_layout.setContentsMargins(0, 0, 0, 0)
        self.tracks_layout.setSpacing(5)
        
        # Scroll area for tracks
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.tracks_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid {PANEL_COLOR};
                border-radius: 5px;
                background-color: {BACKGROUND_COLOR};
            }}
        """)
        
        # Controls
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        
        # Add track button
        self.add_btn = QPushButton("+ Add Audio Track")
        self.add_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
        """)
        
        # Clear all button
        self.clear_btn = QPushButton("Clear All")
        self.clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #b91c1c;
            }}
        """)
        
        controls_layout.addWidget(self.add_btn)
        controls_layout.addWidget(self.clear_btn)
        controls_layout.addStretch()
        
        # Master volume
        master_widget = QWidget()
        master_layout = QHBoxLayout(master_widget)
        master_layout.setContentsMargins(0, 0, 0, 0)
        
        master_label = QLabel("Master Volume:")
        master_label.setStyleSheet(f"color: {TEXT_COLOR}; font-weight: bold;")
        
        self.master_slider = QSlider(Qt.Horizontal)
        self.master_slider.setRange(0, 100)
        self.master_slider.setValue(100)
        self.master_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {PANEL_COLOR};
                height: 8px;
                background: {BACKGROUND_COLOR};
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {ACCENT_COLOR};
                border: 1px solid {PANEL_COLOR};
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::sub-page:horizontal {{
                background: {ACCENT_COLOR};
                border-radius: 4px;
            }}
        """)
        
        self.master_label = QLabel("100%")
        self.master_label.setFixedWidth(40)
        self.master_label.setStyleSheet(f"color: {TEXT_COLOR}; font-family: monospace;")
        
        master_layout.addWidget(master_label)
        master_layout.addWidget(self.master_slider, 1)
        master_layout.addWidget(self.master_label)
        
        # Add all widgets to main layout
        layout.addWidget(title_label)
        layout.addWidget(scroll_area, 1)
        layout.addWidget(controls_widget)
        layout.addWidget(master_widget)
        
        # Apply overall styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border: 2px solid {ACCENT_COLOR};
                border-radius: 10px;
            }}
        """)
    
    def connect_signals(self):
        """Connect widget signals"""
        self.add_btn.clicked.connect(self.add_audio_track)
        self.clear_btn.clicked.connect(self.clear_all_tracks)
        self.master_slider.valueChanged.connect(self.on_master_volume_changed)
    
    def add_audio_track(self):
        """Open file dialog to add an audio track"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Select Audio File", 
            "", 
            "Audio Files (*.mp3 *.wav *.aac *.ogg *.flac *.m4a)"
        )
        
        if file_path and file_path not in self.audio_tracks:
            self.add_track(file_path)
    
    def add_track(self, audio_path):
        """Add a track widget for the given audio path"""
        if audio_path in self.audio_tracks:
            return
        
        track_widget = AudioTrackWidget(audio_path)
        track_widget.volume_changed.connect(self.track_volume_changed.emit)
        track_widget.mute_toggled.connect(self.track_mute_toggled.emit)
        track_widget.remove_requested.connect(self.remove_track)
        
        self.tracks_layout.addWidget(track_widget)
        self.audio_tracks[audio_path] = track_widget
        
        self.track_added.emit(audio_path)
    
    def remove_track(self, audio_path):
        """Remove a track from the mixer"""
        if audio_path in self.audio_tracks:
            track_widget = self.audio_tracks[audio_path]
            self.tracks_layout.removeWidget(track_widget)
            track_widget.deleteLater()
            del self.audio_tracks[audio_path]
            
            self.track_removed.emit(audio_path)
    
    def clear_all_tracks(self):
        """Remove all tracks from the mixer"""
        for audio_path in list(self.audio_tracks.keys()):
            self.remove_track(audio_path)
    
    def on_master_volume_changed(self, volume):
        """Handle master volume changes"""
        self.master_label.setText(f"{volume}%")
    
    def get_track_volumes(self):
        """Get volumes for all tracks"""
        return {path: widget.get_volume() for path, widget in self.audio_tracks.items()}
    
    def get_master_volume(self):
        """Get master volume"""
        return self.master_slider.value()


class AudioMixerGroupBox(QGroupBox):
    """Audio mixer wrapped in a group box (for compatibility)"""
    
    # Forward signals
    track_added = pyqtSignal(str)
    track_removed = pyqtSignal(str)
    track_volume_changed = pyqtSignal(str, int)
    track_mute_toggled = pyqtSignal(str, bool)
    
    def __init__(self, parent=None):
        super().__init__("Audio Mixer", parent)
        layout = QVBoxLayout(self)
        
        self.mixer = AudioMixer()  # Create AudioMixer instance, not AudioMixerGroupBox
        
        # Forward signals
        self.mixer.track_added.connect(self.track_added.emit)
        self.mixer.track_removed.connect(self.track_removed.emit)
        self.mixer.track_volume_changed.connect(self.track_volume_changed.emit)
        self.mixer.track_mute_toggled.connect(self.track_mute_toggled.emit)
        
        layout.addWidget(self.mixer)
        
        # Apply group box styling
        self.setStyleSheet(f"""
            QGroupBox {{
                color: {TEXT_COLOR};
                border: 2px solid {PANEL_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                font-weight: bold;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {ACCENT_COLOR};
            }}
        """)
    
    def add_track(self, audio_path):
        """Add a track to the mixer"""
        self.mixer.add_track(audio_path)
