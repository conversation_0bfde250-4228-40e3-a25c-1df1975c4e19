# -*- coding: utf-8 -*-
# Helper functions for the video application

import os
import time
import json
import uuid
import hashlib
import subprocess
import shutil
import tempfile
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from pathlib import Path

# Simple in-memory cache for development
_cache = {}

def get_cache(key: str, cache_dir: str = ".cache") -> Optional[Any]:
    """
    Get data from cache. First checks in-memory cache, then file cache.
    
    Args:
        key: Cache key
        cache_dir: Cache directory (for file cache)
        
    Returns:
        Cached data or None if not found or expired
    """
    # First check in-memory cache
    item = _cache.get(key)
    if item and item['expiry'] > datetime.now():
        return item['value']
    
    # Fall back to file cache
    os.makedirs(cache_dir, exist_ok=True)
    
    # Create filename from key hash
    key_hash = hashlib.md5(key.encode()).hexdigest()
    cache_file = os.path.join(cache_dir, f"{key_hash}.json")
    
    if not os.path.exists(cache_file):
        return None
    
    try:
        with open(cache_file, 'r') as f:
            data = json.load(f)
        
        # Check if cache is expired
        if data.get('expires', 0) < time.time():
            os.remove(cache_file)
            return None
        
        return data.get('data')
    except:
        return None

def set_cache(key: str, data: Any, timeout: int = 3600, cache_dir: str = ".cache") -> bool:
    """
    Set data in cache. Sets both in-memory and file cache.
    
    Args:
        key: Cache key
        data: Data to cache
        timeout: Cache timeout in seconds
        cache_dir: Cache directory (for file cache)
        
    Returns:
        True if successful
    """
    # Set in-memory cache
    _cache[key] = {
        'value': data,
        'expiry': datetime.now() + timedelta(seconds=timeout)
    }
    
    # Also set file cache
    try:
        os.makedirs(cache_dir, exist_ok=True)
        
        # Create filename from key hash
        key_hash = hashlib.md5(key.encode()).hexdigest()
        cache_file = os.path.join(cache_dir, f"{key_hash}.json")
        
        cache_data = {
            'data': data,
            'expires': time.time() + timeout,
            'key': key
        }
        
        with open(cache_file, 'w') as f:
            json.dump(cache_data, f)
        
        return True
    except:
        return False

def generate_unique_filename(prefix: str, extension: str) -> str:
    """
    Generate a unique filename with timestamp and UUID.
    
    Args:
        prefix: Filename prefix
        extension: File extension
        
    Returns:
        Unique filename
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"{prefix}_{timestamp}_{unique_id}.{extension}"

def hash(text: str) -> str:
    """
    Simple hash function using MD5.
    
    Args:
        text: Text to hash
        
    Returns:
        MD5 hash string
    """
    return hashlib.md5(text.encode()).hexdigest()

def format_duration(seconds: float) -> str:
    """
    Format duration in seconds to HH:MM:SS format.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"

def format_file_size(size_bytes: int) -> str:
    """
    Format file size in bytes to human-readable format.
    
    Args:
        size_bytes: File size in bytes
        
    Returns:
        Formatted file size string
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024 and i < len(size_names) - 1:
        size /= 1024
        i += 1
    
    return f"{size:.2f} {size_names[i]}"

def safe_delete_file(file_path: str) -> bool:
    """
    Safely delete a file if it exists.
    
    Args:
        file_path: Path to file to delete
        
    Returns:
        True if deleted or doesn't exist, False if error
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
        return True
    except:
        return False

def create_temp_dir() -> str:
    """
    Create a temporary directory for processing.
    
    Returns:
        Path to temporary directory
    """
    temp_dir = os.path.join(tempfile.gettempdir(), f"video_app_{int(time.time())}")
    os.makedirs(temp_dir, exist_ok=True)
    return temp_dir

def cleanup_temp_dir(temp_dir: str) -> bool:
    """
    Clean up a temporary directory.
    
    Args:
        temp_dir: Path to temporary directory
        
    Returns:
        True if successful
    """
    try:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return True
    except:
        return False

def check_ffmpeg_availability() -> Dict[str, Any]:
    """
    Check if FFmpeg is available and provide installation instructions.
    
    Returns:
        Dictionary with availability status and instructions
    """
    ffmpeg_path = shutil.which('ffmpeg')
    
    if ffmpeg_path:
        try:
            result = subprocess.run(
                [ffmpeg_path, '-version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return {
                    "available": True,
                    "path": ffmpeg_path,
                    "message": "FFmpeg is installed and working correctly"
                }
        except:
            pass
    
    # FFmpeg not found or not working
    installation_instructions = {
        "windows": "Download from https://www.gyan.dev/ffmpeg/builds/ and add to PATH",
        "macos": "Run 'brew install ffmpeg' in Terminal",
        "linux": "Run 'sudo apt install ffmpeg' (Ubuntu/Debian) or similar for your distribution"
    }
    
    return {
        "available": False,
        "path": None,
        "message": "FFmpeg is required for video editing features",
        "installation_instructions": installation_instructions
    }
