# -*- coding: utf-8 -*-
"""
Color selection utilities for video editing
"""

from core.libraries import *
from core.theme import *

# Centralized text/background color presets for captions and overlays
CAPTION_PRESETS = {
    "default": {"text":"white", "stroke":"black", "bg":None},
    "inverse": {"text":"black", "stroke":"white", "bg":None},
    "boxed": {"text":"white", "stroke":"black", "bg":"#00000080"},
    "warning": {"text":"#FBBF24", "stroke":"black", "bg":"#11182780"},
}


class ColorPicker(QWidget):
    """Custom color picker widget"""

    color_changed = pyqtSignal(str)  # Hex color string

    def __init__(self, initial_color="#FFFFFF", parent=None):
        super().__init__(parent)
        self.current_color = initial_color
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Setup the color picker UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Color preview
        self.color_preview = QPushButton()
        self.color_preview.setFixedSize(40, 30)
        self.color_preview.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.current_color};
                border: 2px solid {PANEL_COLOR};
                border-radius: 5px;
            }}
            QPushButton:hover {{
                border: 2px solid {ACCENT_COLOR};
            }}
        """)

        # Color input
        self.color_input = QLineEdit(self.current_color)
        self.color_input.setFixedWidth(80)
        self.color_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 5px;
                font-family: monospace;
            }}
        """)

        # Browse button
        self.browse_btn = QPushButton("...")
        self.browse_btn.setFixedSize(30, 30)
        self.browse_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)

        layout.addWidget(self.color_preview)
        layout.addWidget(self.color_input)
        layout.addWidget(self.browse_btn)

    def connect_signals(self):
        """Connect widget signals"""
        self.color_preview.clicked.connect(self.open_color_dialog)
        self.browse_btn.clicked.connect(self.open_color_dialog)
        self.color_input.textChanged.connect(self.on_color_input_changed)
        self.color_input.returnPressed.connect(self.validate_color_input)

    def open_color_dialog(self):
        """Open the color selection dialog"""
        try:
            color = QColorDialog.getColor(QColor(self.current_color), self)
            if color.isValid():
                self.set_color(color.name())
        except:
            # Fallback if QColorDialog is not available
            pass

    def on_color_input_changed(self, text):
        """Handle color input text changes"""
        if self.is_valid_color(text):
            self.set_color(text, emit_signal=False)

    def validate_color_input(self):
        """Validate and apply color input"""
        text = self.color_input.text().strip()
        if self.is_valid_color(text):
            self.set_color(text)
        else:
            # Reset to current color if invalid
            self.color_input.setText(self.current_color)

    def is_valid_color(self, color_str):
        """Check if color string is valid"""
        if not color_str.startswith('#'):
            return False
        if len(color_str) != 7:
            return False
        try:
            int(color_str[1:], 16)
            return True
        except ValueError:
            return False

    def set_color(self, color, emit_signal=True):
        """Set the current color"""
        if self.is_valid_color(color):
            self.current_color = color
            self.color_input.setText(color)
            self.color_preview.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border: 2px solid {PANEL_COLOR};
                    border-radius: 5px;
                }}
                QPushButton:hover {{
                    border: 2px solid {ACCENT_COLOR};
                }}
            """)

            if emit_signal:
                self.color_changed.emit(color)

    def get_color(self):
        """Get the current color"""
        return self.current_color


# Convenience functions
def select_color(initial_color="#FFFFFF", parent=None):
    """Show color selection dialog and return selected color"""
    try:
        color = QColorDialog.getColor(QColor(initial_color), parent)
        if color.isValid():
            return color.name()
    except:
        pass
    return None


def create_color_picker(initial_color="#FFFFFF", parent=None):
    """Create a color picker widget"""
    return ColorPicker(initial_color, parent)


# Common color constants
COLORS = {
    'WHITE': '#FFFFFF',
    'BLACK': '#000000',
    'RED': '#FF0000',
    'GREEN': '#00FF00',
    'BLUE': '#0000FF',
    'YELLOW': '#FFFF00',
    'CYAN': '#00FFFF',
    'MAGENTA': '#FF00FF',
    'ORANGE': '#FFA500',
    'PURPLE': '#800080',
    'PINK': '#FFC0CB',
    'BROWN': '#A52A2A',
    'GRAY': '#808080',
    'SILVER': '#C0C0C0'
}
