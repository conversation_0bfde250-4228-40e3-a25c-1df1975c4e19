# -*- coding: utf-8 -*-
"""
Video adjustment utilities for brightness, contrast, saturation, etc.
"""

from core.libraries import *
import os
import moviepy.editor as mp
from typing import Optional, <PERSON>ple


def adjust_video_properties(
    video_path: str,
    brightness: float = 0.0,
    contrast: float = 1.0,
    saturation: float = 1.0,
    gamma: float = 1.0,
    output_path: Optional[str] = None
) -> str:
    """
    Adjust video properties like brightness, contrast, and saturation.
    
    Args:
        video_path: Path to the input video
        brightness: Brightness adjustment (-1.0 to 1.0, 0 = no change)
        contrast: Contrast multiplier (0.5 = half contrast, 2.0 = double contrast)
        saturation: Saturation multiplier (0 = grayscale, 1 = normal, 2 = very saturated)
        gamma: Gamma correction (0.5 = darker, 1.0 = normal, 2.0 = brighter)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Apply adjustments
        adjusted_video = video
        
        # Apply brightness adjustment
        if brightness != 0.0:
            adjusted_video = adjusted_video.fx(mp.vfx.colorx, 1 + brightness)
        
        # Apply contrast adjustment
        if contrast != 1.0:
            # Contrast adjustment using gamma correction
            adjusted_video = adjusted_video.fx(mp.vfx.gamma_corr, 1.0/contrast)
        
        # Apply gamma correction
        if gamma != 1.0:
            adjusted_video = adjusted_video.fx(mp.vfx.gamma_corr, gamma)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_adjusted.mp4"
        
        # Write output
        adjusted_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        adjusted_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to adjust video properties: {str(e)}")


def adjust_brightness(video_path: str, brightness: float, output_path: Optional[str] = None) -> str:
    """
    Adjust video brightness.
    
    Args:
        video_path: Path to the input video
        brightness: Brightness adjustment (-1.0 to 1.0)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    return adjust_video_properties(video_path, brightness=brightness, output_path=output_path)


def adjust_contrast(video_path: str, contrast: float, output_path: Optional[str] = None) -> str:
    """
    Adjust video contrast.
    
    Args:
        video_path: Path to the input video
        contrast: Contrast multiplier (0.5 = half, 2.0 = double)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    return adjust_video_properties(video_path, contrast=contrast, output_path=output_path)


def adjust_saturation(video_path: str, saturation: float, output_path: Optional[str] = None) -> str:
    """
    Adjust video saturation.
    
    Args:
        video_path: Path to the input video
        saturation: Saturation multiplier (0 = grayscale, 1 = normal, 2 = very saturated)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    return adjust_video_properties(video_path, saturation=saturation, output_path=output_path)


def adjust_gamma(video_path: str, gamma: float, output_path: Optional[str] = None) -> str:
    """
    Adjust video gamma correction.
    
    Args:
        video_path: Path to the input video
        gamma: Gamma value (0.5 = darker, 1.0 = normal, 2.0 = brighter)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    return adjust_video_properties(video_path, gamma=gamma, output_path=output_path)


def convert_to_grayscale(video_path: str, output_path: Optional[str] = None) -> str:
    """
    Convert video to grayscale.
    
    Args:
        video_path: Path to the input video
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Convert to grayscale
        grayscale_video = video.fx(mp.vfx.blackwhite)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_grayscale.mp4"
        
        # Write output
        grayscale_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        grayscale_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to convert to grayscale: {str(e)}")


def invert_colors(video_path: str, output_path: Optional[str] = None) -> str:
    """
    Invert video colors.
    
    Args:
        video_path: Path to the input video
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Invert colors
        inverted_video = video.fx(mp.vfx.invert_colors)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_inverted.mp4"
        
        # Write output
        inverted_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        inverted_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to invert colors: {str(e)}")


def apply_sepia_effect(video_path: str, output_path: Optional[str] = None) -> str:
    """
    Apply sepia effect to video.
    
    Args:
        video_path: Path to the input video
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Apply sepia effect (simplified version)
        # Convert to grayscale first, then tint with sepia color
        sepia_video = video.fx(mp.vfx.blackwhite).fx(mp.vfx.colorx, 1.2)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_sepia.mp4"
        
        # Write output
        sepia_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        sepia_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to apply sepia effect: {str(e)}")


def adjust_hue(video_path: str, hue_shift: float, output_path: Optional[str] = None) -> str:
    """
    Adjust video hue.
    
    Args:
        video_path: Path to the input video
        hue_shift: Hue shift in degrees (-180 to 180)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Apply hue adjustment (simplified - using color multiplication)
        # This is a basic implementation; more sophisticated hue adjustment
        # would require HSV color space manipulation
        hue_factor = 1.0 + (hue_shift / 360.0)
        adjusted_video = video.fx(mp.vfx.colorx, hue_factor)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_hue_adjusted.mp4"
        
        # Write output
        adjusted_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        adjusted_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to adjust hue: {str(e)}")


def apply_color_filter(video_path: str, filter_color: str, intensity: float = 0.3, output_path: Optional[str] = None) -> str:
    """
    Apply a color filter to video.
    
    Args:
        video_path: Path to the input video
        filter_color: Filter color as hex string (e.g., "#FF0000" for red)
        intensity: Filter intensity (0.0 to 1.0)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Create color overlay
        color_clip = mp.ColorClip(size=video.size, color=filter_color, duration=video.duration)
        color_clip = color_clip.set_opacity(intensity)
        
        # Composite video with color filter
        filtered_video = mp.CompositeVideoClip([video, color_clip])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_color_filtered.mp4"
        
        # Write output
        filtered_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        color_clip.close()
        filtered_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to apply color filter: {str(e)}")


# Convenience functions for common adjustments
def brighten_video(video_path: str, amount: float = 0.2) -> str:
    """Brighten video by specified amount"""
    return adjust_brightness(video_path, amount)


def darken_video(video_path: str, amount: float = 0.2) -> str:
    """Darken video by specified amount"""
    return adjust_brightness(video_path, -amount)


def increase_contrast(video_path: str, amount: float = 1.5) -> str:
    """Increase video contrast"""
    return adjust_contrast(video_path, amount)


def decrease_contrast(video_path: str, amount: float = 0.7) -> str:
    """Decrease video contrast"""
    return adjust_contrast(video_path, amount)


def enhance_colors(video_path: str, amount: float = 1.3) -> str:
    """Enhance video colors (increase saturation)"""
    return adjust_saturation(video_path, amount)


def desaturate_video(video_path: str, amount: float = 0.5) -> str:
    """Desaturate video colors"""
    return adjust_saturation(video_path, amount)
