# -*- coding: utf-8 -*-
from core.libraries import *
from typing import List, Dict, Optional, Tuple, Union
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
from utils.video_utils import to_seconds

class VideoEditor:
    """Comprehensive video editing class with Adobe Premiere-like features."""
    
    def __init__(self):
        self.current_project = None
        self.timeline = []
    
    def add_image_overlay(self, video_path: str, image_path: str, 
                         position: Tuple[int, int], 
                         duration: Tuple[float, float],
                         size: Optional[Tuple[int, int]] = None,
                         opacity: float = 1.0,
                         animation: Optional[str] = None) -> str:
        """Add image overlay to video with positioning and animation."""
        video_clip = mp.VideoFileClip(video_path)
        image_clip = mp.ImageClip(image_path)
        
        # Resize if specified
        if size:
            image_clip = image_clip.resize(size)
        
        # Set duration and position
        start, end = duration
        image_clip = (
            image_clip.set_start(start)
            .set_duration(end - start)
            .set_position(position)
            .set_opacity(opacity)
        )
        
        # Apply animation
        if animation:
            image_clip = self._apply_image_animation(image_clip, animation)
        
        # Composite and save
        final = mp.CompositeVideoClip([video_clip, image_clip])
        out_path = os.path.splitext(video_path)[0] + "_with_overlay.mp4"
        final.write_videofile(out_path, codec="libx264", audio_codec="aac")
        
        video_clip.close()
        final.close()
        
        return out_path
    
    def add_external_voiceover(self, video_path: str, audio_path: str, 
                              start_time: float = 0.0,
                              volume: float = 1.0,
                              fade_duration: float = 0.5) -> str:
        """Add external voiceover to video with volume control and fading."""
        video = mp.VideoFileClip(video_path)
        voiceover = mp.AudioFileClip(audio_path)
        
        # Adjust volume
        if volume != 1.0:
            voiceover = voiceover.volumex(volume)
        
        # Apply fading
        voiceover = voiceover.fadein(fade_duration).fadeout(fade_duration)
        
        # Set start time
        voiceover = voiceover.set_start(start_time)
        
        # Mix with original audio
        final_audio = mp.CompositeAudioClip([video.audio, voiceover])
        video = video.set_audio(final_audio)
        
        out_path = os.path.splitext(video_path)[0] + "_with_voiceover.mp4"
        video.write_videofile(out_path, codec="libx264", audio_codec="aac")
        
        video.close()
        voiceover.close()
        
        return out_path
    
    def apply_video_effects(self, video_path: str, effects: List[str],
                          effect_params: Optional[Dict] = None) -> str:
        """Apply various video effects."""
        clip = mp.VideoFileClip(video_path)
        
        for effect in effects:
            clip = self._apply_single_effect(clip, effect, effect_params)
        
        out_path = os.path.splitext(video_path)[0] + "_with_effects.mp4"
        clip.write_videofile(out_path, codec="libx264", audio_codec="aac")
        
        clip.close()
        return out_path
    
    def _apply_single_effect(self, clip, effect: str, params: Optional[Dict]):
        """Apply a single video effect."""
        if effect == "blur":
            return clip.fl_image(lambda frame: self._blur_frame(frame, params))
        elif effect == "brightness":
            return clip.fl_image(lambda frame: self._adjust_brightness(frame, params))
        elif effect == "contrast":
            return clip.fl_image(lambda frame: self._adjust_contrast(frame, params))
        elif effect == "sepia":
            return clip.fl_image(lambda frame: self._apply_sepia(frame))
        elif effect == "vignette":
            return clip.fl_image(lambda frame: self._apply_vignette(frame, params))
        return clip
    
    def _blur_frame(self, frame, params):
        """Apply blur effect to frame."""
        blur_amount = params.get('amount', 2) if params else 2
        pil_image = Image.fromarray(frame)
        blurred = pil_image.filter(ImageFilter.GaussianBlur(blur_amount))
        return np.array(blurred)
    
    def _adjust_brightness(self, frame, params):
        """Adjust frame brightness."""
        factor = params.get('factor', 1.2) if params else 1.2
        return np.clip(frame.astype(np.float32) * factor, 0, 255).astype(np.uint8)
    
    def _adjust_contrast(self, frame, params):
        """Adjust frame contrast."""
        factor = params.get('factor', 1.5) if params else 1.5
        mean = frame.mean()
        return np.clip((frame - mean) * factor + mean, 0, 255).astype(np.uint8)
    
    def _apply_sepia(self, frame):
        """Apply sepia tone effect."""
        sepia_matrix = np.array([
            [0.393, 0.769, 0.189],
            [0.349, 0.686, 0.168],
            [0.272, 0.534, 0.131]
        ])
        sepia_frame = np.dot(frame, sepia_matrix.T)
        return np.clip(sepia_frame, 0, 255).astype(np.uint8)
    
    def _apply_vignette(self, frame, params):
        """Apply vignette effect."""
        strength = params.get('strength', 0.8) if params else 0.8
        height, width = frame.shape[:2]
        x = np.linspace(-1, 1, width)
        y = np.linspace(-1, 1, height)
        X, Y = np.meshgrid(x, y)
        radius = np.sqrt(X**2 + Y**2)
        mask = 1 - np.clip(radius * strength, 0, 1)
        mask = np.expand_dims(mask, axis=2)
        return (frame * mask).astype(np.uint8)
    
    def create_picture_in_picture(self, main_video: str, pip_video: str,
                                pip_position: Tuple[int, int],
                                pip_size: Tuple[int, int],
                                start_time: float = 0.0) -> str:
        """Create picture-in-picture effect."""
        main_clip = mp.VideoFileClip(main_video)
        pip_clip = mp.VideoFileClip(pip_video).resize(pip_size)
        
        pip_clip = pip_clip.set_start(start_time).set_position(pip_position)
        
        final = mp.CompositeVideoClip([main_clip, pip_clip])
        out_path = os.path.splitext(main_video)[0] + "_pip.mp4"
        final.write_videofile(out_path, codec="libx264", audio_codec="aac")
        
        main_clip.close()
        pip_clip.close()
        final.close()
        
        return out_path
    
    def generate_thumbnail(self, video_path: str, timestamp: str,
                         output_size: Tuple[int, int] = (320, 180)) -> str:
        """Generate thumbnail from video at specific timestamp."""
        seconds = to_seconds(timestamp)
        clip = mp.VideoFileClip(video_path)
        frame = clip.get_frame(seconds)
        
        # Convert to PIL Image for processing
        pil_image = Image.fromarray(frame)
        pil_image = pil_image.resize(output_size, Image.LANCZOS)
        
        # Save thumbnail
        out_path = os.path.splitext(video_path)[0] + f"_thumbnail_{timestamp.replace(':','')}.jpg"
        pil_image.save(out_path, "JPEG", quality=95)
        
        clip.close()
        return out_path

def optimize_for_social_media(video_path: str, platform: str = "general") -> str:
    """Optimize video for specific social media platforms."""
    clip = mp.VideoFileClip(video_path)
    
    # Platform-specific optimizations
    optimizations = {
        "instagram": {"size": (1080, 1080), "bitrate": "4000k"},
        "tiktok": {"size": (1080, 1920), "bitrate": "6000k"},
        "youtube": {"size": (1920, 1080), "bitrate": "8000k"},
        "general": {"size": None, "bitrate": "5000k"}
    }
    
    config = optimizations.get(platform.lower(), optimizations["general"])
    
    # Resize if needed
    if config["size"]:
        clip = clip.resize(config["size"])
    
    out_path = os.path.splitext(video_path)[0] + f"_optimized_{platform}.mp4"
    
    clip.write_videofile(
        out_path,
        codec="libx264",
        audio_codec="aac",
        bitrate=config["bitrate"],
        preset="fast",
        ffmpeg_params=["-movflags", "+faststart"]
    )
    
    clip.close()
    return out_path