#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the enhanced video player functionality
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLineEdit, QLabel
from PyQt5.QtCore import Qt

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.unified_video_player import UnifiedVideoPlayer


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎬 Video Player Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # URL input
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter YouTube URL or local file path...")
        layout.addWidget(QLabel("Video URL/Path:"))
        layout.addWidget(self.url_input)
        
        # Load button
        self.load_btn = QPushButton("Load Video")
        self.load_btn.clicked.connect(self.load_video)
        layout.addWidget(self.load_btn)
        
        # Video player
        self.player = UnifiedVideoPlayer(self)
        layout.addWidget(self.player)
        
        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
        # Connect signals
        self.player.error_occurred.connect(self.on_error)
        self.player.status_changed.connect(self.on_status_changed)
        self.player.playback_started.connect(self.on_playback_started)
        
        # Test URLs
        test_layout = QVBoxLayout()
        test_layout.addWidget(QLabel("Test URLs:"))
        
        youtube_btn = QPushButton("Test YouTube Video")
        youtube_btn.clicked.connect(lambda: self.load_test_url("https://www.youtube.com/watch?v=dQw4w9WgXcQ"))
        test_layout.addWidget(youtube_btn)
        
        layout.addLayout(test_layout)
        
    def load_video(self):
        """Load video from input field"""
        url = self.url_input.text().strip()
        if url:
            self.status_label.setText(f"Loading: {url}")
            self.player.load(url)
        else:
            self.status_label.setText("Please enter a URL or file path")
    
    def load_test_url(self, url):
        """Load a test URL"""
        self.url_input.setText(url)
        self.load_video()
    
    def on_error(self, error_message):
        """Handle player errors"""
        self.status_label.setText(f"Error: {error_message}")
        print(f"Player error: {error_message}")
    
    def on_status_changed(self, status):
        """Handle status changes"""
        self.status_label.setText(status)
        print(f"Status: {status}")
    
    def on_playback_started(self):
        """Handle playback started"""
        self.status_label.setText("Playback started!")
        print("Playback started!")


def main():
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QPushButton {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 12px;
        }
        QPushButton:hover {
            background-color: #505050;
        }
        QPushButton:pressed {
            background-color: #606060;
        }
        QLineEdit {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 8px;
            font-size: 12px;
        }
        QLabel {
            color: #ffffff;
            font-size: 12px;
        }
    """)
    
    window = TestWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
