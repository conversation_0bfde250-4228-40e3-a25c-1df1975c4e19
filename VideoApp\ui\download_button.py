# -*- coding: utf-8 -*-
"""
Download Button component for the video editor
"""

from PyQt5.QtWidgets import QPushButton, QProgressBar, QHBoxLayout, QWidget, QLabel, QFileDialog, QMessageBox
from PyQt5.QtCore import Qt, pyqtSignal
import core.theme as theme
from download_video.video_download import download_video
import threading
import os


class DownloadButton(QWidget):
    download_started = pyqtSignal(str)
    download_progress = pyqtSignal(int)
    download_finished = pyqtSignal(str)
    download_error = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Download button
        self.download_btn = QPushButton("⬇️ Download")
        self.download_btn.setCursor(Qt.PointingHandCursor)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setVisible(False)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setVisible(False)
        
        layout.addWidget(self.download_btn)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {theme.ACCENT_COLOR};
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-family: '{theme.APP_FONT_FAMILY}';
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {theme.ACCENT_COLOR_DARK};
                color: white;
            }}
            QPushButton:disabled {{
                background-color: #666;
                color: #999;
            }}
            QProgressBar {{
                border: 1px solid {theme.BORDER_COLOR};
                border-radius: 4px;
                text-align: center;
                background: {theme.PANEL_COLOR};
                color: {theme.TEXT_COLOR};
            }}
            QProgressBar::chunk {{
                background-color: {theme.ACCENT_COLOR};
                width: 10px;
            }}
        """)
        
        self.download_url = ""
        self.is_downloading = False
        
    def set_download_url(self, url):
        """Set the URL to download"""
        self.download_url = url
        self.download_btn.setEnabled(bool(url))
        
    def start_download(self):
        """Start the download process"""
        if not self.download_url:
            self.download_error.emit("No URL specified")
            return

        # Ask user where to save the video
        downloads_dir = os.path.join(os.path.expanduser("~"), "Downloads")
        if not os.path.exists(downloads_dir):
            downloads_dir = os.path.expanduser("~")

        # Suggest a filename based on URL or use default
        suggested_name = "video.mp4"
        try:
            # Try to extract a reasonable filename from URL
            from urllib.parse import urlparse, unquote
            parsed = urlparse(self.download_url)
            if parsed.path:
                path_parts = parsed.path.split('/')
                for part in reversed(path_parts):
                    if part and '.' in part:
                        suggested_name = unquote(part)
                        break
        except:
            pass

        # Show save dialog
        save_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Video As",
            os.path.join(downloads_dir, suggested_name),
            "Video Files (*.mp4 *.avi *.mkv *.mov *.webm);;All Files (*)"
        )

        if not save_path:
            # User cancelled
            return

        self.is_downloading = True
        self.download_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.status_label.setVisible(True)
        self.status_label.setText("Starting download...")

        self.download_started.emit(self.download_url)

        def download_thread():
            try:
                out = download_video(self.download_url, output_path=save_path, progress_hook=self.update_progress)
                self.download_finished.emit(out)
            except Exception as e:
                self.download_error.emit(str(e))

        thread = threading.Thread(target=download_thread)
        thread.start()
        
    def update_progress(self, progress):
        """Update download progress"""
        if isinstance(progress, dict) and 'downloaded_bytes' in progress and 'total_bytes' in progress:
            percentage = int((progress['downloaded_bytes'] / progress['total_bytes']) * 100)
            self.download_progress.emit(percentage)
            self.set_progress(percentage)
        
    def set_progress(self, value):
        """Set download progress (0-100)"""
        self.progress_bar.setValue(value)
        self.status_label.setText(f"Downloading... {value}%")
        
    def finish_download(self):
        """Finish the download process"""
        self.is_downloading = False
        self.download_btn.setEnabled(True)
        self.status_label.setText("Download complete!")
        
        # Hide progress after a delay
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(2000, lambda: self.progress_bar.setVisible(False))
        
    def handle_error(self, error_message):
        """Handle download error"""
        self.is_downloading = False
        self.download_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"Error: {error_message}")
        
        # Clear error after a delay
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(3000, lambda: self.status_label.setVisible(False))
