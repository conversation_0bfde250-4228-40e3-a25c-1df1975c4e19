# -*- coding: utf-8 -*-
from core.libraries import *
import os
from typing import <PERSON>ple, Optional

def export_video(input_path: str, resolution: Tuple[int,int]=(1920,1080), out_path: Optional[str]=None) -> str:
    if not os.path.exists(input_path):
        raise FileNotFoundError(input_path)
    w,h = resolution
    clip = mp.VideoFileClip(input_path).resize(newsize=(w,h))
    out_path = out_path or os.path.splitext(input_path)[0] + f"_{w}x{h}.mp4"
    clip.write_videofile(out_path, codec="libx264", audio_codec="aac")
    clip.close()
    return out_path
