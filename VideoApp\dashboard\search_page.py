# -*- coding: utf-8 -*-
from core.libraries import *
from core.theme import *
from utils.search_engine import MultiPlatformSearch
from ui.unified_video_player import UnifiedVideoPlayer
from ui.download_button import DownloadButton
from ui.video_crop_controls import VideoCropControls
import threading
from download_video.video_download import download_video
from ui.progress_dialog import ProgressDialog
from PyQt5.QtCore import QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QPixmap, QPainter
import webbrowser
import logging
import requests

# Set up logging
logger = logging.getLogger(__name__)

class ThumbnailLoader(QThread):
    thumbnail_loaded = pyqtSignal(str, QIcon)  # result_index, icon
    thumbnail_failed = pyqtSignal(str)  # result_index

    def __init__(self, url, result_index):
        super().__init__()
        self.url = url
        self.result_index = result_index

    def run(self):
        try:
            response = requests.get(self.url, timeout=5)
            response.raise_for_status()
            pixmap = QPixmap()
            if pixmap.loadFromData(response.content):
                scaled_pixmap = pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.thumbnail_loaded.emit(self.result_index, QIcon(scaled_pixmap))
            else:
                self.thumbnail_failed.emit(self.result_index)
        except Exception as e:
            print(f"Thumbnail load failed: {e}")
            self.thumbnail_failed.emit(self.result_index)

class SearchPage(QWidget):
    """Search bar with suggestions, multi-platform results, and inline video player."""
    
    # Define signals at class level
    download_complete_signal = pyqtSignal(str)
    download_error_signal = pyqtSignal(str)
    search_results_ready = pyqtSignal(list)
    search_error = pyqtSignal(str)
    
    def __init__(self, parent=None, video_player=None):
        super().__init__(parent)
        self.video_player = video_player
        self.engine = MultiPlatformSearch()
        self.current_result = None
        self.thumbnail_loaders = []  # Track active thumbnail loaders

        # Create main splitter for resizable panels
        self.main_splitter = QSplitter(Qt.Horizontal)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.addWidget(self.main_splitter)

        # Left panel for search and results with elegant styling
        left_panel = QWidget()
        left_panel.setMinimumWidth(380)
        left_panel.setMaximumWidth(520)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(12, 12, 12, 12)
        left_layout.setSpacing(8)

        # Apply consistent VS Code theme styling
        from core.theme import DARK_THEME
        left_panel.setStyleSheet(f"""
            QWidget {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                border-radius: 8px;
            }}
            QLabel {{
                color: {DARK_THEME['TEXT_COLOR']};
                font-weight: 500;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                padding: 5px;
            }}
            QPushButton {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 12px 18px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                font-weight: 500;
                min-height: 30px;
            }}
            QPushButton:hover {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
            QPushButton:pressed {{
                background-color: {DARK_THEME['ACCENT_COLOR_DARK']};
            }}
            QLineEdit {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 12px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                min-height: 25px;
            }}
            QLineEdit:focus {{
                border-color: {DARK_THEME['ACCENT_COLOR']};
                background-color: {DARK_THEME['PRIMARY_COLOR']};
            }}
            QComboBox {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 10px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
                min-height: 25px;
            }}
            QListWidget {{
                background-color: {DARK_THEME['BACKGROUND_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                padding: 5px;
                font-size: {DARK_THEME['APP_FONT_SIZE']}pt;
            }}
            QListWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
                margin: 2px;
                min-height: 20px;
            }}
            QListWidget::item:hover {{
                background-color: {DARK_THEME['PRIMARY_COLOR']};
            }}
            QListWidget::item:selected {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
        """)

        # Right panel for video player
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 10, 10, 10)

        # Style the right panel with consistent theme
        right_panel.setStyleSheet(f"""
            QWidget {{
                background-color: {DARK_THEME['BACKGROUND_COLOR']};
                border-radius: 8px;
            }}
        """)
        
        # 🔍 Search bar
        search_widget = QWidget()
        search_layout = QHBoxLayout(search_widget)
        search_layout.setContentsMargins(0, 0, 0, 0)
        self.query = QLineEdit()
        self.query.setPlaceholderText("Type here to search videos...")
        self.query.returnPressed.connect(self.on_enter)
        self.search_btn = QPushButton("🔍 Search")
        self.search_btn.clicked.connect(self.on_enter)
        
        search_layout.addWidget(self.query)
        search_layout.addWidget(self.search_btn)
        
        # Platform filter
        platform_widget = QWidget()
        platform_layout = QHBoxLayout(platform_widget)
        platform_layout.setContentsMargins(0, 0, 0, 0)
        platform_layout.addWidget(QLabel("Platform:"))
        self.platform_combo = QComboBox()
        self.platform_combo.addItems(["All", "YouTube", "Facebook", "Instagram", "TikTok", "Twitter"])
        platform_layout.addWidget(self.platform_combo)
        platform_layout.addStretch()
        
        # 📋 Suggestions
        self.suggestions = QListWidget()
        self.suggestions.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {DARK_THEME['BORDER_COLOR']};
            }}
            QListWidget::item:selected {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
            """
        )
        self.suggestions.itemClicked.connect(self.on_pick_suggestion)
        self.suggestions.setVisible(False)  # Initially hidden

        # 📋 Results
        self.results = QListWidget()
        self.results.setStyleSheet(
            f"""
            QListWidget {{
                background-color: {DARK_THEME['PANEL_COLOR']};
                color: {DARK_THEME['TEXT_COLOR']};
                border: 1px solid {DARK_THEME['BORDER_COLOR']};
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 15px;
                border-bottom: 1px solid {DARK_THEME['BORDER_COLOR']};
                min-height: 80px;
                word-wrap: break-word;
            }}
            QListWidget::item:selected {{
                background-color: {DARK_THEME['ACCENT_COLOR']};
                color: #ffffff;
            }}
            QListWidget::item:hover {{
                background-color: {DARK_THEME['PRIMARY_COLOR']};
            }}
            """
        )
        self.results.itemClicked.connect(self.on_pick_result)
        self.results.itemDoubleClicked.connect(self.on_double_click_result)

        # 🎬 Enhanced Video Player
        self.player = UnifiedVideoPlayer(self)

        # 🎬 Video Crop Controls
        self.crop_controls = VideoCropControls(self)
        
        # Action buttons for selected result
        action_widget = QWidget()
        action_layout = QHBoxLayout(action_widget)
        action_layout.setContentsMargins(0, 0, 0, 0)
        self.download_btn = DownloadButton()
        self.edit_btn = QPushButton("Edit Video")
        self.edit_btn.setEnabled(False)
        
        action_layout.addWidget(self.download_btn)
        action_layout.addWidget(self.edit_btn)
        
        # Status label
        self.status_label = QLabel("Ready to search")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 11px;
                padding: 5px;
                background-color: #2d2d30;
                border-radius: 4px;
            }
        """)
        
        # Add to left layout
        left_layout.addWidget(QLabel("🔍 VIDEO SEARCH"))
        left_layout.addWidget(search_widget)
        left_layout.addWidget(platform_widget)
        left_layout.addWidget(QLabel("Suggestions"))
        left_layout.addWidget(self.suggestions, 1)
        left_layout.addWidget(QLabel("Results"))
        left_layout.addWidget(self.results, 2)
        left_layout.addWidget(action_widget)
        left_layout.addWidget(self.status_label)
        
        # Add to right layout
        right_layout.addWidget(QLabel("Preview"))
        right_layout.addWidget(self.player, 1)
        right_layout.addWidget(self.crop_controls)
        
        # Add panels to splitter for resizable layout
        self.main_splitter.addWidget(left_panel)
        self.main_splitter.addWidget(right_panel)

        # Set splitter proportions with better balance to prevent overlapping
        self.main_splitter.setSizes([450, 750])  # Increased right panel size
        self.main_splitter.setStretchFactor(0, 0)  # Left panel fixed size
        self.main_splitter.setStretchFactor(1, 1)  # Right panel stretches
        
        # Connect signals
        self.query.textChanged.connect(self.on_type)
        self.download_btn.download_btn.clicked.connect(self.on_download)
        self.download_btn.download_progress.connect(self.handle_download_progress)
        self.download_btn.download_finished.connect(self.handle_download_complete)
        self.download_btn.download_error.connect(self.handle_download_error)
        self.edit_btn.clicked.connect(self.on_edit)
        
        # Connect search signals
        self.search_results_ready.connect(self._display_results)
        self.search_error.connect(self._display_error)

        # Connect crop controls signals
        self.crop_controls.crop_requested.connect(self.handle_crop_request)

        # Connect player position updates to crop controls
        if hasattr(self.player, 'local_player'):
            self.player.local_player.position_changed.connect(
                lambda pos: self.crop_controls.set_current_position(pos / 1000.0)
            )
            self.player.local_player.duration_changed.connect(
                lambda dur: self.crop_controls.set_video_duration(dur / 1000.0)
            )

        self.current_result = None
        self.next_track_index = 0  # Track counter for automatic track assignment

    # --- Events ---
    def on_type(self, text):
        """Show autocomplete suggestions while typing"""
        if len(text) > 2:  # Only show suggestions after 3 characters
            self.suggestions.clear()
            self.suggestions.setVisible(True)
            suggestions = self.engine.autocomplete(text)[:5]  # Limit to 5 suggestions
            for s in suggestions:
                self.suggestions.addItem(s)
            if not suggestions:
                self.suggestions.setVisible(False)
        else:
            self.suggestions.setVisible(False)

    def on_enter(self):
        """Trigger search when pressing Enter or clicking search button"""
        text = self.query.text().strip()
        platform = self.platform_combo.currentText()
        if text:
            self.populate_results(text, platform)
        else:
            self.status_label.setText("Please enter search terms")

    def on_pick_suggestion(self, item):
        """When a suggestion is clicked, trigger search"""
        try:
            if item and item.text():
                self.query.setText(item.text())
                self.suggestions.setVisible(False)
                self.populate_results(item.text(), self.platform_combo.currentText())
        except Exception as e:
            print(f"Error in suggestion selection: {e}")
            QMessageBox.warning(self, "Error", f"Failed to select suggestion: {str(e)}")

    def populate_results(self, text, platform="All"):
        """Populate results list with formatted items from all platforms"""
        self.results.clear()
        self.status_label.setText(f"Searching for '{text}'...")
        
        try:
            # Show loading indicator
            loading_item = QListWidgetItem("🔍 Searching... Please wait")
            loading_item.setFlags(Qt.NoItemFlags)  # Make it non-selectable
            self.results.addItem(loading_item)
            
            # Force UI update
            QApplication.processEvents()
            
            # Filter by platform if not "All"
            filters = {}
            if platform != "All":
                filters['platform'] = platform.lower()

            # Run search in thread to avoid UI freezing
            def search_thread():
                try:
                    results = self.engine.search(text, max_results=15, filters=filters)
                    if not results:
                        self.search_error.emit("No results found. Try different search terms or check your internet connection.")
                    else:
                        self.search_results_ready.emit(results)
                except Exception as e:
                    self.search_error.emit(f"Search error: {str(e)}")
            
            threading.Thread(target=search_thread, daemon=True).start()

        except Exception as e:
            self._display_error(str(e))

    def _display_results(self, results):
        """Display search results"""
        self.results.clear()
        self.thumbnail_loaders = []  # Clear previous loaders
        
        if not results:
            no_results_item = QListWidgetItem("❌ No videos found. Try different search terms")
            no_results_item.setFlags(Qt.NoItemFlags)
            self.results.addItem(no_results_item)
            self.status_label.setText("No results found")
            return
        
        for idx, res in enumerate(results):
            title = res.get("title", "Untitled")
            views = res.get("views", "Unknown views")
            platform_name = res.get("platform", "Unknown")
            duration = res.get("duration", "Unknown")

            # Truncate title if too long to prevent overlapping
            if len(title) > 60:
                title = title[:57] + "..."

            item_text = f"🎬 {title}\n📺 {platform_name} | 👁️ {views} | ⏱️ {duration}"
            item = QListWidgetItem(item_text)

            result_data = {
                "title": title,
                "url": res.get("url", ""),
                "stream_url": res.get("stream_url", ""),
                "platform": platform_name,
                "views": views,
                "duration": duration,
                "thumbnail": res.get("thumbnail", ""),
                "description": res.get("description", "")
            }

            item.setData(Qt.UserRole, result_data)
            self.results.addItem(item)
            
            # Load thumbnail asynchronously
            thumbnail_url = result_data.get("thumbnail")
            if thumbnail_url:
                loader = ThumbnailLoader(thumbnail_url, idx)
                loader.thumbnail_loaded.connect(lambda i, icon, it=item: self._set_item_icon(it, icon))
                loader.thumbnail_failed.connect(lambda i: self._set_item_placeholder(i))
                loader.start()
                self.thumbnail_loaders.append(loader)
        
        self.status_label.setText(f"✅ Found {len(results)} videos")
        self.edit_btn.setEnabled(False)
        self.download_btn.setEnabled(False)

    def _set_item_icon(self, item, icon):
        """Set icon on list item"""
        item.setIcon(icon)
        # Refresh the item to show icon
        row = self.results.row(item)
        self.results.takeItem(row)
        self.results.insertItem(row, item)

    def _set_item_placeholder(self, result_index):
        """Set placeholder icon for failed thumbnail"""
        item = self.results.item(result_index)
        if item:
            pm = QPixmap(64, 64)
            pm.fill(Qt.transparent)
            painter = QPainter(pm)
            painter.drawText(pm.rect(), Qt.AlignCenter, "🎬")
            painter.end()
            item.setIcon(QIcon(pm))
            self._set_item_icon(item, QIcon(pm))

    def _display_error(self, error_msg):
        """Display error message"""
        self.results.clear()
        error_item = QListWidgetItem(f"❌ {error_msg}")
        error_item.setFlags(Qt.NoItemFlags)
        self.results.addItem(error_item)
        
        help_item = QListWidgetItem("💡 Try checking your internet connection or using different search terms")
        help_item.setFlags(Qt.NoItemFlags)
        self.results.addItem(help_item)
        
        self.status_label.setText(f"Error: {error_msg}")
        self.edit_btn.setEnabled(False)
        self.download_btn.setEnabled(False)

    def on_pick_result(self, item):
        """When a result is clicked, play it in the video player"""
        try:
            if not item:
                return

            self.current_result = item.data(Qt.UserRole)
            print(f"Selected result: {self.current_result}")

            if not self.current_result:
                QMessageBox.information(self, "Info", "No video data available for this item")
                return

            # Get the URL - prioritize stream_url if available
            stream_url = self.current_result.get("stream_url")
            page_url = self.current_result.get("url")
            
            # If we don't have a direct stream URL, try to extract one
            if not stream_url and page_url:
                try:
                    # Use the search engine to get streamable URL
                    video_info = self.engine.fetch_from_link(page_url)
                    stream_url = video_info.get("stream_url")
                    # Update the current result with the fetched info
                    self.current_result.update(video_info)
                except Exception as e:
                    logger.error(f"Failed to extract stream URL: {e}")

            # Try to play the video
            if stream_url:
                # Validate it's direct (e.g., ends with .mp4 or similar)
                if not any(ext in stream_url.lower() for ext in ['.mp4', '.mkv', '.avi']):
                    logger.warning("Stream URL may not be direct - re-extracting")
                    video_info = self.engine.fetch_from_link(page_url)  # Re-fetch with MP4 preference
                    stream_url = video_info.get("stream_url")
                url_to_play = stream_url
            else:
                url_to_play = page_url
            
            if url_to_play:
                try:
                    # Create enhanced video info for the unified player
                    video_info = {
                        'url': page_url,
                        'playable_url': url_to_play,
                        'title': self.current_result.get('title', 'Unknown'),
                        'platform': self.current_result.get('platform', 'unknown'),
                        'stream_url': stream_url
                    }

                    # Load the video in the enhanced player
                    self.player.load_video_info(video_info)

                    # Enable action buttons
                    self.download_btn.setEnabled(True)
                    self.edit_btn.setEnabled(True)

                    # Set download URL for the download button
                    if hasattr(self.download_btn, 'set_download_url'):
                        self.download_btn.set_download_url(page_url or url_to_play)

                    self.status_label.setText("✅ Video loaded - Click play to start")

                    # Auto-play the video after a short delay
                    QTimer.singleShot(1500, self.safe_play_video)

                except Exception as load_error:
                    logger.error(f"Player load error: {load_error}")
                    # Fallback: open in browser
                    QMessageBox.information(self, "Info",
                        "Cannot play in app. Opening in browser instead.")
                    webbrowser.open(url_to_play)
            else:
                QMessageBox.information(self, "Info", "No playable URL found for this video")

        except Exception as e:
            logger.error(f"Playback error: {e}")
            QMessageBox.warning(self, "Playback Error", f"Could not play video: {str(e)}")

    def on_double_click_result(self, item):
        """Handle double-click on search result to play video immediately"""
        try:
            # First load the video (same as single click)
            self.on_pick_result(item)

            # Then immediately try to play it
            QTimer.singleShot(2000, self.force_play_video)

        except Exception as e:
            logger.error(f"Double-click playback error: {e}")
            QMessageBox.warning(self, "Playback Error", f"Could not play video: {str(e)}")

    def force_play_video(self):
        """Force play the video (used for double-click)"""
        try:
            if self.player and self.player.has_video_loaded():
                self.player.play()
                self.status_label.setText("▶️ Playing video (double-clicked)...")
                logger.info("Force-playing video after double-click")
            else:
                # Try again after a short delay
                QTimer.singleShot(1000, self.safe_play_video)
        except Exception as e:
            logger.error(f"Force play error: {e}")

    def safe_play_video(self):
        """Safely attempt to play the loaded video"""
        try:
            if self.player and self.player.has_video_loaded():
                self.player.play()
                self.status_label.setText("▶️ Playing video...")
                logger.info("Auto-playing video after load")
            else:
                logger.warning("No video loaded for playback")
                self.status_label.setText("⚠️ No video loaded")
        except Exception as e:
            logger.error(f"Error starting playback: {e}")
            self.status_label.setText(f"❌ Playback error: {str(e)}")
    
    def on_download(self):
        """Handle download button click"""
        if self.current_result:
            url = self.current_result.get("url")
            if url:
                self.download_btn.start_download()
    
    def on_edit(self):
        """Handle edit button click - send to edit page"""
        if self.current_result and hasattr(self.parent(), 'switch_to_edit_page'):
            url = self.current_result.get("url")
            if url:
                progress_dialog = ProgressDialog("Downloading for editing...", self)
                progress_dialog.show()
                
                def download_thread():
                    try:
                        out = download_video(url, progress_hook=progress_dialog.update_progress)
                        self.download_complete_signal.emit(out)
                    except Exception as e:
                        self.download_error_signal.emit(str(e))
                
                threading.Thread(target=download_thread).start()

    def handle_crop_request(self, start_time, end_time):
        """Handle video crop request and add to timeline"""
        try:
            if not self.current_result:
                QMessageBox.warning(self, "No Video", "No video selected for cropping")
                return

            # Get video URL
            video_url = self.current_result.get("url")
            video_title = self.current_result.get("title", "Unknown Video")

            if not video_url:
                QMessageBox.warning(self, "No URL", "No video URL available for cropping")
                return

            # Show progress dialog
            progress_dialog = ProgressDialog(f"Cropping video segment ({start_time:.1f}s - {end_time:.1f}s)...", self)
            progress_dialog.show()

            def crop_and_add_to_timeline():
                try:
                    # Download and crop the video segment
                    from download_video.video_download import download_video

                    # Download the video with time constraints
                    output_path = download_video(
                        video_url,
                        start_time=start_time,
                        end_time=end_time,
                        progress_hook=progress_dialog.update_progress
                    )

                    if output_path and isinstance(output_path, dict):
                        file_path = output_path.get('output_path') or output_path.get('filename')
                    else:
                        file_path = output_path

                    if file_path and os.path.exists(file_path):
                        # Add to timeline on next available track
                        self.add_clip_to_timeline(file_path, video_title, start_time, end_time)

                        # Reset crop selection
                        self.crop_controls.reset_crop_selection()

                        # Show success message
                        QTimer.singleShot(100, lambda: QMessageBox.information(
                            self, "Success",
                            f"Video segment added to Track {self.next_track_index}\n"
                            f"Duration: {end_time - start_time:.1f} seconds"
                        ))

                        # Increment track counter
                        self.next_track_index += 1

                    else:
                        QTimer.singleShot(100, lambda: QMessageBox.warning(
                            self, "Error", "Failed to crop video segment"
                        ))

                except Exception as e:
                    logger.error(f"Crop error: {e}")
                    QTimer.singleShot(100, lambda: QMessageBox.critical(
                        self, "Crop Error", f"Failed to crop video: {str(e)}"
                    ))
                finally:
                    progress_dialog.close()

            # Run cropping in separate thread
            threading.Thread(target=crop_and_add_to_timeline, daemon=True).start()

        except Exception as e:
            logger.error(f"Crop request error: {e}")
            QMessageBox.critical(self, "Error", f"Failed to process crop request: {str(e)}")

    def add_clip_to_timeline(self, file_path, title, start_time, end_time):
        """Add a cropped clip to the timeline"""
        try:
            # Check if we have access to the edit page through parent
            if hasattr(self.parent(), 'switch_to_edit_page'):
                # Switch to edit page and add the clip
                edit_page = self.parent().switch_to_edit_page()
                if edit_page and hasattr(edit_page, 'load_video'):
                    edit_page.load_video(file_path)
                    logger.info(f"Added clip to timeline: {title} ({start_time:.1f}s - {end_time:.1f}s)")
                else:
                    logger.warning("Could not access edit page to add clip")
            else:
                logger.warning("No parent with switch_to_edit_page method found")

        except Exception as e:
            logger.error(f"Timeline add error: {e}")
            QMessageBox.warning(self, "Timeline Error", f"Could not add clip to timeline: {str(e)}")
    
    def handle_download_progress(self, value):
        """Handle download progress updates"""
        pass
    
    def handle_download_complete(self, out):
        """Handle download completion"""
        if hasattr(self.parent(), 'switch_to_edit_page'):
            self.parent().switch_to_edit_page(out)
    
    def handle_download_error(self, error_msg):
        """Handle download error"""
        QMessageBox.warning(self, "Download Error", f"Failed to download: {error_msg}")
