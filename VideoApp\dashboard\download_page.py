# -*- coding: utf-8 -*-
from core.libraries import *
from core.theme import *
from utils.video_downloader import list_formats, download_video, download_audio_only
from ui.progress_dialog import ProgressDialog

class DownloadPage(QWidget):
    """Direct link paste, list formats, choose quality/resolution, download video or audio only."""

    # Define signals for thread communication
    update_formats_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    download_complete_signal = pyqtSignal(str, str)
    download_error_signal = pyqtSignal(str)

    def __init__(self, parent=None, video_player=None):
        super().__init__(parent)
        v = QVBoxLayout(self)
        self.video_player = video_player
        self.url = QLineEdit()
        self.url.setPlaceholderText("Paste video link (YouTube, Facebook, TikTok, Instagram, X)")
        self.btn_list = QPushButton("List Formats / Qualities")
        self.formats = QListWidget()
        
        # Progress indicator
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        self.format_box = QComboBox()
        self.btn_dl_video = QPushButton("Download Full Video")
        self.btn_dl_audio = QPushButton("Download Audio Only")
        
        # Add to edit page button
        self.btn_to_edit = QPushButton("Send to Editing")
        self.btn_to_edit.setEnabled(False)

        v.addWidget(QLabel("Direct Download"))
        v.addWidget(self.url)
        v.addWidget(self.btn_list)
        v.addWidget(self.formats, 3)
        v.addWidget(self.progress_bar)
        v.addWidget(QLabel("Select format to download:"))
        v.addWidget(self.format_box)
        v.addWidget(self.btn_dl_video)
        v.addWidget(self.btn_dl_audio)
        v.addWidget(self.btn_to_edit)

        self.btn_list.clicked.connect(self.on_list)
        self.btn_dl_video.clicked.connect(self.on_dl_video)
        self.btn_dl_audio.clicked.connect(self.on_dl_audio)
        self.btn_to_edit.clicked.connect(self.send_to_editing)

        # Connect signals to slots
        self.update_formats_signal.connect(self.update_formats)
        self.error_signal.connect(self.show_error)
        self.download_complete_signal.connect(self.handle_download_complete)
        self.download_error_signal.connect(self.handle_download_error)

        self.downloaded_path = None

    def on_list(self):
        self.formats.clear()
        self.format_box.clear()
        url = self.url.text().strip()
        if not url:
            QMessageBox.warning(self, "Missing URL", "Please paste a video link.")
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # indeterminate progress
        
        # Use thread to avoid UI freezing
        thread = threading.Thread(target=self._list_formats_thread, args=(url,))
        thread.start()

    def _list_formats_thread(self, url):
        try:
            fmts = list_formats(url)
            # Use signal to update UI from thread
            self.update_formats_signal.emit(fmts)
        except Exception as e:
            self.error_signal.emit(str(e))
        finally:
            self.progress_bar.setVisible(False)
    
    def update_formats(self, fmts):
        for f in fmts:
            txt = f"{f['format_id']} | {f.get('ext')} | {f.get('resolution','')} | {f.get('abr','')}"
            self.formats.addItem(txt)
            self.format_box.addItem(txt, userData=f)
    
    def show_error(self, error_msg):
        QMessageBox.warning(self, "Error", f"Failed to list formats: {error_msg}")

    def on_dl_video(self):
        f = self.format_box.currentData()
        url = self.url.text().strip()
        if not f:
            QMessageBox.warning(self, "Select format", "Please pick a format first.")
            return
        
        # Show progress dialog
        progress_dialog = ProgressDialog("Downloading video...", self)
        progress_dialog.show()
        
        def download_thread():
            try:
                out = download_video(url, format_id=f['format_id'], progress_hook=progress_dialog.update_progress)
                self.download_complete_signal.emit(out, "video")
            except Exception as e:
                self.download_error_signal.emit(str(e))
        
        thread = threading.Thread(target=download_thread)
        thread.start()

    def on_dl_audio(self):
        url = self.url.text().strip()
        
        # Show progress dialog
        progress_dialog = ProgressDialog("Downloading audio...", self)
        progress_dialog.show()
        
        def download_thread():
            try:
                out = download_audio_only(url, audio_format="mp3", progress_hook=progress_dialog.update_progress)
                self.download_complete_signal.emit(out, "audio")
            except Exception as e:
                self.download_error_signal.emit(str(e))
        
        thread = threading.Thread(target=download_thread)
        thread.start()
    
    def handle_download_complete(self, out, media_type):
        self.downloaded_path = out
        self.btn_to_edit.setEnabled(True)
        QMessageBox.information(self, "Downloaded", f"Saved: {out}")
    
    def handle_download_error(self, error_msg):
        QMessageBox.warning(self, "Download Error", f"Failed to download: {error_msg}")
    
    def send_to_editing(self):
        if self.downloaded_path and hasattr(self.parent(), 'switch_to_edit_page'):
            # Notify main window to switch to edit page and load this video
            self.parent().switch_to_edit_page(self.downloaded_path)


