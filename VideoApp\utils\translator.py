# -*- coding: utf-8 -*-
from core.libraries import *
from typing import List, Dict
import googletrans
from googletrans import Translator
import requests
from bs4 import BeautifulSoup

def translate(text: str, src='auto', dest='ur') -> str:
    if GoogleTranslator is None:
        return text
    return GoogleTranslator(source=src, target=dest).translate(text)

def translate_batch(texts: List[str], src='auto', dest='ur') -> List[str]:
    """Translate a batch of texts more efficiently"""
    if GoogleTranslator is None:
        return texts
    
    translator = GoogleTranslator(source=src, target=dest)
    return [translator.translate(text) for text in texts]

def detect_language(text: str) -> str:
    """Detect the language of the given text"""
    if GoogleTranslator is None:
        return "en"  # Default to English
    
    translator = GoogleTranslator()
    detection = translator.detect(text)
    return detection.lang

def get_supported_languages() -> Dict[str, str]:
    """Get all supported languages for translation"""
    return googletrans.LANGUAGES

def translate_with_alternatives(text: str, src='auto', dest='ur', num_alternatives=3) -> Dict:
    """Get translation with alternatives"""
    # This is a simplified implementation
    # For a real implementation, you might need a different API
    
    base_translation = translate(text, src, dest)
    
    # Generate some simple alternatives (in a real app, use a proper API)
    alternatives = [base_translation]
    
    # Simple variations for demonstration
    if dest == 'ur':
        alternatives.extend([
            base_translation + " (متبادل)",
            base_translation.replace("ہے", "ہیں") if "ہے" in base_translation else base_translation + " (متبادل)",
            base_translation.upper()
        ])
    else:
        alternatives.extend([
            base_translation + " (alternative)",
            base_translation.title(),
            base_translation.upper()
        ])
    
    return {
        "translation": base_translation,
        "alternatives": alternatives[:num_alternatives],
        "source_lang": detect_language(text),
        "target_lang": dest
    }

def translate_website(url: str, dest='ur') -> str:
    """Translate content from a website"""
    try:
        # Fetch website content
        response = requests.get(url)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract text content
        text_elements = soup.find_all(string=True)
        visible_texts = [elem.strip() for elem in text_elements 
                        if elem.parent.name not in ['script', 'style', 'meta', 'head'] 
                        and elem.strip()]
        
        # Translate visible text
        translated_texts = translate_batch(visible_texts, 'auto', dest)
        
        # Replace text in soup
        for i, elem in enumerate([e for e in text_elements 
                                if e.parent.name not in ['script', 'style', 'meta', 'head'] 
                                and e.strip()]):
            if i < len(translated_texts):
                elem.replace_with(translated_texts[i])
        
        return str(soup)
        
    except Exception as e:
        print(f"Website translation failed: {e}")
        return f"Translation failed: {str(e)}"

def create_translation_memory(original: str, translated: str, src_lang: str, dest_lang: str) -> Dict:
    """Create a translation memory entry"""
    return {
        "original": original,
        "translated": translated,
        "source_lang": src_lang,
        "target_lang": dest_lang,
        "timestamp": datetime.now().isoformat()
    }

def save_translation_memory(entries: List[Dict], file_path: str):
    """Save translation memory to a file"""
    import json
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(entries, f, ensure_ascii=False, indent=2)

def load_translation_memory(file_path: str) -> List[Dict]:
    """Load translation memory from a file"""
    import json
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def search_translation_memory(query: str, memory: List[Dict], src_lang: str = None, dest_lang: str = None) -> List[Dict]:
    """Search for existing translations in memory"""
    results = []
    for entry in memory:
        # Filter by language if specified
        if src_lang and entry['source_lang'] != src_lang:
            continue
        if dest_lang and entry['target_lang'] != dest_lang:
            continue
        
        # Search in original and translated text
        if query.lower() in entry['original'].lower() or query.lower() in entry['translated'].lower():
            results.append(entry)
    
    return results

def create_subtitle_translator():
    """Create a specialized translator for subtitles"""
    class SubtitleTranslator:
        def __init__(self, src_lang='auto', dest_lang='en'):
            self.src_lang = src_lang
            self.dest_lang = dest_lang
            self.memory = []
        
        def translate_subtitle(self, text: str, max_length: int = None) -> str:
            """Translate subtitle text with optional length constraint"""
            translation = translate(text, self.src_lang, self.dest_lang)
            
            # Apply length constraint if specified
            if max_length and len(translation) > max_length:
                # Simple shortening strategy
                if len(translation) > max_length + 10:
                    # If too long, split into two lines
                    words = translation.split()
                    mid = len(words) // 2
                    line1 = ' '.join(words[:mid])
                    line2 = ' '.join(words[mid:])
                    translation = f"{line1}\n{line2}"
                else:
                    # Slightly too long, try to shorten
                    translation = translation[:max_length-3] + "..."
            
            # Save to memory
            self.memory.append(create_translation_memory(
                text, translation, self.src_lang, self.dest_lang
            ))
            
            return translation
        
        def save_memory(self, file_path: str):
            """Save translation memory"""
            save_translation_memory(self.memory, file_path)
    
    return SubtitleTranslator
