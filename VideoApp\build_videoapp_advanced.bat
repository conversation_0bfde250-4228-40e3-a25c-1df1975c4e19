@echo off
setlocal enabledelayedexpansion

set PYTHON="C:\Program Files\Python313\python.exe"
set PROJECT="D:\BISH\VideoApp"
set ICON="%PROJECT%\assets\app_icon.ico"
set BUILD_TYPE=%1

if "%BUILD_TYPE%"=="" set BUILD_TYPE=onefile

echo ========================================
echo    Professional Video Editor Builder
echo         Advanced Build Script
echo ========================================
echo Build Type: %BUILD_TYPE%
echo Project: %PROJECT%
echo.

echo Cleaning previous builds...
cd /d %PROJECT%
rmdir /s /q build 2>nul
rmdir /s /q dist 2>nul

echo Verifying Python installation...
%PYTHON% --version
if %errorlevel% neq 0 (
    echo Error: Python not found at %PYTHON%
    echo Please update the PYTHON path in this script
    pause
    exit /b 1
)

echo Verifying icon exists...
if not exist %ICON% (
    echo Creating application icon...
    %PYTHON% create_icon.py
    if not exist %ICON% (
        echo Error: Failed to create icon file
        pause
        exit /b 1
    )
)

echo Installing/Updating build dependencies...
%PYTHON% -m pip install --upgrade pyinstaller pillow

echo Checking VideoApp dependencies...
echo Checking PyQt5...
%PYTHON% -c "import PyQt5; print('✓ PyQt5: OK')" 2>nul || (
    echo Installing PyQt5...
    %PYTHON% -m pip install PyQt5 PyQt5-tools
)

echo Checking yt-dlp...
%PYTHON% -c "import yt_dlp; print('✓ yt-dlp: OK')" 2>nul || (
    echo Installing yt-dlp...
    %PYTHON% -m pip install yt-dlp
)

echo Checking other dependencies...
%PYTHON% -c "import requests; print('✓ requests: OK')" 2>nul || %PYTHON% -m pip install requests
%PYTHON% -c "import numpy; print('✓ numpy: OK')" 2>nul || %PYTHON% -m pip install numpy

echo.
echo Building VideoApp executable...
echo Build configuration: %BUILD_TYPE%

if "%BUILD_TYPE%"=="onedir" (
    echo Building as directory distribution...
    set BUILD_OPTION=--onedir
) else (
    echo Building as single file...
    set BUILD_OPTION=--onefile
)

%PYTHON% -m PyInstaller ^
  %BUILD_OPTION% ^
  --windowed ^
  --name VideoApp ^
  --icon=%ICON% ^
  --add-data "%PROJECT%\assets;assets" ^
  --add-data "%PROJECT%\core;core" ^
  --add-data "%PROJECT%\dashboard;dashboard" ^
  --add-data "%PROJECT%\ui;ui" ^
  --add-data "%PROJECT%\utils;utils" ^
  --add-data "%PROJECT%\editing;editing" ^
  --add-data "%PROJECT%\export;export" ^
  --add-data "%PROJECT%\social;social" ^
  --hidden-import=PyQt5 ^
  --hidden-import=PyQt5.QtCore ^
  --hidden-import=PyQt5.QtWidgets ^
  --hidden-import=PyQt5.QtGui ^
  --hidden-import=PyQt5.QtMultimedia ^
  --hidden-import=PyQt5.QtMultimediaWidgets ^
  --hidden-import=PyQt5.QtWebEngineWidgets ^
  --hidden-import=PyQt5.QtNetwork ^
  --hidden-import=yt_dlp ^
  --hidden-import=yt_dlp.extractor ^
  --hidden-import=requests ^
  --hidden-import=json ^
  --hidden-import=threading ^
  --hidden-import=urllib.parse ^
  --hidden-import=urllib.request ^
  --hidden-import=datetime ^
  --hidden-import=os ^
  --hidden-import=sys ^
  --hidden-import=time ^
  --hidden-import=re ^
  --hidden-import=typing ^
  --hidden-import=concurrent.futures ^
  --hidden-import=subprocess ^
  --hidden-import=tempfile ^
  --hidden-import=shutil ^
  --hidden-import=pathlib ^
  --hidden-import=webbrowser ^
  --collect-all=yt_dlp ^
  --collect-submodules=PyQt5 ^
  --exclude-module=tkinter ^
  --exclude-module=matplotlib ^
  --exclude-module=scipy ^
  --exclude-module=pandas ^
  --clean ^
  --noconfirm ^
  --log-level=INFO ^
  main.py

if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo Build failed! 
    echo ========================================
    echo Check the error messages above.
    echo Common issues:
    echo - Missing dependencies
    echo - Insufficient disk space
    echo - Antivirus blocking PyInstaller
    echo ========================================
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================

if "%BUILD_TYPE%"=="onedir" (
    echo Executable location: %PROJECT%\dist\VideoApp\VideoApp.exe
    echo Distribution folder: %PROJECT%\dist\VideoApp\
    dir "%PROJECT%\dist\VideoApp\" | find "VideoApp.exe"
    
    echo.
    echo Creating portable package...
    cd /d "%PROJECT%\dist"
    if exist VideoApp_Portable.zip del VideoApp_Portable.zip
    powershell -command "Compress-Archive -Path 'VideoApp' -DestinationPath 'VideoApp_Portable.zip'"
    echo Portable package: %PROJECT%\dist\VideoApp_Portable.zip
) else (
    echo Executable location: %PROJECT%\dist\VideoApp.exe
    echo File size:
    dir "%PROJECT%\dist\VideoApp.exe" | find "VideoApp.exe"
)

echo.
echo Testing the executable...
if "%BUILD_TYPE%"=="onedir" (
    set EXEC_PATH="%PROJECT%\dist\VideoApp\VideoApp.exe"
) else (
    set EXEC_PATH="%PROJECT%\dist\VideoApp.exe"
)

echo Starting VideoApp for quick test...
start "" !EXEC_PATH!
timeout /t 3 /nobreak >nul
taskkill /f /im VideoApp.exe 2>nul

echo.
echo ========================================
echo Build Summary:
echo ========================================
echo - Application: Professional Video Editor
echo - Executable: VideoApp.exe
if "%BUILD_TYPE%"=="onedir" (
    echo - Type: Directory distribution
    echo - Location: %PROJECT%\dist\VideoApp\
    echo - Portable: VideoApp_Portable.zip
) else (
    echo - Type: Single file
    echo - Location: %PROJECT%\dist\
)
echo - Icon: Included
echo - Dependencies: Bundled
echo - Python: Not required for end users
echo ========================================

echo.
echo Usage Instructions:
echo ========================================
if "%BUILD_TYPE%"=="onedir" (
    echo 1. Copy the entire VideoApp folder to target machine
    echo 2. Run VideoApp.exe from the VideoApp folder
    echo 3. Or extract VideoApp_Portable.zip and run
) else (
    echo 1. Copy VideoApp.exe to target machine
    echo 2. Run VideoApp.exe directly
)
echo 3. No Python installation required on target machine
echo 4. All dependencies are bundled
echo ========================================

echo.
echo Build completed! You can now distribute the application.
echo.
pause