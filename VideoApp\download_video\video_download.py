# -*- coding: utf-8 -*-
"""
Video Download Module
Provides video downloading functionality using yt-dlp
"""

import os
import sys
import tempfile
from typing import Dict, Any, Optional, Callable

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import yt_dlp
except ImportError:
    print("❌ yt-dlp not installed. Please install it with: pip install yt-dlp")
    yt_dlp = None

from utils.helpers import generate_unique_filename, format_file_size


def download_video(url: str, output_path: str = None, format_id: str = "best", 
                  progress_hook: Callable = None, start_time: float = None, 
                  end_time: float = None) -> Dict[str, Any]:
    """
    Download video from URL using yt-dlp
    
    Args:
        url: Video URL to download
        output_path: Output file path (optional)
        format_id: Format ID or quality selector
        progress_hook: Progress callback function
        start_time: Start time for trimming (seconds)
        end_time: End time for trimming (seconds)
    
    Returns:
        Dict with download result information
    """
    if not yt_dlp:
        return {
            "success": False,
            "error": "yt-dlp not available. Please install it with: pip install yt-dlp"
        }
    
    try:
        # Set up download options
        downloads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "downloads")
        os.makedirs(downloads_dir, exist_ok=True)
        
        if not output_path:
            output_path = os.path.join(downloads_dir, "%(title)s.%(ext)s")
        
        ydl_opts = {
            'format': format_id,
            'outtmpl': output_path,
            'noplaylist': True,
        }
        
        # Add progress hook if provided
        if progress_hook:
            ydl_opts['progress_hooks'] = [progress_hook]
        
        # Add trimming options if specified
        if start_time is not None or end_time is not None:
            postprocessor_args = []
            if start_time is not None:
                postprocessor_args.extend(['-ss', str(start_time)])
            if end_time is not None:
                postprocessor_args.extend(['-to', str(end_time)])
            
            ydl_opts['postprocessor_args'] = postprocessor_args
        
        # Download the video
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=True)
            
            # Get the actual output filename
            if 'requested_downloads' in info and info['requested_downloads']:
                filename = info['requested_downloads'][0]['filepath']
            else:
                filename = ydl.prepare_filename(info)
            
            return {
                "success": True,
                "filename": filename,
                "title": info.get('title', 'Unknown'),
                "duration": info.get('duration', 0),
                "filesize": info.get('filesize', 0),
                "format": info.get('format', 'Unknown'),
                "url": url
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": url
        }


def get_video_info(url: str) -> Dict[str, Any]:
    """
    Get video information without downloading
    
    Args:
        url: Video URL
    
    Returns:
        Dict with video information
    """
    if not yt_dlp:
        return {
            "success": False,
            "error": "yt-dlp not available"
        }
    
    try:
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            
            return {
                "success": True,
                "title": info.get('title', 'Unknown'),
                "description": info.get('description', ''),
                "duration": info.get('duration', 0),
                "view_count": info.get('view_count', 0),
                "uploader": info.get('uploader', 'Unknown'),
                "upload_date": info.get('upload_date', ''),
                "thumbnail": info.get('thumbnail', ''),
                "formats": info.get('formats', [])
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def list_formats(url: str) -> list:
    """
    List available formats for a video
    
    Args:
        url: Video URL
    
    Returns:
        List of available formats
    """
    if not yt_dlp:
        return []
    
    try:
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'listformats': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            formats = info.get('formats', [])
            
            # Process formats for easier use
            processed_formats = []
            for fmt in formats:
                processed_formats.append({
                    'format_id': fmt.get('format_id', ''),
                    'ext': fmt.get('ext', ''),
                    'resolution': fmt.get('resolution', 'audio only'),
                    'filesize': fmt.get('filesize', 0),
                    'vcodec': fmt.get('vcodec', 'none'),
                    'acodec': fmt.get('acodec', 'none'),
                    'fps': fmt.get('fps', 0),
                    'format_note': fmt.get('format_note', ''),
                    'quality': fmt.get('quality', 0),
                    'height': fmt.get('height', 0),
                    'width': fmt.get('width', 0),
                })
            
            return processed_formats
            
    except Exception as e:
        print(f"Error listing formats: {e}")
        return []


# Compatibility functions for existing code
def download_video_standalone(url: str, format_id: str = "best", output_path: str = None,
                            start_time: float = None, end_time: float = None) -> Dict[str, Any]:
    """Standalone download function for compatibility"""
    return download_video(url, output_path, format_id, None, start_time, end_time)


def formats(url: str) -> list:
    """Alias for list_formats for compatibility"""
    return list_formats(url)
