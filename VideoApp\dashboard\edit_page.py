# -*- coding: utf-8 -*-
from core.libraries import *
import logging

logger = logging.getLogger(__name__)
from editing.cutter import cut_subclip
from editing.extract_audio import extract_audio_from_video
from editing.replace_audio import replace_video_audio
from editing.add_captions import render_captions_on_video
from editing.add_text import add_text_to_video
from editing.add_image import overlay_image_on_video
from editing.freeze_frame import freeze_frame
from editing.adjustments import adjust_video_properties
from editing.transitions import apply_transition
from editing.advanced_effects import apply_advanced_effect
from ui.preview_window import PreviewWindow
from ui.effects_panel import EffectsPanel
from ui.timeline_widget import TimelineWidget
from ui.color_grading_panel import ColorGradingPanel
from ui.smart_timeline_manager import SmartTimelineManager

class EditPage(QWidget):
    def __init__(self, parent=None, video_player=None):
        super().__init__(parent)
        self.video_player = video_player

        # Enable drag and drop
        self.setAcceptDrops(True)

        # Create main layout with Premier Pro-style arrangement
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # Top section: Source/Program monitors and effects
        top_splitter = QSplitter(Qt.Horizontal)

        # Bottom section: Timeline
        bottom_section = QWidget()
        bottom_layout = QVBoxLayout(bottom_section)
        bottom_layout.setContentsMargins(0, 0, 0, 0)

        # Create timeline
        self.timeline = TimelineWidget()
        self.timeline.setMinimumHeight(200)
        bottom_layout.addWidget(self.timeline)

        # Main splitter for top and bottom
        self.main_splitter = QSplitter(Qt.Vertical)
        self.main_splitter.addWidget(top_splitter)
        self.main_splitter.addWidget(bottom_section)
        self.main_splitter.setSizes([600, 200])  # 75% top, 25% bottom

        main_layout.addWidget(self.main_splitter)

        # Left panel: Project browser and effects
        left_panel = QWidget()
        left_panel.setMinimumWidth(300)
        left_panel.setMaximumWidth(450)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(8)

        # Create tabbed interface for left panel
        left_tabs = QTabWidget()

        # Project tab
        project_tab = QWidget()
        project_layout = QVBoxLayout(project_tab)

        # Media browser
        media_browser_label = QLabel("Media Browser")
        media_browser_label.setStyleSheet("font-weight: bold; color: #007ACC; font-size: 14px;")
        project_layout.addWidget(media_browser_label)

        # File list widget
        self.media_list = QListWidget()
        self.media_list.setStyleSheet("""
            QListWidget {
                background-color: #2b2b2b;
                border: 1px solid #555;
                border-radius: 5px;
                color: #cccccc;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #444;
            }
            QListWidget::item:selected {
                background-color: #007ACC;
            }
        """)
        project_layout.addWidget(self.media_list)

        # Import button
        import_btn = QPushButton("Import Media")
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #007ACC;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
        """)
        import_btn.clicked.connect(self.import_media)
        project_layout.addWidget(import_btn)

        left_tabs.addTab(project_tab, "Project")

        # Effects tab
        effects_tab = QWidget()
        effects_layout = QVBoxLayout(effects_tab)

        # Effects panel
        self.effects_panel = EffectsPanel()
        effects_layout.addWidget(self.effects_panel)

        left_tabs.addTab(effects_tab, "Effects")

        # Color grading tab
        color_tab = QWidget()
        color_layout = QVBoxLayout(color_tab)

        # Color grading panel
        self.color_grading_panel = ColorGradingPanel()
        color_layout.addWidget(self.color_grading_panel)

        left_tabs.addTab(color_tab, "Color")

        # Timeline Manager tab
        timeline_tab = QWidget()
        timeline_layout = QVBoxLayout(timeline_tab)

        # Smart timeline manager
        self.timeline_manager = SmartTimelineManager()
        timeline_layout.addWidget(self.timeline_manager)

        left_tabs.addTab(timeline_tab, "Timeline")

        # Add tabs to left layout
        left_layout.addWidget(left_tabs)

        # Style the left panel
        left_panel.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                border-radius: 8px;
            }
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #1e1e1e;
            }
            QTabBar::tab {
                background-color: #404040;
                color: #cccccc;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #007ACC;
                color: white;
            }
        """)

        # Center panel: Source and Program monitors
        center_panel = QWidget()
        center_layout = QHBoxLayout(center_panel)
        center_layout.setContentsMargins(5, 5, 5, 5)

        # Source monitor
        source_group = QGroupBox("Source Monitor")
        source_layout = QVBoxLayout(source_group)

        self.source_preview = PreviewWindow()
        self.source_preview.setMinimumSize(320, 240)
        source_layout.addWidget(self.source_preview)

        # Source controls
        source_controls = QHBoxLayout()
        self.source_play_btn = QPushButton("▶")
        self.source_pause_btn = QPushButton("⏸")
        self.source_stop_btn = QPushButton("⏹")

        for btn in [self.source_play_btn, self.source_pause_btn, self.source_stop_btn]:
            btn.setFixedSize(40, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    color: white;
                    border: 1px solid #555;
                    border-radius: 3px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #007ACC;
                }
            """)

        source_controls.addWidget(self.source_play_btn)
        source_controls.addWidget(self.source_pause_btn)
        source_controls.addWidget(self.source_stop_btn)
        source_controls.addStretch()
        source_layout.addLayout(source_controls)

        # Program monitor
        program_group = QGroupBox("Program Monitor")
        program_layout = QVBoxLayout(program_group)

        self.program_preview = PreviewWindow()
        self.program_preview.setMinimumSize(320, 240)
        program_layout.addWidget(self.program_preview)

        # Program controls
        program_controls = QHBoxLayout()
        self.program_play_btn = QPushButton("▶")
        self.program_pause_btn = QPushButton("⏸")
        self.program_stop_btn = QPushButton("⏹")

        for btn in [self.program_play_btn, self.program_pause_btn, self.program_stop_btn]:
            btn.setFixedSize(40, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    color: white;
                    border: 1px solid #555;
                    border-radius: 3px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #007ACC;
                }
            """)

        program_controls.addWidget(self.program_play_btn)
        program_controls.addWidget(self.program_pause_btn)
        program_controls.addWidget(self.program_stop_btn)
        program_controls.addStretch()
        program_layout.addLayout(program_controls)

        center_layout.addWidget(source_group)
        center_layout.addWidget(program_group)
        # Add panels to top splitter
        top_splitter.addWidget(left_panel)
        top_splitter.addWidget(center_panel)

        # Set splitter proportions (25% left, 75% center)
        top_splitter.setSizes([300, 900])
        top_splitter.setStretchFactor(0, 0)  # Left panel fixed
        top_splitter.setStretchFactor(1, 1)  # Center panel stretches

        # Timeline controls
        timeline_controls = QWidget()
        timeline_controls_layout = QHBoxLayout(timeline_controls)
        timeline_controls_layout.setContentsMargins(10, 5, 10, 5)

        # Transport controls
        self.play_btn = QPushButton("▶")
        self.pause_btn = QPushButton("⏸")
        self.stop_btn = QPushButton("⏹")
        self.rewind_btn = QPushButton("⏪")
        self.forward_btn = QPushButton("⏩")

        transport_buttons = [self.rewind_btn, self.play_btn, self.pause_btn, self.stop_btn, self.forward_btn]
        for btn in transport_buttons:
            btn.setFixedSize(40, 30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    color: white;
                    border: 1px solid #555;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #007ACC;
                }
            """)

        # Time display
        self.time_display = QLabel("00:00:00 / 00:00:00")
        self.time_display.setStyleSheet("color: #cccccc; font-family: monospace; font-size: 14px;")

        # Zoom controls
        zoom_out_btn = QPushButton("−")
        zoom_in_btn = QPushButton("+")
        zoom_fit_btn = QPushButton("Fit")

        for btn in [zoom_out_btn, zoom_in_btn, zoom_fit_btn]:
            btn.setFixedSize(30, 25)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #555;
                    color: white;
                    border: 1px solid #777;
                    border-radius: 2px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #777;
                }
            """)

        # Add to layout
        timeline_controls_layout.addWidget(self.rewind_btn)
        timeline_controls_layout.addWidget(self.play_btn)
        timeline_controls_layout.addWidget(self.pause_btn)
        timeline_controls_layout.addWidget(self.stop_btn)
        timeline_controls_layout.addWidget(self.forward_btn)
        timeline_controls_layout.addWidget(self.time_display)
        timeline_controls_layout.addStretch()
        timeline_controls_layout.addWidget(QLabel("Zoom:"))
        timeline_controls_layout.addWidget(zoom_out_btn)
        timeline_controls_layout.addWidget(zoom_in_btn)
        timeline_controls_layout.addWidget(zoom_fit_btn)

        bottom_layout.insertWidget(0, timeline_controls)

        # Connect signals
        self.play_btn.clicked.connect(self.play_timeline)
        self.pause_btn.clicked.connect(self.pause_timeline)
        self.stop_btn.clicked.connect(self.stop_timeline)
        self.rewind_btn.clicked.connect(self.rewind_timeline)
        self.forward_btn.clicked.connect(self.forward_timeline)

        # Source monitor controls
        self.source_play_btn.clicked.connect(self.source_preview.start_playback)
        self.source_pause_btn.clicked.connect(self.source_preview.pause_playback)
        self.source_stop_btn.clicked.connect(self.source_preview.stop_playback)

        # Program monitor controls
        self.program_play_btn.clicked.connect(self.program_preview.start_playback)
        self.program_pause_btn.clicked.connect(self.program_preview.pause_playback)
        self.program_stop_btn.clicked.connect(self.program_preview.stop_playback)

        # Timeline signals
        self.timeline.playhead_moved.connect(self.on_playhead_moved)
        self.timeline.clip_selected.connect(self.on_clip_selected)

        # Effects panel signals
        self.effects_panel.effect_applied.connect(self.apply_effect)

        # Color grading signals
        self.color_grading_panel.color_adjustment_changed.connect(self.apply_color_grading)

        # Timeline manager signals
        self.timeline_manager.clip_added_to_track.connect(self.add_clip_to_timeline_widget)

        # Zoom controls
        zoom_out_btn.clicked.connect(lambda: self.timeline.zoom_timeline(0.8))
        zoom_in_btn.clicked.connect(lambda: self.timeline.zoom_timeline(1.25))
        zoom_fit_btn.clicked.connect(self.fit_timeline)

        # Media list double-click
        self.media_list.itemDoubleClicked.connect(self.load_media_to_source)

        self.current_video_path = None
        self.imported_media = []

    def import_media(self):
        """Import media files to project"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Import Media Files",
            "",
            "Media Files (*.mp4 *.avi *.mov *.mkv *.mp3 *.wav *.aac *.png *.jpg *.jpeg)"
        )

        for file_path in files:
            if file_path not in self.imported_media:
                self.imported_media.append(file_path)
                self.add_media_item_with_thumbnail(file_path)

    def load_media_to_source(self, item):
        """Load selected media to source monitor"""
        file_path = item.data(Qt.UserRole)
        if file_path:
            self.source_preview.load(file_path)
            self.current_video_path = file_path

    def play_timeline(self):
        """Play timeline"""
        self.program_preview.start_playback()

    def pause_timeline(self):
        """Pause timeline"""
        self.program_preview.pause_playback()

    def stop_timeline(self):
        """Stop timeline"""
        self.program_preview.stop_playback()
        self.timeline.set_playhead_position(0)

    def rewind_timeline(self):
        """Rewind timeline by 5 seconds"""
        current_time = self.timeline.current_time
        new_time = max(0, current_time - 5)
        self.timeline.set_playhead_position(new_time)

    def forward_timeline(self):
        """Forward timeline by 5 seconds"""
        current_time = self.timeline.current_time
        new_time = min(self.timeline.timeline_duration, current_time + 5)
        self.timeline.set_playhead_position(new_time)

    def fit_timeline(self):
        """Fit timeline to window"""
        # Reset zoom to fit all content
        self.timeline.pixels_per_second = 20
        self.timeline.setup_timeline()

    def on_playhead_moved(self, time_seconds):
        """Handle playhead movement"""
        minutes = int(time_seconds // 60)
        seconds = int(time_seconds % 60)
        total_minutes = int(self.timeline.timeline_duration // 60)
        total_seconds = int(self.timeline.timeline_duration % 60)

        time_str = f"{minutes:02d}:{seconds:02d} / {total_minutes:02d}:{total_seconds:02d}"
        self.time_display.setText(time_str)

        # Update program monitor
        clips = self.timeline.get_clips_at_time(time_seconds)
        if clips:
            # For simplicity, show the first video clip found
            for clip in clips:
                if clip.clip_type == "video":
                    # In a real implementation, you would seek to the correct frame
                    break

    def on_clip_selected(self, clip):
        """Handle clip selection"""
        print(f"Selected clip: {clip.name} at {clip.start_time}s")

    def apply_color_grading(self, settings):
        """Apply color grading to selected clip"""
        print(f"Applying color grading: {settings}")
        # In a real implementation, you would apply these settings to the video

    def add_clip_to_timeline(self, file_path, track_index=0):
        """Add a clip to the timeline"""
        if not os.path.exists(file_path):
            return

        # Determine clip type
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.mp4', '.avi', '.mov', '.mkv', '.webm']:
            clip_type = "video"
        elif ext in ['.mp3', '.wav', '.aac', '.m4a']:
            clip_type = "audio"
        else:
            clip_type = "other"

        # Get duration (simplified - in real implementation use proper media info)
        try:
            import cv2
            if clip_type == "video":
                cap = cv2.VideoCapture(file_path)
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = frame_count / fps if fps > 0 else 10  # fallback
                cap.release()
            else:
                duration = 10  # Default duration for audio/other
        except:
            duration = 10  # Fallback duration

        # Add to timeline at current playhead position
        start_time = self.timeline.current_time
        clip = self.timeline.add_clip(file_path, start_time, duration, track_index, clip_type)

        # Load to program monitor if it's a video
        if clip_type == "video":
            self.program_preview.load(file_path)

    def dragEnterEvent(self, event):
        """Handle drag enter events"""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        """Handle drop events"""
        files = [u.toLocalFile() for u in event.mimeData().urls()]
        for file_path in files:
            if os.path.exists(file_path):
                # Add to media browser
                if file_path not in self.imported_media:
                    self.imported_media.append(file_path)
                    self.add_media_item_with_thumbnail(file_path)

                # If dropped on timeline area, add to timeline
                drop_pos = event.pos()
                timeline_rect = self.timeline.geometry()
                if timeline_rect.contains(drop_pos):
                    self.add_clip_to_timeline(file_path)

        event.accept()

    def export_project(self):
        """Export the entire project"""
        output_path, _ = QFileDialog.getSaveFileName(
            self, "Export Project", "", "Video Files (*.mp4)"
        )

        if output_path:
            # In a real implementation, you would render all timeline clips
            QMessageBox.information(self, "Export", f"Project would be exported to: {output_path}")

    def create_sequence(self):
        """Create a new sequence"""
        self.timeline.clear_timeline()
        QMessageBox.information(self, "New Sequence", "New sequence created")
    
    def apply_effect(self, effect_type, parameters):
        """Apply the selected effect to the video"""
        if not self.current_video_path:
            QMessageBox.warning(self, "No Video", "Please load a video first.")
            return
            
        try:
            if effect_type == "text":
                out = add_text_to_video(
                    self.current_video_path, 
                    parameters['text'], 
                    parameters['position'],
                    parameters['color'],
                    parameters['size'],
                    parameters['start_time'],
                    parameters['duration']
                )
            elif effect_type == "image":
                out = overlay_image_on_video(
                    self.current_video_path,
                    parameters['image_path'],
                    parameters['position'],
                    parameters['size'],
                    parameters['start_time'],
                    parameters['duration']
                )
            elif effect_type == "freeze":
                out = freeze_frame(
                    self.current_video_path,
                    parameters['freeze_time'],
                    parameters['duration']
                )
            elif effect_type == "adjustment":
                out = adjust_video_properties(
                    self.current_video_path,
                    parameters['brightness'],
                    parameters['contrast'],
                    parameters['saturation']
                )
            elif effect_type == "transition":
                # For transitions, we need a second video
                second_video, _ = QFileDialog.getOpenFileName(
                    self, "Select Second Video", "", "Video Files (*.mp4 *.avi *.mov *.mkv)"
                )
                if second_video:
                    out = apply_transition(
                        self.current_video_path,
                        second_video,
                        parameters['transition_type'],
                        parameters['duration']
                    )
            elif effect_type == "advanced":
                out = apply_advanced_effect(
                    self.current_video_path,
                    parameters['effect_type'],
                    parameters['intensity']
                )
            
            self.load_video_preview(out)
            QMessageBox.information(self, "Effect Applied", f"Saved: {out}")
            
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to apply effect: {str(e)}")

    def add_clip_to_timeline_widget(self, file_path, track_index, start_time, duration):
        """Add a clip to the timeline widget from the smart timeline manager"""
        try:
            # Determine clip type based on file extension
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
            audio_extensions = ['.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a']

            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext in video_extensions:
                clip_type = "video"
            elif file_ext in audio_extensions:
                clip_type = "audio"
            else:
                clip_type = "video"  # Default to video

            # Add clip to timeline widget
            clip = self.timeline.add_clip(file_path, start_time, duration, track_index, clip_type)

            if clip:
                logger.info(f"Added clip to timeline: {os.path.basename(file_path)} on track {track_index + 1}")
            else:
                logger.warning(f"Failed to add clip to timeline: {file_path}")

        except Exception as e:
            logger.error(f"Error adding clip to timeline widget: {e}")
            QMessageBox.warning(self, "Timeline Error", f"Failed to add clip to timeline: {str(e)}")

    def load_video(self, video_path):
        """Load a video from external source (e.g., download page)"""
        if os.path.exists(video_path):
            # Add to media browser
            if video_path not in self.imported_media:
                self.imported_media.append(video_path)
                self.add_media_item_with_thumbnail(video_path)

            # Load to source monitor
            self.source_preview.load(video_path)
            self.current_video_path = video_path

            # Use smart timeline manager to add to timeline
            self.timeline_manager.add_clip_smart(video_path, os.path.basename(video_path))

    def add_media_item_with_thumbnail(self, file_path):
        """Add media item to list with thumbnail generation for videos"""
        try:
            # Create list item with filename
            item = QListWidgetItem()
            filename = os.path.basename(file_path)

            # Check if it's a video file
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext in video_extensions:
                # Generate thumbnail for video
                thumbnail_path = self.generate_video_thumbnail(file_path)
                if thumbnail_path and os.path.exists(thumbnail_path):
                    # Load thumbnail as icon
                    pixmap = QPixmap(thumbnail_path)
                    if not pixmap.isNull():
                        # Scale thumbnail to appropriate size
                        scaled_pixmap = pixmap.scaled(64, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        icon = QIcon(scaled_pixmap)
                        item.setIcon(icon)
                        item.setText(f"🎬 {filename}")
                    else:
                        item.setText(f"🎬 {filename}")
                else:
                    item.setText(f"🎬 {filename}")
            else:
                # Non-video files
                if file_ext in ['.mp3', '.wav', '.aac', '.flac']:
                    item.setText(f"🎵 {filename}")
                elif file_ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
                    item.setText(f"🖼️ {filename}")
                else:
                    item.setText(f"📄 {filename}")

            # Store file path in item data
            item.setData(Qt.UserRole, file_path)

            # Add to media list
            self.media_list.addItem(item)

        except Exception as e:
            print(f"Error adding media item: {e}")
            # Fallback to simple text item
            item = QListWidgetItem(os.path.basename(file_path))
            item.setData(Qt.UserRole, file_path)
            self.media_list.addItem(item)

    def generate_video_thumbnail(self, video_path):
        """Generate thumbnail for video file using FFmpeg"""
        try:
            # Create thumbnail filename
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            thumbnail_dir = os.path.join(tempfile.gettempdir(), "bish_thumbnails")
            os.makedirs(thumbnail_dir, exist_ok=True)
            thumbnail_path = os.path.join(thumbnail_dir, f"{base_name}_thumb.jpg")

            # Skip if thumbnail already exists
            if os.path.exists(thumbnail_path):
                return thumbnail_path

            # Try multiple methods for thumbnail generation

            # Method 1: Try FFmpeg
            if self._try_ffmpeg_thumbnail(video_path, thumbnail_path):
                return thumbnail_path

            # Method 2: Try MoviePy as fallback
            if self._try_moviepy_thumbnail(video_path, thumbnail_path):
                return thumbnail_path

            print("⚠️ All thumbnail generation methods failed")
            return None

        except Exception as e:
            print(f"⚠️ Error generating thumbnail: {e}")
            return None

    def _try_ffmpeg_thumbnail(self, video_path, thumbnail_path):
        """Try generating thumbnail with FFmpeg"""
        try:
            # Use FFmpeg to generate thumbnail
            cmd = [
                "ffmpeg",
                "-i", video_path,
                "-ss", "00:00:01",  # Take frame at 1 second
                "-vframes", "1",    # Extract 1 frame
                "-vf", "scale=320:240:force_original_aspect_ratio=decrease",  # Scale to reasonable size
                "-y",               # Overwrite if exists
                thumbnail_path
            ]

            # Run FFmpeg command
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and os.path.exists(thumbnail_path):
                print(f"✅ Generated thumbnail with FFmpeg: {thumbnail_path}")
                return True
            else:
                print(f"⚠️ FFmpeg failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("⚠️ FFmpeg thumbnail generation timed out")
            return False
        except FileNotFoundError:
            print("⚠️ FFmpeg not found")
            return False
        except Exception as e:
            print(f"⚠️ FFmpeg error: {e}")
            return False

    def _try_moviepy_thumbnail(self, video_path, thumbnail_path):
        """Try generating thumbnail with MoviePy"""
        try:
            import moviepy.editor as mp
            from PIL import Image

            # Load video and extract frame
            clip = mp.VideoFileClip(video_path)

            # Get frame at 1 second (or 10% of duration if video is shorter)
            duration = clip.duration
            timestamp = min(1.0, duration * 0.1) if duration > 0 else 0

            frame = clip.get_frame(timestamp)
            clip.close()

            # Convert to PIL Image and save
            pil_image = Image.fromarray(frame)
            pil_image = pil_image.resize((320, 240), Image.LANCZOS)
            pil_image.save(thumbnail_path, "JPEG", quality=85)

            if os.path.exists(thumbnail_path):
                print(f"✅ Generated thumbnail with MoviePy: {thumbnail_path}")
                return True
            else:
                return False

        except ImportError:
            print("⚠️ MoviePy not available for thumbnail generation")
            return False
        except Exception as e:
            print(f"⚠️ MoviePy thumbnail error: {e}")
            return False
