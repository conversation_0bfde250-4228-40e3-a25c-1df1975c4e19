# -*- coding: utf-8 -*-
"""Centralized imports so other modules can simply `from core.libraries import *`."""
import sys, os, pathlib, json, time, threading, subprocess, shutil, re
from typing import List, Optional, Dict, Tuple, Union, Any, Callable

# Import Qt compatibility layer
try:
    from core.qt_compat import *
    print("Qt compatibility layer imported successfully")
except ImportError as e:
    print(f"Failed to import Qt compatibility layer: {e}")
    # Fallback to direct imports
    try:
        from PyQt5.QtWidgets import *
        from PyQt5.QtCore import *
        from PyQt5.QtGui import *
        print("Using PyQt5 directly")
    except ImportError:
        try:
            from PySide6.QtWidgets import *
            from PySide6.QtCore import *
            from PySide6.QtGui import *
            print("Using PySide6 directly")
        except ImportError:
            print("ERROR: No Qt framework available!")
            sys.exit(1)

# Additional utility imports
try:
    from pathlib import Path
except ImportError:
    Path = None

try:
    import logging
    logging.basicConfig(level=logging.INFO)
except ImportError:
    logging = None

# Add missing standard library imports
try:
    import hashlib
    import tempfile
    import shutil
    import subprocess
except ImportError as e:
    print(f"Standard library import error: {e}")

# GUI (PyQt5)
try:
    from PyQt5.QtWidgets import (
        QApplication, QWidget, QMainWindow, QVBoxLayout, QHBoxLayout, QGridLayout,
        QPushButton, QLabel, QLineEdit, QTextEdit, QListWidget, QListWidgetItem,
        QFileDialog, QComboBox, QSlider, QStackedWidget, QMessageBox, QStyle, QToolButton,
        QAction, QMenu, QProgressBar, QDialog, QToolBar, QStatusBar, QDockWidget,
        QSplitter, QFrame, QScrollArea, QGroupBox, QCheckBox, QRadioButton, QSpinBox,
        QDoubleSpinBox, QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView,
        QTreeWidget, QTreeWidgetItem, QAbstractItemView, QSizePolicy, QSpacerItem
    )
    from PyQt5.QtCore import Qt, QUrl, QTimer, QSize, QThread, pyqtSignal, QDateTime, QPoint, QRect
    from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent, QMediaPlaylist
    from PyQt5.QtMultimediaWidgets import QVideoWidget
    from PyQt5.QtGui import (
        QIcon, QPixmap, QFont, QColor, QPalette, QBrush, QLinearGradient,
        QRadialGradient, QConicalGradient, QPainter, QPen, QImage, QMovie,
        QKeySequence, QDesktopServices, QCursor, QMouseEvent, QWheelEvent,
        QKeyEvent, QFocusEvent, QResizeEvent, QMoveEvent, QCloseEvent
    )
    from PyQt5.Qt import QStyleFactory
except Exception as e:
    print("PyQt5 not available in this environment:", e)
    # Create dummy classes for IDE autocomplete
    class QDockWidget: pass
    class QSplitter: pass
    class pyqtSignal: pass
    # Add other dummy classes as needed

# Media
try:
    import moviepy.editor as mp
    from moviepy.video.io.VideoFileClip import VideoFileClip
    from moviepy.video.VideoClip import VideoClip
    from moviepy.audio.io.AudioFileClip import AudioFileClip
except Exception as e:
    print("moviepy not available:", e)
    mp = None
    VideoFileClip = None
    VideoClip = None
    AudioFileClip = None

# Downloading
try:
    import yt_dlp
    from yt_dlp import YoutubeDL
    from yt_dlp.utils import DownloadError, ExtractorError, GeoRestrictedError
except Exception as e:
    print("yt-dlp not available:", e)
    yt_dlp = None
    YoutubeDL = None
    DownloadError = Exception
    ExtractorError = Exception
    GeoRestrictedError = Exception

# Speech-to-text (optional at runtime; you can package a tiny model)
try:
    import whisper
    from whisper import load_model, transcribe
    from whisper.tokenizer import LANGUAGES
except Exception as e:
    whisper = None
    load_model = None
    transcribe = None
    LANGUAGES = {}
    print("whisper not available:", e)

# Optional translate helper
try:
    from deep_translator import GoogleTranslator
    from deep_translator.exceptions import TranslationNotFound, RequestError
except Exception as e:
    GoogleTranslator = None
    TranslationNotFound = Exception
    RequestError = Exception
    print("deep_translator not available:", e)

# File handling and utilities
try:
    import requests
    from requests.exceptions import RequestException, ConnectionError, Timeout
except Exception as e:
    print("requests not available:", e)
    requests = None
    RequestException = Exception
    ConnectionError = Exception
    Timeout = Exception

try:
    import numpy as np
except Exception as e:
    print("numpy not available:", e)
    np = None

try:
    import cv2
except Exception as e:
    print("OpenCV not available:", e)
    cv2 = None

# Audio processing
try:
    import pydub
    from pydub import AudioSegment
    from pydub.effects import normalize, compress_dynamic_range
except Exception as e:
    print("pydub not available:", e)
    pydub = None
    AudioSegment = None
    normalize = None
    compress_dynamic_range = None

# Enhanced ffmpeg detection with better error handling
def ensure_ffmpeg():
    """Try to locate ffmpeg on system path with better error reporting."""
    for cmd in ["ffmpeg", "ffmpeg.exe"]:
        path = shutil.which(cmd)
        if path:
            print(f"FFmpeg found at: {path}")
            return path
    
    print("Warning: FFmpeg not found on system path. Some functionality may be limited.")
    return None

# Utility functions for error handling
def handle_import_error(module_name, exception):
    """Handle import errors gracefully."""
    print(f"Warning: {module_name} not available: {exception}")
    return None

# Check for essential dependencies at import time
ESSENTIAL_DEPS = {
    'PyQt5': 'GUI functionality',
    'moviepy': 'Video editing and processing',
    'yt_dlp': 'Video downloading',
}

def check_dependencies():
    """Check if essential dependencies are available."""
    missing_deps = []
    for dep, purpose in ESSENTIAL_DEPS.items():
        try:
            # Special handling for PyQt5 which has different import name
            if dep == 'PyQt5':
                __import__('PyQt5.QtCore')
            else:
                __import__(dep.lower().replace('-', '_'))
        except ImportError:
            missing_deps.append((dep, purpose))
    
    if missing_deps:
        print("Warning: Missing essential dependencies:")
        for dep, purpose in missing_deps:
            print(f"  - {dep}: Required for {purpose}")
    
    # Additional dependency check from the missing code
    missing = []
    if yt_dlp is None:
        missing.append("yt-dlp")
    if cv2 is None:
        missing.append("opencv-python")
    if pydub is None:
        missing.append("pydub")
    if whisper is None:
        missing.append("openai-whisper")
    
    if missing:
        print("Additional missing dependencies:")
        for dep in missing:
            print(f"  - {dep}")
        missing_deps.extend([(dep, "various functionality") for dep in missing])
    
    return len(missing_deps) == 0

# Run dependency check when module is imported
check_dependencies()

# Create dummy classes for missing modules to prevent import errors
if whisper is None:
    class WhisperModel:
        def transcribe(self, *args, **kwargs):
            raise ImportError("whisper not available")
    
    whisper = type('whisper', (), {'load_model': lambda *args, **kwargs: WhisperModel()})()

if GoogleTranslator is None:
    class DummyTranslator:
        def translate(self, *args, **kwargs):
            raise ImportError("deep_translator not available")
    
    GoogleTranslator = DummyTranslator
