# -*- coding: utf-8 -*-
# Audio mixing and editing functionality

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import os

@dataclass
class AudioTrack:
    path: str
    volume: float = 1.0
    start_time: float = 0
    duration: Optional[float] = None
    fade_in: float = 0
    fade_out: float = 0

def extract_audio_from_video(
    video_path: str,
    output_path: Optional[str] = None,
    format: str = "mp3"
) -> Dict[str, Any]:
    """
    Extract audio from a video file.
    
    Args:
        video_path: Path to the input video
        output_path: Path for the output audio file (optional)
        format: Output audio format (mp3, wav, aac, etc.)
        
    Returns:
        Dictionary with operation status and output file information
    """
    try:
        from utils.ffmpeg_wrapper import ffmpeg_extract_audio
        from utils.helpers import generate_unique_filename
        
        if output_path is None:
            output_path = generate_unique_filename('audio', format)
        
        result = ffmpeg_extract_audio(video_path, output_path, format)
        
        if result["success"]:
            return {
                "success": True,
                "output_path": output_path,
                "format": format,
                "source_video": video_path
            }
        else:
            return result
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to extract audio: {str(e)}",
            "video_path": video_path
        }

def replace_audio_in_video(
    video_path: str,
    audio_path: str,
    output_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    Replace the audio track in a video.
    
    Args:
        video_path: Path to the input video
        audio_path: Path to the new audio file
        output_path: Path for the output video (optional)
        
    Returns:
        Dictionary with operation status and output file information
    """
    try:
        from utils.ffmpeg_wrapper import ffmpeg_replace_audio
        from utils.helpers import generate_unique_filename
        
        if output_path is None:
            output_path = generate_unique_filename('video_new_audio', 'mp4')
        
        result = ffmpeg_replace_audio(video_path, audio_path, output_path)
        
        if result["success"]:
            return {
                "success": True,
                "output_path": output_path,
                "audio_source": audio_path,
                "video_source": video_path
            }
        else:
            return result
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to replace audio: {str(e)}",
            "video_path": video_path,
            "audio_path": audio_path
        }

def mix_audio_tracks(
    audio_tracks: List[AudioTrack],
    output_path: Optional[str] = None,
    format: str = "mp3"
) -> Dict[str, Any]:
    """
    Mix multiple audio tracks together.
    
    Args:
        audio_tracks: List of audio tracks to mix
        output_path: Path for the output audio file (optional)
        format: Output audio format
        
    Returns:
        Dictionary with operation status and output file information
    """
    try:
        from utils.ffmpeg_wrapper import ffmpeg_mix_audio
        from utils.helpers import generate_unique_filename
        
        if output_path is None:
            output_path = generate_unique_filename('mixed_audio', format)
        
        # Convert AudioTrack objects to dictionaries for FFmpeg
        track_configs = []
        for track in audio_tracks:
            track_configs.append({
                "path": track.path,
                "volume": track.volume,
                "start_time": track.start_time,
                "duration": track.duration,
                "fade_in": track.fade_in,
                "fade_out": track.fade_out
            })
        
        result = ffmpeg_mix_audio(track_configs, output_path, format)
        
        if result["success"]:
            return {
                "success": True,
                "output_path": output_path,
                "format": format,
                "mixed_tracks": len(audio_tracks)
            }
        else:
            return result
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to mix audio tracks: {str(e)}",
            "track_count": len(audio_tracks)
        }

def add_audio_to_video(
    video_path: str,
    audio_path: str,
    output_path: Optional[str] = None,
    audio_volume: float = 1.0,
    original_audio_volume: float = 0.5
) -> Dict[str, Any]:
    """
    Add additional audio to a video while keeping the original audio.
    
    Args:
        video_path: Path to the input video
        audio_path: Path to the additional audio file
        output_path: Path for the output video (optional)
        audio_volume: Volume of the additional audio (0.0 to 1.0)
        original_audio_volume: Volume of the original audio (0.0 to 1.0)
        
    Returns:
        Dictionary with operation status and output file information
    """
    try:
        from utils.ffmpeg_wrapper import ffmpeg_add_audio
        from utils.helpers import generate_unique_filename
        
        if output_path is None:
            output_path = generate_unique_filename('video_with_extra_audio', 'mp4')
        
        result = ffmpeg_add_audio(
            video_path, 
            audio_path, 
            output_path,
            audio_volume=audio_volume,
            original_audio_volume=original_audio_volume
        )
        
        if result["success"]:
            return {
                "success": True,
                "output_path": output_path,
                "audio_volume": audio_volume,
                "original_audio_volume": original_audio_volume
            }
        else:
            return result
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to add audio to video: {str(e)}",
            "video_path": video_path,
            "audio_path": audio_path
        }