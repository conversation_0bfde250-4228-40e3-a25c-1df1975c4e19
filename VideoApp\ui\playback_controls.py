# -*- coding: utf-8 -*-
"""
Playback controls widget for video player
"""

from core.libraries import *
from core.theme import *


class PlaybackControls(QWidget):
    """Widget containing playback controls like play, pause, stop, seek, volume"""
    
    # Signals
    play_clicked = pyqtSignal()
    pause_clicked = pyqtSignal()
    stop_clicked = pyqtSignal()
    seek_requested = pyqtSignal(int)  # Position in seconds
    volume_changed = pyqtSignal(int)  # Volume 0-100
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_playing = False
        self.duration = 0
        self.position = 0
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Play/Pause button
        self.play_pause_btn = QPushButton("▶")
        self.play_pause_btn.setFixedSize(40, 40)
        self.play_pause_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 20px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: {PANEL_COLOR};
            }}
        """)
        
        # Stop button
        self.stop_btn = QPushButton("⏹")
        self.stop_btn.setFixedSize(35, 35)
        self.stop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 17px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        # Time label (current/total)
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-family: monospace;
                font-size: 12px;
                min-width: 100px;
            }}
        """)
        
        # Seek slider
        self.seek_slider = QSlider(Qt.Horizontal)
        self.seek_slider.setRange(0, 100)
        self.seek_slider.setValue(0)
        self.seek_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {PANEL_COLOR};
                height: 8px;
                background: {BACKGROUND_COLOR};
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {ACCENT_COLOR};
                border: 1px solid {PANEL_COLOR};
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::sub-page:horizontal {{
                background: {ACCENT_COLOR};
                border-radius: 4px;
            }}
        """)
        
        # Volume controls
        volume_widget = QWidget()
        volume_layout = QHBoxLayout(volume_widget)
        volume_layout.setContentsMargins(0, 0, 0, 0)
        
        # Volume icon
        self.volume_icon = QLabel("🔊")
        self.volume_icon.setFixedSize(20, 20)
        
        # Volume slider
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_slider.setFixedWidth(80)
        self.volume_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {PANEL_COLOR};
                height: 6px;
                background: {BACKGROUND_COLOR};
                margin: 2px 0;
                border-radius: 3px;
            }}
            QSlider::handle:horizontal {{
                background: {ACCENT_COLOR};
                border: 1px solid {PANEL_COLOR};
                width: 14px;
                margin: -2px 0;
                border-radius: 7px;
            }}
            QSlider::sub-page:horizontal {{
                background: {ACCENT_COLOR};
                border-radius: 3px;
            }}
        """)
        
        # Volume percentage label
        self.volume_label = QLabel("50%")
        self.volume_label.setFixedWidth(30)
        self.volume_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-size: 10px;
            }}
        """)
        
        volume_layout.addWidget(self.volume_icon)
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_label)
        
        # Add all widgets to main layout
        layout.addWidget(self.play_pause_btn)
        layout.addWidget(self.stop_btn)
        layout.addWidget(self.time_label)
        layout.addWidget(self.seek_slider, 1)  # Stretch factor 1
        layout.addWidget(volume_widget)
        
        # Apply overall styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border-top: 1px solid {ACCENT_COLOR};
            }}
        """)
    
    def connect_signals(self):
        """Connect widget signals to slots"""
        self.play_pause_btn.clicked.connect(self.toggle_play_pause)
        self.stop_btn.clicked.connect(self.stop_clicked.emit)
        self.seek_slider.sliderPressed.connect(self.on_seek_start)
        self.seek_slider.sliderReleased.connect(self.on_seek_end)
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
    
    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if self.is_playing:
            self.pause_clicked.emit()
            self.set_paused()
        else:
            self.play_clicked.emit()
            self.set_playing()
    
    def set_playing(self):
        """Update UI to show playing state"""
        self.is_playing = True
        self.play_pause_btn.setText("⏸")
    
    def set_paused(self):
        """Update UI to show paused state"""
        self.is_playing = False
        self.play_pause_btn.setText("▶")
    
    def set_stopped(self):
        """Update UI to show stopped state"""
        self.is_playing = False
        self.play_pause_btn.setText("▶")
        self.position = 0
        self.seek_slider.setValue(0)
        self.update_time_display()
    
    def set_duration(self, duration_seconds):
        """Set the total duration of the media"""
        self.duration = duration_seconds
        self.seek_slider.setRange(0, duration_seconds)
        self.update_time_display()
    
    def set_position(self, position_seconds):
        """Update the current playback position"""
        self.position = position_seconds
        if not self.seek_slider.isSliderDown():  # Don't update if user is dragging
            self.seek_slider.setValue(position_seconds)
        self.update_time_display()
    
    def update_time_display(self):
        """Update the time display label"""
        current_time = self.format_time(self.position)
        total_time = self.format_time(self.duration)
        self.time_label.setText(f"{current_time} / {total_time}")
    
    def format_time(self, seconds):
        """Format seconds as MM:SS or HH:MM:SS"""
        seconds = int(seconds)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def on_seek_start(self):
        """Handle when user starts dragging the seek slider"""
        pass  # Could pause updates here if needed
    
    def on_seek_end(self):
        """Handle when user finishes dragging the seek slider"""
        position = self.seek_slider.value()
        self.seek_requested.emit(position)
    
    def on_volume_changed(self, volume):
        """Handle volume slider changes"""
        self.volume_label.setText(f"{volume}%")
        self.volume_changed.emit(volume)
        
        # Update volume icon based on level
        if volume == 0:
            self.volume_icon.setText("🔇")
        elif volume < 30:
            self.volume_icon.setText("🔈")
        elif volume < 70:
            self.volume_icon.setText("🔉")
        else:
            self.volume_icon.setText("🔊")
    
    def set_volume(self, volume):
        """Set the volume programmatically"""
        self.volume_slider.setValue(volume)
    
    def get_volume(self):
        """Get the current volume level"""
        return self.volume_slider.value()
