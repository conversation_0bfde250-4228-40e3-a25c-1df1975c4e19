# -*- coding: utf-8 -*-
"""
Video preview window widget
"""

from core.libraries import *
from core.theme import *
import os
import cv2


class PreviewWindow(QWidget):
    """Widget for previewing videos with playback controls"""
    
    # Signals
    position_changed = pyqtSignal(float)  # Position in seconds
    duration_changed = pyqtSignal(float)  # Duration in seconds
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.video_path = None
        self.cap = None
        self.timer = QTimer()
        self.fps = 30
        self.frame_count = 0
        self.current_frame = 0
        self.duration = 0
        self.is_playing = False
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the preview window UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Video display area
        self.video_label = QLabel()
        self.video_label.setMinimumSize(640, 360)
        self.video_label.setStyleSheet(f"""
            QLabel {{
                background-color: {BACKGROUND_COLOR};
                border: 2px solid {PANEL_COLOR};
                border-radius: 8px;
                color: {TEXT_COLOR};
                font-size: 14px;
            }}
        """)
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("No video loaded")
        self.video_label.setScaledContents(True)
        
        # Controls
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        
        # Play/Pause button
        self.play_btn = QPushButton("▶")
        self.play_btn.setFixedSize(40, 40)
        self.play_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 20px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
            QPushButton:disabled {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
            }}
        """)
        self.play_btn.setEnabled(False)
        
        # Stop button
        self.stop_btn = QPushButton("⏹")
        self.stop_btn.setFixedSize(35, 35)
        self.stop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 17px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
            QPushButton:disabled {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                opacity: 0.5;
            }}
        """)
        self.stop_btn.setEnabled(False)
        
        # Position slider
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 100)
        self.position_slider.setValue(0)
        self.position_slider.setEnabled(False)
        self.position_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                border: 1px solid {PANEL_COLOR};
                height: 8px;
                background: {BACKGROUND_COLOR};
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {ACCENT_COLOR};
                border: 1px solid {PANEL_COLOR};
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::sub-page:horizontal {{
                background: {ACCENT_COLOR};
                border-radius: 4px;
            }}
            QSlider:disabled {{
                opacity: 0.5;
            }}
        """)
        
        # Time display
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-family: monospace;
                font-size: 12px;
                min-width: 100px;
            }}
        """)
        
        # Add controls to layout
        controls_layout.addWidget(self.play_btn)
        controls_layout.addWidget(self.stop_btn)
        controls_layout.addWidget(self.position_slider, 1)
        controls_layout.addWidget(self.time_label)
        
        # Add to main layout
        layout.addWidget(self.video_label, 1)
        layout.addWidget(controls_widget)
        
        # Apply overall styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 10px;
            }}
        """)
    
    def connect_signals(self):
        """Connect widget signals"""
        self.play_btn.clicked.connect(self.toggle_playback)
        self.stop_btn.clicked.connect(self.stop_playback)
        self.position_slider.sliderPressed.connect(self.on_seek_start)
        self.position_slider.sliderReleased.connect(self.on_seek_end)
        self.timer.timeout.connect(self.update_frame)
    
    def load(self, video_path):
        """Load a video file for preview"""
        if not os.path.exists(video_path):
            self.video_label.setText("Video file not found")
            return False
        
        try:
            # Release previous video
            if self.cap:
                self.cap.release()
            
            # Load new video
            self.cap = cv2.VideoCapture(video_path)
            if not self.cap.isOpened():
                self.video_label.setText("Failed to load video")
                return False
            
            self.video_path = video_path
            
            # Get video properties
            self.fps = self.cap.get(cv2.CAP_PROP_FPS) or 30
            self.frame_count = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.duration = self.frame_count / self.fps
            self.current_frame = 0
            
            # Update UI
            self.position_slider.setRange(0, self.frame_count - 1)
            self.position_slider.setValue(0)
            self.position_slider.setEnabled(True)
            self.play_btn.setEnabled(True)
            self.stop_btn.setEnabled(True)
            
            # Display first frame
            self.display_frame(0)
            self.update_time_display()
            
            # Emit duration signal
            self.duration_changed.emit(self.duration)
            
            return True
            
        except Exception as e:
            self.video_label.setText(f"Error loading video: {str(e)}")
            return False
    
    def display_frame(self, frame_number):
        """Display a specific frame"""
        if not self.cap:
            return
        
        try:
            # Set frame position
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # Read frame
            ret, frame = self.cap.read()
            if ret:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Convert to QImage
                h, w, ch = frame_rgb.shape
                bytes_per_line = ch * w
                qt_image = QImage(frame_rgb.data, w, h, bytes_per_line, QImage.Format_RGB888)
                
                # Scale to fit label
                pixmap = QPixmap.fromImage(qt_image)
                scaled_pixmap = pixmap.scaled(
                    self.video_label.size(), 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                
                self.video_label.setPixmap(scaled_pixmap)
                self.current_frame = frame_number
                
        except Exception as e:
            print(f"Error displaying frame: {e}")
    
    def toggle_playback(self):
        """Toggle between play and pause"""
        if self.is_playing:
            self.pause_playback()
        else:
            self.start_playback()
    
    def start_playback(self):
        """Start video playback"""
        if not self.cap:
            return
        
        self.is_playing = True
        self.play_btn.setText("⏸")
        
        # Start timer for frame updates
        interval = int(1000 / self.fps)  # milliseconds
        self.timer.start(interval)
    
    def pause_playback(self):
        """Pause video playback"""
        self.is_playing = False
        self.play_btn.setText("▶")
        self.timer.stop()
    
    def stop_playback(self):
        """Stop video playback and return to beginning"""
        self.pause_playback()
        self.seek(0)
    
    def seek(self, position):
        """Seek to a specific position (0.0 to 1.0)"""
        if not self.cap:
            return
        
        frame_number = int(position * (self.frame_count - 1))
        frame_number = max(0, min(frame_number, self.frame_count - 1))
        
        self.display_frame(frame_number)
        self.position_slider.setValue(frame_number)
        self.update_time_display()
        
        # Emit position signal
        time_seconds = frame_number / self.fps
        self.position_changed.emit(time_seconds)
    
    def update_frame(self):
        """Update to next frame during playback"""
        if not self.is_playing or not self.cap:
            return
        
        next_frame = self.current_frame + 1
        if next_frame >= self.frame_count:
            # End of video
            self.stop_playback()
            return
        
        self.display_frame(next_frame)
        self.position_slider.setValue(next_frame)
        self.update_time_display()
        
        # Emit position signal
        time_seconds = next_frame / self.fps
        self.position_changed.emit(time_seconds)
    
    def on_seek_start(self):
        """Handle when user starts seeking"""
        was_playing = self.is_playing
        if was_playing:
            self.pause_playback()
        self._was_playing_before_seek = was_playing
    
    def on_seek_end(self):
        """Handle when user finishes seeking"""
        frame_number = self.position_slider.value()
        self.display_frame(frame_number)
        self.update_time_display()
        
        # Resume playback if it was playing before
        if getattr(self, '_was_playing_before_seek', False):
            self.start_playback()
        
        # Emit position signal
        time_seconds = frame_number / self.fps
        self.position_changed.emit(time_seconds)
    
    def update_time_display(self):
        """Update the time display"""
        current_time = self.current_frame / self.fps
        total_time = self.duration
        
        current_str = self.format_time(current_time)
        total_str = self.format_time(total_time)
        
        self.time_label.setText(f"{current_str} / {total_str}")
    
    def format_time(self, seconds):
        """Format seconds as MM:SS or HH:MM:SS"""
        seconds = int(seconds)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def get_current_time(self):
        """Get current playback time in seconds"""
        return self.current_frame / self.fps if self.fps > 0 else 0
    
    def get_duration(self):
        """Get video duration in seconds"""
        return self.duration
    
    def closeEvent(self, event):
        """Clean up when widget is closed"""
        if self.cap:
            self.cap.release()
        self.timer.stop()
        super().closeEvent(event)


class PreviewDialog(QDialog):
    """Dialog for previewing videos"""
    
    def __init__(self, video_path=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Video Preview")
        self.setModal(False)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # Preview widget
        self.preview = PreviewWindow()
        layout.addWidget(self.preview)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.close)
        self.close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 5px;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # Apply dialog styling
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {BACKGROUND_COLOR};
                border: 2px solid {PANEL_COLOR};
                border-radius: 10px;
            }}
        """)
        
        # Load video if provided
        if video_path:
            self.preview.load(video_path)
    
    def load_video(self, video_path):
        """Load a video for preview"""
        return self.preview.load(video_path)


# Convenience function
def preview_video(video_path, parent=None):
    """Show video preview dialog"""
    dialog = PreviewDialog(video_path, parent)
    dialog.show()
    return dialog
