# -*- coding: utf-8 -*-
"""
Enhanced Local Video Player Widget with better codec support and error handling
"""

import os
import platform
import subprocess
import logging
from pathlib import Path
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QMessageBox
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QTimer
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtGui import QDragEnterEvent, QDropEvent

# Try to import video widget with fallback
try:
    from PyQt5.QtMultimediaWidgets import QVideoWidget
    VIDEO_WIDGET_AVAILABLE = True
except ImportError:
    print("⚠️ QVideoWidget not available - using fallback")
    QVideoWidget = None
    VIDEO_WIDGET_AVAILABLE = False

# Set up logging
logger = logging.getLogger(__name__)


class LocalVideoPlayer(QWidget):
    """Enhanced video player specifically for local video files"""
    
    # Signals
    error_occurred = pyqtSignal(str)
    playback_started = pyqtSignal()
    status_changed = pyqtSignal(str)
    duration_changed = pyqtSignal(int)
    position_changed = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_video_path = None
        self.setup_ui()
        self.setup_player()
        self.setup_drag_drop()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # Video widget
        if VIDEO_WIDGET_AVAILABLE:
            self.video_widget = QVideoWidget(self)
            self.video_widget.setStyleSheet("background-color: black;")
        else:
            self.video_widget = QLabel("Video playback not available\n(QVideoWidget missing)")
            self.video_widget.setAlignment(Qt.AlignCenter)
            self.video_widget.setStyleSheet("background-color: black; color: white; font-size: 14px;")
        
        self.layout.addWidget(self.video_widget)
        
    def setup_player(self):
        """Setup the media player"""
        self.player = QMediaPlayer(self)
        
        if VIDEO_WIDGET_AVAILABLE:
            self.player.setVideoOutput(self.video_widget)
        
        # Set default volume
        self.player.setVolume(80)
        
        # Connect signals
        self.player.error.connect(self.handle_player_error)
        self.player.stateChanged.connect(self.on_state_changed)
        self.player.durationChanged.connect(self.on_duration_changed)
        self.player.positionChanged.connect(self.on_position_changed)
        
    def setup_drag_drop(self):
        """Setup drag and drop functionality"""
        self.setAcceptDrops(True)
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        """Handle drag enter events"""
        if event.mimeData().hasUrls():
            # Check if any of the URLs are video files
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    if self._is_video_file(file_path):
                        event.acceptProposedAction()
                        return
        event.ignore()
        
    def dropEvent(self, event: QDropEvent):
        """Handle drop events"""
        for url in event.mimeData().urls():
            if url.isLocalFile():
                file_path = url.toLocalFile()
                if self._is_video_file(file_path):
                    self.load_video(file_path)
                    break
        event.acceptProposedAction()
        
    def _is_video_file(self, file_path):
        """Check if file is a supported video format"""
        video_extensions = {
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', 
            '.m4v', '.3gp', '.ogv', '.ts', '.mts', '.m2ts'
        }
        return Path(file_path).suffix.lower() in video_extensions
        
    def load_video(self, file_path):
        """Load a local video file with comprehensive validation"""
        try:
            logger.info(f"🎬 Loading local video: {file_path}")
            
            # Validate file path
            validation_result = self._validate_video_file(file_path)
            if not validation_result['valid']:
                raise ValueError(validation_result['error'])
            
            # Create media content
            media_content = QMediaContent(QUrl.fromLocalFile(file_path))
            if media_content.isNull():
                raise ValueError("Failed to create media content from file")
            
            # Set media
            self.player.setMedia(media_content)
            self.current_video_path = file_path
            
            # Update status
            filename = os.path.basename(file_path)
            self.status_changed.emit(f"📁 Loaded: {filename}")
            
            logger.info(f"✅ Successfully loaded: {filename}")
            
        except Exception as e:
            error_msg = f"Failed to load video: {str(e)}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            
    def _validate_video_file(self, file_path):
        """Comprehensive video file validation"""
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                return {'valid': False, 'error': f"File not found: {file_path}"}
            
            # Check if it's a file (not directory)
            if not os.path.isfile(file_path):
                return {'valid': False, 'error': f"Path is not a file: {file_path}"}
            
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {'valid': False, 'error': "File is empty"}
            
            # Check file extension
            if not self._is_video_file(file_path):
                return {'valid': False, 'error': "Unsupported file format"}
            
            # Check file permissions
            if not os.access(file_path, os.R_OK):
                return {'valid': False, 'error': "File is not readable - check permissions"}
            
            return {'valid': True, 'error': None}
            
        except Exception as e:
            return {'valid': False, 'error': f"Validation error: {str(e)}"}
    
    def play(self):
        """Start playback"""
        if self.current_video_path and self.player.state() != QMediaPlayer.PlayingState:
            self.player.play()
    
    def pause(self):
        """Pause playback"""
        if self.player.state() == QMediaPlayer.PlayingState:
            self.player.pause()
    
    def stop(self):
        """Stop playback"""
        self.player.stop()
    
    def seek(self, position):
        """Seek to position (in milliseconds)"""
        if self.player.isSeekable():
            self.player.setPosition(position)
    
    def set_volume(self, volume):
        """Set volume (0-100)"""
        self.player.setVolume(max(0, min(100, volume)))
    
    def get_duration(self):
        """Get video duration in milliseconds"""
        return self.player.duration()
    
    def get_position(self):
        """Get current position in milliseconds"""
        return self.player.position()
    
    def handle_player_error(self, error):
        """Handle player errors with detailed codec guidance"""
        error_messages = {
            QMediaPlayer.ResourceError: self._get_resource_error_message(),
            QMediaPlayer.FormatError: self._get_format_error_message(),
            QMediaPlayer.NetworkError: "Network error occurred",
            QMediaPlayer.AccessDeniedError: "Access denied - check file permissions",
            QMediaPlayer.ServiceMissingError: self._get_service_error_message(),
        }
        
        error_msg = error_messages.get(error, f"Media error: {self.player.errorString()}")
        logger.error(f"Player error: {error_msg}")
        self.error_occurred.emit(error_msg)
        
        # Trigger alternative playback after error
        QTimer.singleShot(1000, self._try_alternative_playback)
    
    def _get_resource_error_message(self):
        """Get detailed resource error message with solutions"""
        return (
            "Resource error - Cannot play the media file.\n\n"
            "🔧 This usually means:\n"
            "• Missing codecs for this video format\n"
            "• Corrupted video file\n"
            "• Unsupported video container\n\n"
            "💡 Solutions:\n"
            "1. Install K-Lite Codec Pack\n"
            "2. Convert video to MP4 format\n"
            "3. Try playing in VLC Media Player\n"
            "4. Check if file plays in other applications"
        )
    
    def _get_format_error_message(self):
        """Get detailed format error message"""
        return (
            "Format error - Unsupported video format.\n\n"
            "🔧 Recommended formats:\n"
            "• MP4 (H.264 + AAC)\n"
            "• WebM (VP8/VP9 + Vorbis)\n"
            "• AVI (with standard codecs)\n\n"
            "💡 Convert your video using:\n"
            "• HandBrake (free)\n"
            "• FFmpeg\n"
            "• VLC Media Player"
        )
    
    def _get_service_error_message(self):
        """Get detailed service error message"""
        return (
            "Media service error - Qt multimedia components missing.\n\n"
            "🔧 Solutions:\n"
            "• Install Windows Media Feature Pack (Windows N/KN)\n"
            "• Update your graphics drivers\n"
            "• Reinstall Qt multimedia components\n"
            "• Try using VLC as external player"
        )
    
    def _try_alternative_playback(self):
        """Try alternative playback methods when Qt multimedia fails"""
        if not self.current_video_path:
            return
            
        try:
            logger.info("🔄 Attempting alternative playback...")
            
            # Try system default player
            if platform.system() == "Windows":
                os.startfile(self.current_video_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", self.current_video_path])
            else:  # Linux
                subprocess.run(["xdg-open", self.current_video_path])
            
            filename = os.path.basename(self.current_video_path)
            self.status_changed.emit(f"🎬 Opened in system player: {filename}")
            
        except Exception as e:
            logger.error(f"Alternative playback failed: {e}")
            self.error_occurred.emit(f"All playback methods failed: {str(e)}")
    
    def on_state_changed(self, state):
        """Handle player state changes"""
        if state == QMediaPlayer.PlayingState:
            self.playback_started.emit()
            filename = os.path.basename(self.current_video_path) if self.current_video_path else "Video"
            self.status_changed.emit(f"▶️ Playing: {filename}")
        elif state == QMediaPlayer.PausedState:
            self.status_changed.emit("⏸️ Paused")
        elif state == QMediaPlayer.StoppedState:
            self.status_changed.emit("⏹️ Stopped")
    
    def on_duration_changed(self, duration):
        """Handle duration changes"""
        self.duration_changed.emit(duration)
    
    def on_position_changed(self, position):
        """Handle position changes"""
        self.position_changed.emit(position)
    
    def show_codec_installation_guide(self):
        """Show comprehensive codec installation guide"""
        guide = """
🎬 Video Playback Troubleshooting Guide

For optimal video playback, please ensure you have the necessary codecs installed:

🔧 Windows Users:
1. Install Windows Media Feature Pack (for Windows N/KN editions)
2. Install K-Lite Codec Pack Standard: https://codecguide.com/download_k-lite_codec_pack_standard.htm
3. Install VLC Media Player (includes codecs): https://www.videolan.org/

🔧 macOS Users:
1. Install VLC Media Player: https://www.videolan.org/
2. Install Perian (if available for your macOS version)

🔧 Linux Users:
1. Install restricted extras package:
   Ubuntu/Debian: sudo apt install ubuntu-restricted-extras
   Fedora: sudo dnf install gstreamer1-plugins-{bad,good,ugly}
2. Install VLC: sudo apt install vlc

💡 Tips:
• MP4 files with H.264 video and AAC audio have the best compatibility
• Convert videos using HandBrake: https://handbrake.fr/
• Keep your system and drivers updated
        """
        
        msg = QMessageBox(self)
        msg.setWindowTitle("Codec Installation Guide")
        msg.setText(guide)
        msg.setIcon(QMessageBox.Information)
        msg.exec_()
