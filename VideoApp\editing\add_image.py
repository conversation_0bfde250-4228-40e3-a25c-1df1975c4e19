# -*- coding: utf-8 -*-
"""
Add image overlays to videos
"""

from core.libraries import *
import os
import moviepy.editor as mp
from typing import Optional, Tu<PERSON>, Union


def overlay_image_on_video(
    video_path: str,
    image_path: str,
    start_time: float = 0,
    duration: Optional[float] = None,
    position: Union[str, Tuple[int, int]] = "top-right",
    size: Optional[Tuple[int, int]] = None,
    opacity: float = 1.0,
    output_path: Optional[str] = None
) -> str:
    """
    Overlay an image on a video.
    
    Args:
        video_path: Path to the input video
        image_path: Path to the image to overlay
        start_time: When to start showing the image (seconds)
        duration: How long to show the image (seconds, None for rest of video)
        position: Image position ("center", "top-left", etc. or (x, y) tuple)
        size: Image size as (width, height) tuple (None to keep original)
        opacity: Image opacity (0.0 to 1.0)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Load image
        image_clip = mp.ImageClip(image_path)
        
        # Set duration
        if duration is None:
            duration = video.duration - start_time
        image_clip = image_clip.set_duration(duration)
        
        # Resize image if size is specified
        if size:
            image_clip = image_clip.resize(size)
        
        # Set opacity
        if opacity < 1.0:
            image_clip = image_clip.set_opacity(opacity)
        
        # Set timing and position
        image_clip = image_clip.set_start(start_time)
        image_clip = set_image_position(image_clip, position, video.size)
        
        # Composite video with image
        final_video = mp.CompositeVideoClip([video, image_clip])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_with_image.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        image_clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to overlay image on video: {str(e)}")


def set_image_position(image_clip: mp.ImageClip, position: Union[str, Tuple[int, int]], video_size: Tuple[int, int]) -> mp.ImageClip:
    """Set the position of image on the video"""
    
    if isinstance(position, tuple):
        # Custom position as (x, y) coordinates
        return image_clip.set_position(position)
    
    # Predefined positions
    position_map = {
        'center': 'center',
        'top': ('center', 'top'),
        'bottom': ('center', 'bottom'),
        'left': ('left', 'center'),
        'right': ('right', 'center'),
        'top-left': ('left', 'top'),
        'top-right': ('right', 'top'),
        'bottom-left': ('left', 'bottom'),
        'bottom-right': ('right', 'bottom')
    }
    
    pos = position_map.get(position.lower(), 'center')
    return image_clip.set_position(pos)


def add_watermark_image(
    video_path: str,
    watermark_path: str,
    position: str = "bottom-right",
    size: Optional[Tuple[int, int]] = None,
    opacity: float = 0.7,
    output_path: Optional[str] = None
) -> str:
    """
    Add a watermark image to a video.
    
    Args:
        video_path: Path to the input video
        watermark_path: Path to the watermark image
        position: Watermark position
        size: Watermark size (None for auto-sizing)
        opacity: Watermark opacity
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Auto-size watermark if no size specified
        if size is None:
            # Make watermark 10% of video width
            watermark_width = int(video.w * 0.1)
            size = (watermark_width, None)  # Keep aspect ratio
        
        return overlay_image_on_video(
            video_path=video_path,
            image_path=watermark_path,
            start_time=0,
            duration=video.duration,
            position=position,
            size=size,
            opacity=opacity,
            output_path=output_path
        )
        
    except Exception as e:
        raise Exception(f"Failed to add watermark: {str(e)}")


def add_logo_overlay(
    video_path: str,
    logo_path: str,
    position: str = "top-left",
    size: Optional[Tuple[int, int]] = None,
    start_time: float = 0,
    duration: Optional[float] = None,
    output_path: Optional[str] = None
) -> str:
    """
    Add a logo overlay to a video.
    
    Args:
        video_path: Path to the input video
        logo_path: Path to the logo image
        position: Logo position
        size: Logo size (None for auto-sizing)
        start_time: When to start showing the logo
        duration: How long to show the logo
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Auto-size logo if no size specified
        if size is None:
            # Make logo 15% of video width
            logo_width = int(video.w * 0.15)
            size = (logo_width, None)  # Keep aspect ratio
        
        return overlay_image_on_video(
            video_path=video_path,
            image_path=logo_path,
            start_time=start_time,
            duration=duration,
            position=position,
            size=size,
            opacity=1.0,
            output_path=output_path
        )
        
    except Exception as e:
        raise Exception(f"Failed to add logo: {str(e)}")


def add_animated_image(
    video_path: str,
    image_path: str,
    animation: str = "fade",
    start_time: float = 0,
    duration: float = 3.0,
    position: str = "center",
    size: Optional[Tuple[int, int]] = None,
    output_path: Optional[str] = None
) -> str:
    """
    Add an animated image overlay to a video.
    
    Args:
        video_path: Path to the input video
        image_path: Path to the image to overlay
        animation: Animation type ("fade", "slide", "zoom", "bounce")
        start_time: When to start showing the image
        duration: How long to show the image
        position: Image position
        size: Image size
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Load and prepare image
        image_clip = mp.ImageClip(image_path).set_duration(duration)
        
        # Resize if specified
        if size:
            image_clip = image_clip.resize(size)
        
        # Apply animation
        image_clip = apply_image_animation(image_clip, animation)
        
        # Set timing and position
        image_clip = image_clip.set_start(start_time)
        image_clip = set_image_position(image_clip, position, video.size)
        
        # Composite video with image
        final_video = mp.CompositeVideoClip([video, image_clip])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_animated_image.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        image_clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to add animated image: {str(e)}")


def apply_image_animation(image_clip: mp.ImageClip, animation: str) -> mp.ImageClip:
    """Apply animation effects to image clip"""
    
    if animation == "fade":
        # Fade in and out
        return image_clip.fadein(0.5).fadeout(0.5)
    
    elif animation == "slide":
        # Slide in from right
        w, h = image_clip.size
        return image_clip.set_position(lambda t: (max(0, w - 100*t), 'center'))
    
    elif animation == "zoom":
        # Zoom in effect
        return image_clip.resize(lambda t: min(1, 0.3 + 0.7*t))
    
    elif animation == "bounce":
        # Bounce effect (simplified)
        return image_clip.set_position(lambda t: ('center', max(0, 50 - 50*t)))
    
    else:
        # No animation
        return image_clip


def add_multiple_images(
    video_path: str,
    image_configs: list,
    output_path: Optional[str] = None
) -> str:
    """
    Add multiple image overlays to a video.
    
    Args:
        video_path: Path to the input video
        image_configs: List of image configuration dictionaries
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Create all image clips
        image_clips = []
        for config in image_configs:
            image_clip = mp.ImageClip(config['image_path'])
            
            # Set duration
            duration = config.get('duration', 5.0)
            image_clip = image_clip.set_duration(duration)
            
            # Resize if specified
            size = config.get('size')
            if size:
                image_clip = image_clip.resize(size)
            
            # Set opacity
            opacity = config.get('opacity', 1.0)
            if opacity < 1.0:
                image_clip = image_clip.set_opacity(opacity)
            
            # Set timing and position
            start_time = config.get('start_time', 0)
            image_clip = image_clip.set_start(start_time)
            
            position = config.get('position', 'center')
            image_clip = set_image_position(image_clip, position, video.size)
            
            # Apply animation if specified
            animation = config.get('animation')
            if animation:
                image_clip = apply_image_animation(image_clip, animation)
            
            image_clips.append(image_clip)
        
        # Composite all clips
        final_video = mp.CompositeVideoClip([video] + image_clips)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_multi_images.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        for clip in image_clips:
            clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to add multiple images: {str(e)}")


def create_picture_in_picture(
    main_video_path: str,
    overlay_video_path: str,
    position: str = "top-right",
    size: Optional[Tuple[int, int]] = None,
    start_time: float = 0,
    duration: Optional[float] = None,
    output_path: Optional[str] = None
) -> str:
    """
    Create a picture-in-picture effect with two videos.
    
    Args:
        main_video_path: Path to the main video
        overlay_video_path: Path to the overlay video
        position: Position of the overlay video
        size: Size of the overlay video (None for auto-sizing)
        start_time: When to start the overlay
        duration: Duration of the overlay
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load videos
        main_video = mp.VideoFileClip(main_video_path)
        overlay_video = mp.VideoFileClip(overlay_video_path)
        
        # Auto-size overlay if no size specified
        if size is None:
            # Make overlay 25% of main video width
            overlay_width = int(main_video.w * 0.25)
            overlay_height = int(overlay_width * overlay_video.h / overlay_video.w)
            size = (overlay_width, overlay_height)
        
        # Resize overlay video
        overlay_video = overlay_video.resize(size)
        
        # Set duration
        if duration is None:
            duration = min(overlay_video.duration, main_video.duration - start_time)
        overlay_video = overlay_video.set_duration(duration)
        
        # Set timing and position
        overlay_video = overlay_video.set_start(start_time)
        overlay_video = set_image_position(overlay_video, position, main_video.size)
        
        # Composite videos
        final_video = mp.CompositeVideoClip([main_video, overlay_video])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(main_video_path)[0]
            output_path = f"{base_name}_pip.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        main_video.close()
        overlay_video.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create picture-in-picture: {str(e)}")
