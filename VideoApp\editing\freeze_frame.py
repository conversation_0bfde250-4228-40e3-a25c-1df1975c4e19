# -*- coding: utf-8 -*-
"""
Freeze frame effects for video editing
"""

from core.libraries import *
import os
import moviepy.editor as mp
from typing import Optional, <PERSON><PERSON>


def create_freeze_frame(
    video_path: str,
    freeze_time: float,
    freeze_duration: float = 2.0,
    output_path: Optional[str] = None
) -> str:
    """
    Create a freeze frame effect at a specific time in the video.
    
    Args:
        video_path: Path to the input video
        freeze_time: Time in seconds where to freeze the frame
        freeze_duration: How long to hold the freeze (seconds)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Validate freeze time
        if freeze_time >= video.duration:
            raise ValueError(f"Freeze time {freeze_time} is beyond video duration {video.duration}")
        
        # Split video into parts
        part1 = video.subclip(0, freeze_time)
        
        # Create freeze frame
        freeze_frame = video.subclip(freeze_time, freeze_time + 0.1).loop(duration=freeze_duration)
        
        # Get remaining part
        remaining_start = freeze_time
        if remaining_start < video.duration:
            part2 = video.subclip(remaining_start, video.duration)
            # Concatenate all parts
            final_video = mp.concatenate_videoclips([part1, freeze_frame, part2])
        else:
            # No remaining part
            final_video = mp.concatenate_videoclips([part1, freeze_frame])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_freeze_frame.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        part1.close()
        freeze_frame.close()
        if 'part2' in locals():
            part2.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create freeze frame: {str(e)}")


def create_slow_motion_freeze(
    video_path: str,
    start_time: float,
    end_time: float,
    slow_factor: float = 0.5,
    output_path: Optional[str] = None
) -> str:
    """
    Create a slow motion effect that gradually freezes.
    
    Args:
        video_path: Path to the input video
        start_time: Start time for slow motion effect
        end_time: End time for slow motion effect
        slow_factor: Speed factor (0.5 = half speed, 0.1 = very slow)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Validate times
        if start_time >= end_time or end_time > video.duration:
            raise ValueError("Invalid start/end times")
        
        # Split video into parts
        part1 = video.subclip(0, start_time)
        slow_part = video.subclip(start_time, end_time)
        part3 = video.subclip(end_time, video.duration)
        
        # Apply slow motion
        slow_part = slow_part.fx(mp.vfx.speedx, slow_factor)
        
        # Concatenate parts
        final_video = mp.concatenate_videoclips([part1, slow_part, part3])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_slow_freeze.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        part1.close()
        slow_part.close()
        part3.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create slow motion freeze: {str(e)}")


def create_zoom_freeze(
    video_path: str,
    freeze_time: float,
    freeze_duration: float = 2.0,
    zoom_factor: float = 1.5,
    output_path: Optional[str] = None
) -> str:
    """
    Create a freeze frame with zoom effect.
    
    Args:
        video_path: Path to the input video
        freeze_time: Time in seconds where to freeze the frame
        freeze_duration: How long to hold the freeze (seconds)
        zoom_factor: Zoom level (1.0 = no zoom, 2.0 = 2x zoom)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Validate freeze time
        if freeze_time >= video.duration:
            raise ValueError(f"Freeze time {freeze_time} is beyond video duration {video.duration}")
        
        # Split video into parts
        part1 = video.subclip(0, freeze_time)
        
        # Create freeze frame with zoom
        freeze_frame = video.subclip(freeze_time, freeze_time + 0.1)
        freeze_frame = freeze_frame.resize(zoom_factor).set_position('center')
        freeze_frame = freeze_frame.loop(duration=freeze_duration)
        
        # Get remaining part
        remaining_start = freeze_time
        if remaining_start < video.duration:
            part2 = video.subclip(remaining_start, video.duration)
            # Concatenate all parts
            final_video = mp.concatenate_videoclips([part1, freeze_frame, part2])
        else:
            # No remaining part
            final_video = mp.concatenate_videoclips([part1, freeze_frame])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_zoom_freeze.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        part1.close()
        freeze_frame.close()
        if 'part2' in locals():
            part2.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create zoom freeze: {str(e)}")


def create_multiple_freeze_frames(
    video_path: str,
    freeze_points: list,
    freeze_duration: float = 1.0,
    output_path: Optional[str] = None
) -> str:
    """
    Create multiple freeze frames at different points in the video.
    
    Args:
        video_path: Path to the input video
        freeze_points: List of time points (in seconds) where to freeze
        freeze_duration: How long to hold each freeze (seconds)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Sort freeze points
        freeze_points = sorted(freeze_points)
        
        # Validate freeze points
        for point in freeze_points:
            if point >= video.duration:
                raise ValueError(f"Freeze point {point} is beyond video duration {video.duration}")
        
        # Create video segments
        clips = []
        current_time = 0
        
        for freeze_time in freeze_points:
            # Add segment before freeze
            if freeze_time > current_time:
                segment = video.subclip(current_time, freeze_time)
                clips.append(segment)
            
            # Add freeze frame
            freeze_frame = video.subclip(freeze_time, freeze_time + 0.1).loop(duration=freeze_duration)
            clips.append(freeze_frame)
            
            current_time = freeze_time
        
        # Add remaining segment
        if current_time < video.duration:
            remaining_segment = video.subclip(current_time, video.duration)
            clips.append(remaining_segment)
        
        # Concatenate all clips
        final_video = mp.concatenate_videoclips(clips)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_multi_freeze.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        for clip in clips:
            clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create multiple freeze frames: {str(e)}")


def create_bullet_time_effect(
    video_path: str,
    start_time: float,
    end_time: float,
    freeze_duration: float = 2.0,
    output_path: Optional[str] = None
) -> str:
    """
    Create a bullet-time effect (slow motion leading to freeze).
    
    Args:
        video_path: Path to the input video
        start_time: Start time for the effect
        end_time: End time for the effect
        freeze_duration: How long to hold the final freeze
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Validate times
        if start_time >= end_time or end_time > video.duration:
            raise ValueError("Invalid start/end times")
        
        # Split video into parts
        part1 = video.subclip(0, start_time)
        effect_part = video.subclip(start_time, end_time)
        part3 = video.subclip(end_time, video.duration)
        
        # Create slow motion effect (gradually slowing down)
        slow_part = effect_part.fx(mp.vfx.speedx, 0.2)  # Very slow
        
        # Create freeze frame at the end
        freeze_frame = video.subclip(end_time - 0.1, end_time).loop(duration=freeze_duration)
        
        # Concatenate parts
        final_video = mp.concatenate_videoclips([part1, slow_part, freeze_frame, part3])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_bullet_time.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        part1.close()
        effect_part.close()
        slow_part.close()
        freeze_frame.close()
        part3.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create bullet time effect: {str(e)}")


def extract_frame_as_image(
    video_path: str,
    time: float,
    output_path: Optional[str] = None,
    format: str = "png"
) -> str:
    """
    Extract a single frame from video as an image.
    
    Args:
        video_path: Path to the input video
        time: Time in seconds to extract frame
        output_path: Output image path (optional)
        format: Image format ("png", "jpg", "bmp")
    
    Returns:
        Path to the extracted image file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Validate time
        if time >= video.duration:
            raise ValueError(f"Time {time} is beyond video duration {video.duration}")
        
        # Extract frame
        frame = video.get_frame(time)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_frame_{int(time)}s.{format}"
        
        # Save frame as image
        from PIL import Image
        img = Image.fromarray(frame)
        img.save(output_path)
        
        # Clean up
        video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to extract frame: {str(e)}")


def create_frame_sequence(
    video_path: str,
    start_time: float,
    end_time: float,
    frame_interval: float = 1.0,
    output_dir: Optional[str] = None
) -> list:
    """
    Extract a sequence of frames from video.
    
    Args:
        video_path: Path to the input video
        start_time: Start time for extraction
        end_time: End time for extraction
        frame_interval: Interval between frames (seconds)
        output_dir: Output directory for frames
    
    Returns:
        List of paths to extracted frame files
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Validate times
        if start_time >= end_time or end_time > video.duration:
            raise ValueError("Invalid start/end times")
        
        # Create output directory if not provided
        if output_dir is None:
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            output_dir = f"{base_name}_frames"
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Extract frames
        frame_paths = []
        current_time = start_time
        frame_number = 0
        
        while current_time <= end_time:
            # Extract frame
            frame = video.get_frame(current_time)
            
            # Save frame
            frame_path = os.path.join(output_dir, f"frame_{frame_number:04d}.png")
            from PIL import Image
            img = Image.fromarray(frame)
            img.save(frame_path)
            
            frame_paths.append(frame_path)
            
            # Move to next frame
            current_time += frame_interval
            frame_number += 1
        
        # Clean up
        video.close()
        
        return frame_paths
        
    except Exception as e:
        raise Exception(f"Failed to create frame sequence: {str(e)}")


# Convenience functions
def quick_freeze(video_path: str, freeze_time: float) -> str:
    """Quick freeze frame with default settings"""
    return create_freeze_frame(video_path, freeze_time, freeze_duration=2.0)


def dramatic_freeze(video_path: str, freeze_time: float) -> str:
    """Dramatic freeze with zoom effect"""
    return create_zoom_freeze(video_path, freeze_time, freeze_duration=3.0, zoom_factor=1.3)


# Compatibility function for existing imports
def freeze_frame(video_path: str, freeze_time: float, freeze_duration: float = 2.0, output_path: Optional[str] = None) -> str:
    """
    Compatibility function for existing imports.
    Creates a freeze frame effect at a specific time in the video.

    Args:
        video_path: Path to the input video
        freeze_time: Time in seconds where to freeze the frame
        freeze_duration: How long to hold the freeze (seconds)
        output_path: Output file path (optional)

    Returns:
        Path to the output video file
    """
    return create_freeze_frame(video_path, freeze_time, freeze_duration, output_path)
