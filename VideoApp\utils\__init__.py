# -*- coding: utf-8 -*-
# Utils module package initialization

from .video_downloader import download_video, list_formats, search_videos, get_trending_videos, search_channels, download_playlist
from .ffmpeg_wrapper import (
    check_ffmpeg_installed, get_ffmpeg_path, run_ffmpeg_command,
    ffmpeg_add_text, ffmpeg_add_image, ffmpeg_extract_audio,
    ffmpeg_replace_audio, ffmpeg_mix_audio, ffmpeg_add_audio,
    ffmpeg_freeze_frame, ffmpeg_slow_motion, ffmpeg_time_lapse,
    ffmpeg_render_timeline, ffmpeg_concat_videos, ffmpeg_add_multiple_texts
)
from .helpers import (
    generate_unique_filename, get_cache, set_cache,
    format_duration, format_file_size, safe_delete_file,
    create_temp_dir, cleanup_temp_dir, check_ffmpeg_availability
)
# ffmpeg_utils import commented out - file missing
# from .ffmpeg_utils import (
#     enhance_video, apply_ffmpeg_filters, extract_frames,
#     create_video_from_images, add_audio_to_video, extract_audio_ffmpeg,
#     merge_videos, change_video_speed, create_thumbnail
# )

__all__ = [
    'download_video',
    'list_formats',
    'search_videos',
    'get_trending_videos',
    'search_channels',
    'download_playlist',
    'check_ffmpeg_installed',
    'get_ffmpeg_path',
    'run_ffmpeg_command',
    'ffmpeg_add_text',
    'ffmpeg_add_image',
    'ffmpeg_extract_audio',
    'ffmpeg_replace_audio',
    'ffmpeg_mix_audio',
    'ffmpeg_add_audio',
    'ffmpeg_freeze_frame',
    'ffmpeg_slow_motion',
    'ffmpeg_time_lapse',
    'ffmpeg_render_timeline',
    'ffmpeg_concat_videos',
    'ffmpeg_add_multiple_texts',
    'generate_unique_filename',
    'get_cache',
    'set_cache',
    'format_duration',
    'format_file_size',
    'safe_delete_file',
    'create_temp_dir',
    'cleanup_temp_dir',
    'check_ffmpeg_availability'
    # ffmpeg_utils functions commented out - file missing
    # 'enhance_video',
    # 'apply_ffmpeg_filters',
    # 'extract_frames',
    # 'create_video_from_images',
    # 'add_audio_to_video',
    # 'extract_audio_ffmpeg',
    # 'merge_videos',
    # 'change_video_speed',
    # 'create_thumbnail'
]
