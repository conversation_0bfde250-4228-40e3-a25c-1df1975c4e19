"""
Video Search Interface
Comprehensive search functionality for videos from various platforms
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import json
import os
import requests
import webbrowser
from typing import List, Dict
from urllib.parse import quote_plus
import yt_dlp

class VideoSearchWidget(QWidget):
    """Video search widget with platform selection and results"""
    
    video_selected = pyqtSignal(dict)  # Emits video info when selected
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_results = []
        self.setup_ui()
        
    def setup_ui(self):
        """Setup search interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Search header
        header = QLabel("🔍 VIDEO SEARCH")
        header.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #0078d4;
                padding: 10px;
                border-bottom: 2px solid #0078d4;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(header)
        
        # Search controls
        search_controls = self.create_search_controls()
        layout.addWidget(search_controls)
        
        # Platform selection
        platform_controls = self.create_platform_controls()
        layout.addWidget(platform_controls)
        
        # Results area
        self.results_area = self.create_results_area()
        layout.addWidget(self.results_area)
        
        # Status bar
        self.status_label = QLabel("Ready to search...")
        self.status_label.setStyleSheet("color: #888; padding: 5px;")
        layout.addWidget(self.status_label)
        
    def create_search_controls(self):
        """Create search input and button"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Search input
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter search terms (e.g., 'python tutorial', 'music video')...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #555;
                border-radius: 5px;
                background-color: #333;
                color: white;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """)
        self.search_input.returnPressed.connect(self.perform_search)
        layout.addWidget(self.search_input)
        
        # Search button
        search_btn = QPushButton("🔍 Search")
        search_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        search_btn.clicked.connect(self.perform_search)
        layout.addWidget(search_btn)
        
        return widget
        
    def create_platform_controls(self):
        """Create platform selection controls"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Platform label
        platform_label = QLabel("Platform:")
        platform_label.setStyleSheet("color: white; font-weight: bold; padding: 5px;")
        layout.addWidget(platform_label)
        
        # Platform selection
        self.platform_combo = QComboBox()
        self.platform_combo.addItems([
            "All Platforms",
            "YouTube", 
            "Vimeo",
            "Facebook",
            "Instagram",
            "Twitter",
            "TikTok"
        ])
        self.platform_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                background-color: #333;
                color: white;
                border: 1px solid #555;
                border-radius: 3px;
                min-width: 120px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
            }
        """)
        layout.addWidget(self.platform_combo)
        
        # Max results
        results_label = QLabel("Max Results:")
        results_label.setStyleSheet("color: white; font-weight: bold; padding: 5px;")
        layout.addWidget(results_label)
        
        self.max_results_spin = QSpinBox()
        self.max_results_spin.setRange(5, 50)
        self.max_results_spin.setValue(20)
        self.max_results_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                background-color: #333;
                color: white;
                border: 1px solid #555;
                border-radius: 3px;
                min-width: 80px;
            }
        """)
        layout.addWidget(self.max_results_spin)
        
        layout.addStretch()
        
        # Advanced search button
        advanced_btn = QPushButton("⚙️ Advanced")
        advanced_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #444;
                color: white;
                border: 1px solid #666;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        advanced_btn.clicked.connect(self.show_advanced_search)
        layout.addWidget(advanced_btn)
        
        return widget
        
    def create_results_area(self):
        """Create scrollable results area"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #555;
                background-color: #2b2b2b;
            }
        """)
        
        # Results container
        self.results_container = QWidget()
        self.results_layout = QVBoxLayout(self.results_container)
        self.results_layout.setAlignment(Qt.AlignTop)
        
        # Initial message
        initial_msg = QLabel("🔍 Enter search terms above and click Search to find videos")
        initial_msg.setAlignment(Qt.AlignCenter)
        initial_msg.setStyleSheet("""
            QLabel {
                color: #888;
                font-size: 16px;
                padding: 50px;
            }
        """)
        self.results_layout.addWidget(initial_msg)
        
        scroll_area.setWidget(self.results_container)
        return scroll_area
        
    def perform_search(self):
        """Perform video search"""
        query = self.search_input.text().strip()
        if not query:
            self.status_label.setText("❌ Please enter search terms")
            return
            
        platform = self.platform_combo.currentText()
        max_results = self.max_results_spin.value()
        
        self.status_label.setText(f"🔍 Searching for '{query}' on {platform}...")
        
        # Clear previous results
        self.clear_results()
        
        # Show loading indicator
        loading_widget = self.create_loading_widget()
        self.results_layout.addWidget(loading_widget)
        
        # Start search in background thread
        self.search_thread = SearchThread(query, platform, max_results)
        self.search_thread.results_ready.connect(self.display_results)
        self.search_thread.error_occurred.connect(self.handle_search_error)
        self.search_thread.start()
        
    def clear_results(self):
        """Clear previous search results"""
        while self.results_layout.count():
            child = self.results_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
    def create_loading_widget(self):
        """Create loading indicator"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Loading animation (simple text for now)
        loading_label = QLabel("🔄 Searching...")
        loading_label.setAlignment(Qt.AlignCenter)
        loading_label.setStyleSheet("""
            QLabel {
                color: #0078d4;
                font-size: 16px;
                padding: 30px;
            }
        """)
        layout.addWidget(loading_label)
        
        # Animate the loading text
        self.loading_timer = QTimer()
        self.loading_dots = 0
        
        def update_loading():
            self.loading_dots = (self.loading_dots + 1) % 4
            dots = "." * self.loading_dots
            loading_label.setText(f"🔄 Searching{dots}")
            
        self.loading_timer.timeout.connect(update_loading)
        self.loading_timer.start(500)
        
        return widget
        
    def display_results(self, results):
        """Display search results with thumbnails"""
        self.loading_timer.stop()
        self.clear_results()

        if not results:
            no_results = QLabel("😔 No videos found. Try different search terms.")
            no_results.setAlignment(Qt.AlignCenter)
            no_results.setStyleSheet("""
                QLabel {
                    color: #888;
                    font-size: 16px;
                    padding: 50px;
                }
            """)
            self.results_layout.addWidget(no_results)
            self.status_label.setText("No results found")
            return
            
        self.search_results = results
        self.displayed_count = 0

        # Display initial batch of videos (5 at a time)
        self.display_video_batch(5)

        # Add "Load More" button if there are more results
        if len(results) > 5:
            self.add_load_more_button()

        self.status_label.setText(f"✅ Found {len(results)} videos")

    def display_video_batch(self, batch_size):
        """Display a batch of videos with thumbnails"""
        end_index = min(self.displayed_count + batch_size, len(self.search_results))

        for i in range(self.displayed_count, end_index):
            video = self.search_results[i]
            result_widget = self.create_enhanced_result_widget(video, i)
            self.results_layout.addWidget(result_widget)

        self.displayed_count = end_index

    def add_load_more_button(self):
        """Add Load More button"""
        if hasattr(self, 'load_more_button'):
            self.load_more_button.deleteLater()

        self.load_more_button = QPushButton("📥 Load More Videos")
        self.load_more_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        self.load_more_button.clicked.connect(self.load_more_videos)
        self.results_layout.addWidget(self.load_more_button)

    def load_more_videos(self):
        """Load more videos when button is clicked"""
        remaining = len(self.search_results) - self.displayed_count
        if remaining > 0:
            # Remove the load more button temporarily
            self.load_more_button.deleteLater()

            # Display next batch
            self.display_video_batch(5)

            # Add load more button again if there are still more videos
            if self.displayed_count < len(self.search_results):
                self.add_load_more_button()

    def create_enhanced_result_widget(self, video, index):
        """Create enhanced widget for single search result with thumbnail"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                margin: 5px;
            }
            QWidget:hover {
                background-color: #e9ecef;
                border-color: #0078d4;
            }
        """)

        layout = QHBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)

        # Thumbnail with actual image loading
        thumbnail_label = QLabel()
        thumbnail_label.setFixedSize(120, 90)
        thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #dee2e6;
                border: 1px solid #ced4da;
                border-radius: 4px;
                color: #6c757d;
            }
        """)
        thumbnail_label.setAlignment(Qt.AlignCenter)
        thumbnail_label.setText("🎬\nLoading...")

        # Load thumbnail image if available
        thumbnail_url = video.get('thumbnail', '')
        if thumbnail_url:
            self.load_thumbnail(thumbnail_label, thumbnail_url)
        else:
            thumbnail_label.setText("🎬\nNo Image")

        layout.addWidget(thumbnail_label)

        # Video info section
        info_layout = QVBoxLayout()

        # Title with proper wrapping and fixed height
        title_label = QLabel(video.get('title', 'Unknown Title'))
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #212529;
                margin-bottom: 4px;
            }
        """)
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(40)  # Limit height to prevent overlapping
        info_layout.addWidget(title_label)

        # Metadata
        metadata_text = f"👁️ {video.get('views', 'Unknown')} views • ⏱️ {video.get('duration', 'Unknown')} • 📅 {video.get('upload_date', 'Unknown')}"
        metadata_label = QLabel(metadata_text)
        metadata_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 8px;
            }
        """)
        info_layout.addWidget(metadata_label)

        # Description with limited height
        description = video.get('description', '')
        if description:
            desc_label = QLabel(description[:150] + "..." if len(description) > 150 else description)
            desc_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #495057;
                    line-height: 1.4;
                }
            """)
            desc_label.setWordWrap(True)
            desc_label.setMaximumHeight(40)  # Limit height
            info_layout.addWidget(desc_label)

        layout.addLayout(info_layout, 1)

        # Action buttons
        button_layout = QVBoxLayout()

        # Play button
        play_btn = QPushButton("▶️ Play")
        play_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        play_btn.clicked.connect(lambda: self.play_video(video))
        button_layout.addWidget(play_btn)

        # Download button
        download_btn = QPushButton("📥 Download")
        download_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        download_btn.clicked.connect(lambda: self.download_video(video))
        button_layout.addWidget(download_btn)

        # Double-click support for the entire widget
        widget.mouseDoubleClickEvent = lambda event: self.play_video(video)

        layout.addLayout(button_layout)

        return widget

    def create_result_widget(self, video, index):
        """Create widget for single search result"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: #333;
                border: 1px solid #555;
                border-radius: 5px;
                margin: 5px;
            }
            QWidget:hover {
                background-color: #404040;
                border-color: #0078d4;
            }
        """)
        
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Thumbnail with image loading
        thumbnail = QLabel("🎬")
        thumbnail.setFixedSize(120, 90)
        thumbnail.setAlignment(Qt.AlignCenter)
        thumbnail.setStyleSheet("""
            QLabel {
                background-color: #222;
                border: 1px solid #444;
                border-radius: 3px;
                font-size: 24px;
            }
        """)

        # Load thumbnail image if available
        thumbnail_url = video.get('thumbnail', '')
        if thumbnail_url:
            self.load_thumbnail(thumbnail, thumbnail_url)

        layout.addWidget(thumbnail)
        
        # Video info
        info_layout = QVBoxLayout()
        
        # Title
        title = QLabel(video.get('title', 'Unknown Title'))
        title.setWordWrap(True)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        info_layout.addWidget(title)
        
        # Details
        details = []
        if video.get('duration'):
            details.append(f"⏱️ {video['duration']}")
        if video.get('platform'):
            details.append(f"📺 {video['platform']}")
        if video.get('views'):
            details.append(f"👁️ {video['views']}")
            
        details_text = " • ".join(details)
        details_label = QLabel(details_text)
        details_label.setStyleSheet("""
            QLabel {
                color: #aaa;
                font-size: 12px;
                margin-bottom: 5px;
            }
        """)
        info_layout.addWidget(details_label)
        
        # Description
        description = video.get('description', '')
        if len(description) > 150:
            description = description[:150] + "..."
        desc_label = QLabel(description)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("""
            QLabel {
                color: #ccc;
                font-size: 11px;
            }
        """)
        info_layout.addWidget(desc_label)
        
        layout.addLayout(info_layout, 1)
        
        # Action buttons
        buttons_layout = QVBoxLayout()
        
        # Play button
        play_btn = QPushButton("▶️ Play")
        play_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        play_btn.clicked.connect(lambda: self.play_video(video))
        buttons_layout.addWidget(play_btn)
        
        # Download button
        download_btn = QPushButton("⬇️ Download")
        download_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        download_btn.clicked.connect(lambda: self.download_video(video))
        buttons_layout.addWidget(download_btn)
        
        # Edit button
        edit_btn = QPushButton("✂️ Edit")
        edit_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #ffc107;
                color: black;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        edit_btn.clicked.connect(lambda: self.edit_video(video))
        buttons_layout.addWidget(edit_btn)
        
        layout.addLayout(buttons_layout)
        
        return widget
        
    def play_video(self, video):
        """Enhanced video playback with better URL handling and platform detection"""
        try:
            print(f"🎬 Attempting to play video: {video.get('title', 'Unknown')}")

            # Prepare enhanced video info
            video_info = video.copy()
            url = video_info.get('url', '')
            video_id = video_info.get('video_id', '')

            # Determine the best playback method
            platform = video_info.get('platform', '').lower()

            if 'youtube' in platform or 'youtube.com' in url or 'youtu.be' in url:
                # Enhanced YouTube handling - try to get direct stream first
                video_info['playback_method'] = 'youtube'
                print(f"🎥 Detected YouTube video")
                
                # Try to get direct stream URL
                stream_url = self.get_playable_url(video)
                if stream_url:
                    print(f"✅ Got direct YouTube stream URL")
                    video_info['playable_url'] = stream_url
                    video_info['playback_method'] = 'direct_stream'
                else:
                    # Fallback to webview for YouTube
                    print(f"⚠️ Using YouTube web player fallback")
                    video_info['playback_method'] = 'web'
                    video_info['playable_url'] = url
            elif any(url.lower().endswith(ext) for ext in ['.mp4', '.webm', '.avi', '.mov', '.mkv']):
                # Direct video file
                video_info['playback_method'] = 'direct'
                video_info['playable_url'] = url
                print(f"🎬 Detected direct video file")
            else:
                # Web content
                video_info['playback_method'] = 'web'
                video_info['playable_url'] = url
                print(f"🌐 Detected web content")

            # Emit signal with enhanced video info
            self.video_selected.emit(video_info)
            self.status_label.setText(f"▶️ Preparing: {video.get('title', 'Unknown')}")

        except Exception as e:
            print(f"❌ Error playing video: {e}")
            self.status_label.setText(f"❌ Error: {str(e)}")
            # Fallback to browser
            if video.get('url'):
                webbrowser.open(video['url'])
                self.status_label.setText(f"🌐 Opened in browser: {video.get('title', 'Unknown')}")

    def get_playable_url(self, video):
        """Get direct playable URL for video"""
        try:
            if video.get('video_id') and video.get('platform') == 'YouTube':
                # Use yt-dlp to get direct stream URL
                ydl_opts = {
                    'quiet': True,
                    'no_warnings': True,
                    'format': 'best[height<=720]',  # Get best quality up to 720p
                }

                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    info = ydl.extract_info(f"https://www.youtube.com/watch?v={video['video_id']}", download=False)

                if info and info.get('url'):
                    return info['url']

            return None

        except Exception as e:
            print(f"Error getting playable URL: {e}")
            return None
        
    def download_video(self, video):
        """Download selected video with save dialog"""
        try:
            url = video.get('url', '')
            if not url:
                QMessageBox.warning(self, "Download Error", "No URL available for download")
                return
                
            # Get save location from user
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Video",
                f"{video.get('title', 'video')}.mp4",
                "Video Files (*.mp4);;All Files (*)"
            )
            
            if not file_path:
                return  # User cancelled
                
            self.status_label.setText(f"⬇️ Downloading: {video.get('title', 'Unknown')}")
            
            # Start download in background thread
            self.download_thread = DownloadThread(url, file_path)
            self.download_thread.progress.connect(self.on_download_progress)
            self.download_thread.finished.connect(self.on_download_finished)
            self.download_thread.error.connect(self.on_download_error)
            self.download_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "Download Error", f"Failed to start download: {str(e)}")

    def on_download_progress(self, progress):
        """Handle download progress updates"""
        self.status_label.setText(f"⬇️ Downloading: {progress}%")

    def on_download_finished(self, file_path):
        """Handle download completion"""
        self.status_label.setText(f"✅ Download complete: {os.path.basename(file_path)}")
        QMessageBox.information(self, "Download Complete", 
                              f"Video downloaded successfully to:\n{file_path}")

    def on_download_error(self, error):
        """Handle download errors"""
        self.status_label.setText(f"❌ Download failed")
        QMessageBox.critical(self, "Download Error", f"Download failed: {error}")

    def edit_video(self, video):
        """Edit selected video"""
        # TODO: Implement edit functionality
        self.status_label.setText(f"✂️ Editing: {video.get('title', 'Unknown')}")
        
    def handle_search_error(self, error):
        """Handle search errors"""
        self.loading_timer.stop()
        self.clear_results()
        
        error_widget = QLabel(f"❌ Search Error: {error}")
        error_widget.setAlignment(Qt.AlignCenter)
        error_widget.setStyleSheet("""
            QLabel {
                color: #ff4444;
                font-size: 16px;
                padding: 50px;
            }
        """)
        self.results_layout.addWidget(error_widget)
        self.status_label.setText(f"❌ Error: {error}")
        
    def show_advanced_search(self):
        """Show advanced search options"""
        # TODO: Implement advanced search dialog
        QMessageBox.information(self, "Advanced Search", "Advanced search options coming soon!")

    def load_thumbnail(self, label, thumbnail_url):
        """Load thumbnail image from URL"""
        try:
            import requests
            from PyQt5.QtGui import QPixmap
            from PyQt5.QtCore import QThread, pyqtSignal

            # Create thumbnail loader thread
            loader = ThumbnailLoader(thumbnail_url)
            loader.thumbnail_loaded.connect(lambda pixmap: self.set_thumbnail(label, pixmap))
            loader.thumbnail_failed.connect(lambda: self.set_thumbnail_failed(label))
            loader.start()

        except Exception as e:
            print(f"Error loading thumbnail: {e}")
            self.set_thumbnail_failed(label)

    def set_thumbnail(self, label, pixmap):
        """Set thumbnail pixmap to label"""
        try:
            if pixmap and not pixmap.isNull():
                # Scale pixmap to fit label size
                scaled_pixmap = pixmap.scaled(
                    label.size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                label.setPixmap(scaled_pixmap)
                label.setText("")  # Clear text
            else:
                self.set_thumbnail_failed(label)
        except Exception as e:
            print(f"Error setting thumbnail: {e}")
            self.set_thumbnail_failed(label)

    def set_thumbnail_failed(self, label):
        """Set failed thumbnail state"""
        label.setText("🎬\nNo Image")
        label.setStyleSheet(label.styleSheet() + "color: #888;")

class ThumbnailLoader(QThread):
    """Thread for loading thumbnails from URLs"""
    thumbnail_loaded = pyqtSignal(object)  # QPixmap
    thumbnail_failed = pyqtSignal()

    def __init__(self, url):
        super().__init__()
        self.url = url

    def run(self):
        """Load thumbnail in background thread"""
        try:
            import requests
            from PyQt5.QtGui import QPixmap

            # Download image data
            response = requests.get(self.url, timeout=10)
            response.raise_for_status()

            # Create pixmap from image data
            pixmap = QPixmap()
            if pixmap.loadFromData(response.content):
                self.thumbnail_loaded.emit(pixmap)
            else:
                self.thumbnail_failed.emit()

        except Exception as e:
            print(f"Thumbnail loading failed: {e}")
            self.thumbnail_failed.emit()

class DownloadThread(QThread):
    """Background thread for downloading videos"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    
    def __init__(self, url, file_path):
        super().__init__()
        self.url = url
        self.file_path = file_path
        
    def run(self):
        """Download video using yt-dlp"""
        try:
            ydl_opts = {
                'outtmpl': self.file_path,
                'progress_hooks': [self.download_progress],
                'quiet': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([self.url])
                
            self.finished.emit(self.file_path)
            
        except Exception as e:
            self.error.emit(str(e))
            
    def download_progress(self, d):
        """Handle download progress"""
        if d['status'] == 'downloading':
            percent = d.get('_percent_str', '0%').replace('%', '')
            try:
                self.progress.emit(int(float(percent)))
            except:
                pass

class SearchThread(QThread):
    """Background thread for performing searches"""
    
    results_ready = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, query, platform, max_results):
        super().__init__()
        self.query = query
        self.platform = platform
        self.max_results = max_results
        
    def run(self):
        """Run search in background"""
        try:
            results = []

            if self.platform in ["All Platforms", "YouTube"]:
                youtube_results = self.search_youtube()
                results.extend(youtube_results)

            # Sort by view count (most viewed first)
            results.sort(key=lambda x: self.parse_view_count(x.get('views', '0')), reverse=True)

            # Limit results
            results = results[:self.max_results]

            self.results_ready.emit(results)

        except Exception as e:
            self.error_occurred.emit(str(e))

    def search_youtube(self):
        """Search YouTube using yt-dlp with improved error handling"""
        try:
            # Use yt-dlp to search YouTube with better error handling
            ydl_opts = {
                'quiet': False,  # Enable output for debugging
                'no_warnings': False,
                'extract_flat': True,
                'ignoreerrors': True,
                'no_check_certificate': True,
                'socket_timeout': 30,
                'retries': 3,
            }

            # Format the search query properly for yt-dlp
            search_query = f"ytsearch{self.max_results}:{self.query}"
            print(f"Searching YouTube with query: {search_query}")

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                search_results = ydl.extract_info(search_query, download=False)

            results = []
            if search_results and 'entries' in search_results:
                print(f"Found {len(search_results['entries'])} entries")
                for i, entry in enumerate(search_results['entries']):
                    if entry and entry.get('id'):
                        print(f"Processing entry {i+1}: {entry.get('title', 'Unknown')}")
                        # Create basic video info without detailed extraction to avoid API limits
                        video_info = {
                            'title': entry.get('title', 'Unknown Title'),
                            'description': entry.get('description', '')[:200] + '...' if entry.get('description') else 'No description available',
                            'duration': self.format_duration(entry.get('duration', 0)),
                            'platform': 'YouTube',
                            'views': self.format_views(entry.get('view_count', 0)),
                            'url': entry.get('url', f"https://www.youtube.com/watch?v={entry['id']}"),
                            'video_id': entry['id'],
                            'thumbnail': entry.get('thumbnail', ''),
                            'uploader': entry.get('uploader', 'Unknown'),
                            'upload_date': entry.get('upload_date', ''),
                            'like_count': entry.get('like_count', 0),
                        }
                        results.append(video_info)

                        # Limit to prevent too many requests
                        if len(results) >= self.max_results:
                            break
            else:
                print("No search results found")

            print(f"Returning {len(results)} results")
            return results

        except Exception as e:
            print(f"YouTube search error: {e}")
            # Return some dummy data for testing if search fails
            return [{
                'title': f'Sample Video Result for "{self.query}"',
                'description': 'This is a sample result. The actual search may be experiencing connectivity issues.',
                'duration': '3:45',
                'platform': 'YouTube',
                'views': '1.2M views',
                'url': 'https://www.youtube.com',
                'video_id': 'sample123',
                'thumbnail': '',
                'uploader': 'Sample Channel',
                'upload_date': '20240101',
                'like_count': 1000,
            }]

    def get_video_details(self, video_id):
        """Get detailed video information"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=False)

            return {
                'title': info.get('title', 'Unknown Title'),
                'description': info.get('description', '')[:200] + '...' if info.get('description') else '',
                'duration': self.format_duration(info.get('duration', 0)),
                'platform': 'YouTube',
                'views': self.format_views(info.get('view_count', 0)),
                'url': info.get('webpage_url', ''),
                'video_id': video_id,
                'thumbnail': info.get('thumbnail', ''),
                'uploader': info.get('uploader', 'Unknown'),
                'upload_date': info.get('upload_date', ''),
                'like_count': info.get('like_count', 0),
                'formats': info.get('formats', [])
            }

        except Exception as e:
            print(f"Error getting video details for {video_id}: {e}")
            return None

    def format_duration(self, seconds):
        """Format duration from seconds to MM:SS or HH:MM:SS"""
        if not seconds:
            return "0:00"

        # Convert to int to handle float values
        seconds = int(seconds)

        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60

        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"

    def format_views(self, view_count):
        """Format view count"""
        if not view_count:
            return "0 views"

        # Convert to int to handle float values
        view_count = int(view_count)

        if view_count >= 1000000:
            return f"{view_count/1000000:.1f}M views"
        elif view_count >= 1000:
            return f"{view_count/1000:.1f}K views"
        else:
            return f"{view_count:,} views"

    def parse_view_count(self, views_str):
        """Parse view count string to number for sorting"""
        if not views_str:
            return 0

        # Remove 'views' and clean up
        views_str = views_str.replace(' views', '').replace(',', '')

        try:
            if 'M' in views_str:
                return int(float(views_str.replace('M', '')) * 1000000)
            elif 'K' in views_str:
                return int(float(views_str.replace('K', '')) * 1000)
            else:
                return int(views_str)
        except:
            return 0
