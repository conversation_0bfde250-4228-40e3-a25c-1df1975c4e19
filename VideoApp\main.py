# -*- coding: utf-8 -*-
import sys
import os
import threading
import argpar<PERSON>
from typing import Dict, Any, List

# ------------------------------
# Suppress PostgreSQL driver warnings
# ------------------------------
os.environ['QT_DEBUG_PLUGINS'] = '0'  # Disable plugin debug messages

# ------------------------------
# Ensure current folder is in sys.path
# ------------------------------
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
if BASE_DIR not in sys.path:
    sys.path.insert(0, BASE_DIR)

# ------------------------------
# Workaround for missing pyaudioop in Python 3.13
# ------------------------------
try:
    import pyaudioop
except ImportError:
    # Add our compatibility module to sys.modules
    import sys
    pyaudioop_path = os.path.join(BASE_DIR, 'pyaudioop.py')
    if os.path.exists(pyaudioop_path):
        import importlib.util
        spec = importlib.util.spec_from_file_location("pyaudioop", pyaudioop_path)
        pyaudioop_module = importlib.util.module_from_spec(spec)
        sys.modules['pyaudioop'] = pyaudioop_module
        spec.loader.exec_module(pyaudioop_module)

# ------------------------------
# Import application modules
# ------------------------------
try:
    # Suppress Qt plugin warnings
    import warnings
    warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*LIBPQ.dll.*")
    warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*qsqlpsql.dll.*")
    
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
    from PyQt5.QtCore import QUrl
    from ui.main_window import MainWindow
    from ui.unified_video_player import UnifiedVideoPlayer
    from ui.premiere_interface import PremiereInterface
    from core import theme   # ✅ theme core کے اندر ہے
    
    # Import consolidated modules from utils
    from utils.video_downloader import download_video_standalone as download_video, list_formats as formats
    from utils.video_downloader import get_trending_videos, search_channels, download_playlist_advanced
    from utils.helpers import check_ffmpeg_availability

    # Create wrapper functions for compatibility
    def get_recommended_format(url):
        """Get recommended format from available formats"""
        formats_list = formats(url)
        if formats_list and len(formats_list) > 0:
            # Return the first high-quality format
            for fmt in formats_list:
                if fmt.get('height', 0) >= 720:
                    return fmt
            return formats_list[0]  # Return first format if no HD found
        return None

    def list_quality(url):
        """List available qualities"""
        return formats(url)

    def get_quality_by_id(url, format_id):
        """Get quality by format ID"""
        formats_list = formats(url)
        for fmt in formats_list:
            if fmt.get('format_id') == format_id:
                return fmt
        return None

    def get_quality_by_resolution(url, resolution):
        """Get quality by resolution"""
        formats_list = formats(url)
        for fmt in formats_list:
            if str(fmt.get('height', '')) in resolution:
                return fmt
        return None
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class VideoPlayer:
    """Thread-safe video player with error handling"""
    
    def __init__(self):
        self.player = QMediaPlayer()
        self.player.error.connect(self.handle_error)
        self.error_message = ""
        
    def handle_error(self, error):
        """Handle media player errors"""
        error_messages = {
            QMediaPlayer.NoError: "No error",
            QMediaPlayer.ResourceError: "Resource error - Cannot play the media",
            QMediaPlayer.FormatError: "Format error - Unsupported format",
            QMediaPlayer.NetworkError: "Network error - Connection issue",
            QMediaPlayer.AccessDeniedError: "Access denied - Permission issue",
        }
        self.error_message = error_messages.get(error, "Unknown media error")
        print(f"Media error: {self.error_message}")
    
    def play_stream(self, stream_url):
        """Safely play a video stream in a separate thread"""
        if not stream_url:
            return "No stream URL provided"
            
        def play_in_thread():
            try:
                # Check if URL is valid
                if not stream_url.startswith(('http://', 'https://')):
                    self.error_message = "Invalid URL format"
                    return
                    
                media_content = QMediaContent(QUrl(stream_url))
                self.player.setMedia(media_content)
                self.player.play()
                
            except Exception as e:
                self.error_message = f"Playback error: {str(e)}"
                print(self.error_message)
        
        # Reset error message
        self.error_message = ""
        
        # Run in separate thread to avoid UI freezing
        thread = threading.Thread(target=play_in_thread)
        thread.daemon = True
        thread.start()
        
        # Wait a moment for any immediate errors
        thread.join(timeout=0.5)
        
        return self.error_message
    
    def stop(self):
        self.player.stop()
    
    def pause(self):
        self.player.pause()
    
    def set_volume(self, volume):
        self.player.setVolume(volume)


def check_dependencies():
    """Check if all required dependencies are available."""
    ffmpeg_status = check_ffmpeg_availability()
    if not ffmpeg_status["available"]:
        print("Warning: FFmpeg is not installed or not in PATH")
        print(ffmpeg_status["message"])
        print("Installation instructions:")
        for os_name, instructions in ffmpeg_status.get("installation_instructions", {}).items():
            print(f"  {os_name}: {instructions}")
        print()
    
    # Check for yt-dlp
    try:
        import yt_dlp
        print("✅ yt-dlp is installed")
    except ImportError:
        print("❌ yt-dlp is not installed. Please install it with: pip install yt-dlp")
    
    # PostgreSQL driver warning is non-critical
    print("ℹ️  PostgreSQL driver warning can be ignored (not required for video downloading)")


def main():
    """Main entry point for VideoApp."""
    # Suppress warnings at the main level too
    import warnings
    warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*LIBPQ.dll.*")
    warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*qsqlpsql.dll.*")
    
    parser = argparse.ArgumentParser(description="VideoApp - Download, edit, and share videos")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Check dependencies first
    check_dependencies()
    
    # Download command
    download_parser = subparsers.add_parser("download", help="Download videos")
    download_parser.add_argument("url", help="URL of the video to download")
    download_parser.add_argument("--format", "-f", default="best", help="Video format/quality")
    download_parser.add_argument("--output", "-o", help="Output file path")
    download_parser.add_argument("--start-time", type=float, help="Start time for trimming (seconds)")
    download_parser.add_argument("--end-time", type=float, help="End time for trimming (seconds)")
    
    # List formats command
    formats_parser = subparsers.add_parser("formats", help="List available formats")
    formats_parser.add_argument("url", help="URL of the video")
    
    # List quality command
    quality_parser = subparsers.add_parser("quality", help="List available quality options")
    quality_parser.add_argument("url", help="URL of the video")
    quality_parser.add_argument("--resolution", help="Filter by specific resolution")
    quality_parser.add_argument("--format-id", help="Get specific format by ID")
    
    # Edit command (will require FFmpeg)
    edit_parser = subparsers.add_parser("edit", help="Edit videos")
    edit_parser.add_argument("input", help="Input video path")
    edit_parser.add_argument("--output", "-o", help="Output video path")
    
    # Search command
    search_parser = subparsers.add_parser("search", help="Search for videos")
    search_parser.add_argument("query", help="Search query")
    search_parser.add_argument("--platform", "-p", help="Platform to search")
    search_parser.add_argument("--max", "-m", type=int, default=10, help="Max results")
    
    # Trending command
    trending_parser = subparsers.add_parser("trending", help="Get trending videos")
    trending_parser.add_argument("--platform", "-p", default="youtube", help="Platform (youtube, vimeo)")
    trending_parser.add_argument("--max", "-m", type=int, default=20, help="Max results")
    
    # Channels command
    channels_parser = subparsers.add_parser("channels", help="Search for channels")
    channels_parser.add_argument("query", help="Search query")
    channels_parser.add_argument("--max", "-m", type=int, default=10, help="Max results")
    
    # Add a command to check FFmpeg status
    ffmpeg_parser = subparsers.add_parser("check-ffmpeg", help="Check FFmpeg installation")
    
    # GUI mode (default)
    gui_parser = subparsers.add_parser("gui", help="Start GUI application (default)")
    gui_parser.add_argument("--style", choices=["classic", "premiere"], default="premiere",
                           help="GUI style: classic (original) or premiere (Adobe Premiere Pro style)")

    args = parser.parse_args()
    
    # If no command provided, start GUI with premiere style
    if not args.command:
        start_gui(style="premiere")
        return
    
    try:
        if args.command == "download":
            result = download_video(
                args.url, 
                format_id=args.format, 
                output_path=args.output,
                start_time=args.start_time,
                end_time=args.end_time
            )
            print_result(result)
            
        elif args.command == "formats":
            result = formats(args.url)
            if isinstance(result, list):
                print("✅ Available formats:")
                for fmt in result:
                    print(f"  - {fmt.get('format_id', 'N/A')}: {fmt.get('resolution', 'N/A')} ({fmt.get('ext', 'N/A')})")
            else:
                print_result(result)
            
        elif args.command == "quality":
            if args.resolution:
                result = get_quality_by_resolution(args.url, args.resolution)
            elif args.format_id:
                result = get_quality_by_id(args.url, args.format_id)
            else:
                result = list_quality(args.url)
            print_result(result)
            
        elif args.command == "edit":
            # Check if FFmpeg is available before attempting to edit
            ffmpeg_status = check_ffmpeg_availability()
            if not ffmpeg_status["available"]:
                print("Error: Video editing requires FFmpeg")
                print(ffmpeg_status["message"])
                sys.exit(1)
            
            print("Edit functionality is not yet implemented in CLI mode.")
            print("Use the GUI application for video editing.")
            
        elif args.command == "search":
            from social.search import search_videos, Platform
            platform = Platform(args.platform.lower()) if args.platform else None
            result = search_videos(args.query, platform=platform, max_results=args.max)
            print_result(result)
            
        elif args.command == "trending":
            result = get_trending_videos(args.platform, max_results=args.max)
            print_result(result)
            
        elif args.command == "channels":
            result = search_channels(args.query, max_results=args.max)
            print_result(result)
            
        elif args.command == "check-ffmpeg":
            result = check_ffmpeg_availability()
            print_result(result)
            
        elif args.command == "gui":
            start_gui(style=args.style)
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


def start_gui(style="premiere"):
    """Start the GUI application with specified style."""
    # Suppress warnings for GUI mode too
    import warnings
    warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*LIBPQ.dll.*")
    warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*qsqlpsql.dll.*")

    app = QApplication(sys.argv)
    app.setApplicationName("Professional Video Editor")
    app.setApplicationVersion("2.0.0")

    print(f"🎬 Starting Video Editor in {style} mode...")

    if style == "premiere":
        # ------------------------------
        # Adobe Premiere Pro Style Interface
        # ------------------------------
        print("✨ Loading Adobe Premiere Pro-style interface...")

        # Set professional dark theme
        from PyQt5.QtWidgets import QStyleFactory
        from PyQt5.QtGui import QPalette, QColor

        app.setStyle(QStyleFactory.create('Fusion'))
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(43, 43, 43))
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        palette.setColor(QPalette.Base, QColor(35, 35, 35))
        palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        palette.setColor(QPalette.Text, QColor(255, 255, 255))
        palette.setColor(QPalette.Button, QColor(64, 64, 64))
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        palette.setColor(QPalette.Highlight, QColor(0, 120, 212))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        app.setPalette(palette)

        # Create Premiere-style interface
        w = PremiereInterface()

        print("🎯 Premiere Pro Features:")
        print("   ✅ Separate video, audio, text, and image tracks")
        print("   ✅ Professional timeline with zoom controls")
        print("   ✅ Project panel for media management")
        print("   ✅ Effects panel with video/audio effects")
        print("   ✅ Properties panel for clip adjustments")
        print("   ✅ Improved video player with crash protection")
        print("   ✅ Download manager with progress tracking")

    else:
        # ------------------------------
        # Classic Interface
        # ------------------------------
        print("📺 Loading classic interface...")

        # Initialize enhanced video player
        video_player = UnifiedVideoPlayer()

        # Select default theme
        USE_DARK = True
        try:
            if USE_DARK:
                app.setStyleSheet(theme.app_styles(theme.DARK_THEME))
            else:
                app.setStyleSheet(theme.app_styles(theme.LIGHT_THEME))
        except AttributeError as e:
            print(f"❌ Theme error: {e}")
            sys.exit(1)

        # Load Main Window with video player reference
        w = MainWindow(video_player=video_player)

    w.show()
    print("🚀 Application started successfully!")

    # ------------------------------
    # Start event loop
    # ------------------------------
    sys.exit(app.exec_())


def print_result(result):
    """Print a result dictionary or list in a formatted way."""
    # Handle list results
    if isinstance(result, list):
        print("✅ Results:")
        for item in result:
            if isinstance(item, dict):
                print(f"  - {item}")
            else:
                print(f"  - {item}")
        return

    # Handle dictionary results
    if not isinstance(result, dict):
        print(f"Result: {result}")
        return

    if result.get("success", False) or result.get("available", False) or result.get("installed", False):
        print("✅ Success!")
        for key, value in result.items():
            if key not in ["success", "available", "installed"]:
                if isinstance(value, list) and value:
                    print(f"{key}:")
                    for item in value:
                        if hasattr(item, '__dict__'):
                            print(f"  - {item}")
                        else:
                            print(f"  - {item}")
                else:
                    print(f"{key}: {value}")
    else:
        print("❌ Error or Warning:")
        for key, value in result.items():
            if key not in ["success", "available", "installed"]:
                print(f"{key}: {value}")


if __name__ == "__main__":
    main()
