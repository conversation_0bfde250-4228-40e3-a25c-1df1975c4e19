# -*- coding: utf-8 -*-
# Social module package initialization

try:
    from .search import search_videos, get_trending_videos, search_channels, Platform, SearchResult
    from .auth import SocialAuthManager, AuthConfig, AuthStatus, get_platform_auth_url
    from .upload import upload_video, get_upload_status, update_video_metadata, delete_video, UploadConfig
except ImportError as e:
    print(f"Warning: Could not import social module components: {e}")
    # Create dummy classes to prevent import errors
    class Platform: pass
    class SearchResult: pass
    class AuthStatus: pass
    class AuthConfig: pass
    class SocialAuthManager: pass
    class UploadConfig: pass
    
    # Create dummy functions
    def search_videos(*args, **kwargs): 
        return {"success": False, "error": "Social module not available"}
    
    def get_trending_videos(*args, **kwargs): 
        return {"success": False, "error": "Social module not available"}
    
    def search_channels(*args, **kwargs): 
        return {"success": False, "error": "Social module not available"}
    
    def get_platform_auth_url(*args, **kwargs): 
        return ""
    
    def upload_video(*args, **kwargs): 
        return {"success": False, "error": "Social module not available"}
    
    def get_upload_status(*args, **kwargs): 
        return {"success": False, "error": "Social module not available"}
    
    def update_video_metadata(*args, **kwargs): 
        return {"success": False, "error": "Social module not available"}
    
    def delete_video(*args, **kwargs): 
        return {"success": False, "error": "Social module not available"}

__all__ = [
    'search_videos',
    'get_trending_videos',
    'search_channels',
    'Platform',
    'SearchResult',
    'SocialAuthManager',
    'AuthConfig',
    'AuthStatus',
    'get_platform_auth_url',
    'upload_video',
    'get_upload_status',
    'update_video_metadata',
    'delete_video',
    'UploadConfig'
]
