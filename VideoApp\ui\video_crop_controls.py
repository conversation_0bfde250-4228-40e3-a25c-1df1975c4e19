# -*- coding: utf-8 -*-
"""
Video Crop Controls Widget for selecting video segments during playback
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QPushButton, 
                             QLabel, QSlider, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
import core.theme as theme


class VideoCropControls(QWidget):
    """Widget for controlling video cropping during playback"""
    
    # Signals
    crop_start_set = pyqtSignal(float)  # Start time in seconds
    crop_end_set = pyqtSignal(float)    # End time in seconds
    crop_requested = pyqtSignal(float, float)  # Start time, end time
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.start_time = 0.0
        self.end_time = 0.0
        self.current_position = 0.0
        self.video_duration = 0.0
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        title = QLabel("🎬 Video Cropping Controls")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet(f"color: {theme.TEXT_COLOR}; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Crop controls group
        crop_group = QGroupBox("Crop Selection")
        crop_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {theme.ACCENT_COLOR};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                color: {theme.TEXT_COLOR};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        crop_layout = QVBoxLayout(crop_group)
        
        # Current position display
        self.position_label = QLabel("Current: 00:00")
        self.position_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.ACCENT_COLOR};
                font-family: monospace;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
                background-color: {theme.PANEL_COLOR};
                border-radius: 3px;
            }}
        """)
        crop_layout.addWidget(self.position_label)
        
        # Start crop controls
        start_widget = QWidget()
        start_layout = QHBoxLayout(start_widget)
        start_layout.setContentsMargins(0, 0, 0, 0)
        
        self.start_crop_btn = QPushButton("📍 Set Start")
        self.start_crop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #2e7d32;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #388e3c;
            }}
            QPushButton:pressed {{
                background-color: #1b5e20;
            }}
        """)
        
        self.start_time_label = QLabel("Start: 00:00")
        self.start_time_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.TEXT_COLOR};
                font-family: monospace;
                font-size: 12px;
                padding: 5px;
                min-width: 80px;
            }}
        """)
        
        start_layout.addWidget(self.start_crop_btn)
        start_layout.addWidget(self.start_time_label)
        start_layout.addStretch()
        crop_layout.addWidget(start_widget)
        
        # End crop controls
        end_widget = QWidget()
        end_layout = QHBoxLayout(end_widget)
        end_layout.setContentsMargins(0, 0, 0, 0)
        
        self.end_crop_btn = QPushButton("🏁 Set End")
        self.end_crop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #d32f2f;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #f44336;
            }}
            QPushButton:pressed {{
                background-color: #b71c1c;
            }}
        """)
        
        self.end_time_label = QLabel("End: 00:00")
        self.end_time_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.TEXT_COLOR};
                font-family: monospace;
                font-size: 12px;
                padding: 5px;
                min-width: 80px;
            }}
        """)
        
        end_layout.addWidget(self.end_crop_btn)
        end_layout.addWidget(self.end_time_label)
        end_layout.addStretch()
        crop_layout.addWidget(end_widget)
        
        # Duration display
        self.duration_label = QLabel("Duration: 00:00")
        self.duration_label.setStyleSheet(f"""
            QLabel {{
                color: {theme.ACCENT_COLOR};
                font-family: monospace;
                font-size: 12px;
                font-weight: bold;
                padding: 5px;
                background-color: {theme.BACKGROUND_COLOR};
                border-radius: 3px;
                border: 1px solid {theme.BORDER_COLOR};
            }}
        """)
        crop_layout.addWidget(self.duration_label)
        
        # Crop action button
        self.crop_btn = QPushButton("✂️ Crop & Add to Timeline")
        self.crop_btn.setEnabled(False)
        self.crop_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {theme.ACCENT_COLOR};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {theme.ACCENT_COLOR_DARK};
            }}
            QPushButton:disabled {{
                background-color: #666;
                color: #999;
            }}
        """)
        crop_layout.addWidget(self.crop_btn)
        
        layout.addWidget(crop_group)
        layout.addStretch()
        
        # Connect signals
        self.start_crop_btn.clicked.connect(self.set_start_crop)
        self.end_crop_btn.clicked.connect(self.set_end_crop)
        self.crop_btn.clicked.connect(self.perform_crop)
        
    def set_current_position(self, position_seconds):
        """Update current playback position"""
        self.current_position = position_seconds
        self.position_label.setText(f"Current: {self.format_time(position_seconds)}")
        
    def set_video_duration(self, duration_seconds):
        """Set the total video duration"""
        self.video_duration = duration_seconds
        
    def set_start_crop(self):
        """Set the start time for cropping"""
        self.start_time = self.current_position
        self.start_time_label.setText(f"Start: {self.format_time(self.start_time)}")
        self.crop_start_set.emit(self.start_time)
        self.update_crop_button()
        self.update_duration_display()
        
    def set_end_crop(self):
        """Set the end time for cropping"""
        self.end_time = self.current_position
        self.end_time_label.setText(f"End: {self.format_time(self.end_time)}")
        self.crop_end_set.emit(self.end_time)
        self.update_crop_button()
        self.update_duration_display()
        
    def update_crop_button(self):
        """Enable/disable crop button based on selection"""
        has_valid_selection = (self.start_time < self.end_time and 
                              self.end_time > 0 and 
                              self.start_time >= 0)
        self.crop_btn.setEnabled(has_valid_selection)
        
    def update_duration_display(self):
        """Update the duration display"""
        if self.start_time < self.end_time:
            duration = self.end_time - self.start_time
            self.duration_label.setText(f"Duration: {self.format_time(duration)}")
        else:
            self.duration_label.setText("Duration: --:--")
            
    def perform_crop(self):
        """Emit crop request signal"""
        if self.start_time < self.end_time:
            self.crop_requested.emit(self.start_time, self.end_time)
            
    def reset_crop_selection(self):
        """Reset the crop selection"""
        self.start_time = 0.0
        self.end_time = 0.0
        self.start_time_label.setText("Start: 00:00")
        self.end_time_label.setText("End: 00:00")
        self.duration_label.setText("Duration: 00:00")
        self.crop_btn.setEnabled(False)
        
    def format_time(self, seconds):
        """Format seconds as MM:SS or HH:MM:SS"""
        seconds = int(seconds)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
