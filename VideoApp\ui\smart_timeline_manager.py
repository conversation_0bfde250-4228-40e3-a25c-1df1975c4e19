# -*- coding: utf-8 -*-
"""
Smart Timeline Manager for automatic track assignment and management
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QMessageBox
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
import os
import logging

logger = logging.getLogger(__name__)


class SmartTimelineManager(QWidget):
    """Manages automatic track assignment and clip organization"""
    
    # Signals
    clip_added_to_track = pyqtSignal(str, int, float, float)  # file_path, track_index, start_time, duration
    track_availability_changed = pyqtSignal(int, bool)  # track_index, is_available
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tracks = {}  # track_index: {'clips': [], 'available': True, 'next_position': 0.0}
        self.max_tracks = 10
        self.default_clip_duration = 5.0  # Default duration for clips without known duration
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        title = QLabel("🎬 Smart Timeline Manager")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet("color: #ffffff; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Track status display
        self.track_status_label = QLabel("Tracks: Ready")
        self.track_status_label.setStyleSheet("""
            QLabel {
                color: #cccccc;
                font-family: monospace;
                font-size: 11px;
                padding: 5px;
                background-color: #3c3c3c;
                border-radius: 3px;
                border: 1px solid #555555;
            }
        """)
        layout.addWidget(self.track_status_label)
        
        # Control buttons
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        
        self.clear_tracks_btn = QPushButton("🗑️ Clear All")
        self.clear_tracks_btn.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f44336;
            }
        """)
        
        self.reset_tracks_btn = QPushButton("🔄 Reset")
        self.reset_tracks_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ffa726;
            }
        """)
        
        controls_layout.addWidget(self.clear_tracks_btn)
        controls_layout.addWidget(self.reset_tracks_btn)
        controls_layout.addStretch()
        layout.addWidget(controls_widget)
        
        # Initialize tracks
        self.initialize_tracks()
        
        # Connect signals
        self.clear_tracks_btn.clicked.connect(self.clear_all_tracks)
        self.reset_tracks_btn.clicked.connect(self.reset_track_assignment)
        
    def initialize_tracks(self):
        """Initialize track data structure"""
        for i in range(self.max_tracks):
            self.tracks[i] = {
                'clips': [],
                'available': True,
                'next_position': 0.0,
                'total_duration': 0.0
            }
        self.update_track_status()
        
    def add_clip_smart(self, file_path, title="", duration=None):
        """Add a clip using smart track assignment"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"File does not exist: {file_path}")
                return None
            
            # Use provided duration or default
            clip_duration = duration or self.default_clip_duration
            
            # Find the best track for this clip
            track_index = self.find_best_track()
            
            if track_index is None:
                QMessageBox.warning(self, "No Available Tracks", 
                                  "All tracks are full. Please clear some tracks or reset the timeline.")
                return None
            
            # Calculate start position on the track
            start_position = self.tracks[track_index]['next_position']
            
            # Add clip to track data
            clip_info = {
                'file_path': file_path,
                'title': title or os.path.basename(file_path),
                'start_time': start_position,
                'duration': clip_duration,
                'track_index': track_index
            }
            
            self.tracks[track_index]['clips'].append(clip_info)
            self.tracks[track_index]['next_position'] = start_position + clip_duration
            self.tracks[track_index]['total_duration'] += clip_duration
            
            # Emit signal for timeline widget to add the clip
            self.clip_added_to_track.emit(file_path, track_index, start_position, clip_duration)
            
            # Update status
            self.update_track_status()
            
            logger.info(f"Added clip '{title}' to track {track_index + 1} at {start_position:.1f}s")
            return clip_info
            
        except Exception as e:
            logger.error(f"Error adding clip: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add clip: {str(e)}")
            return None
    
    def find_best_track(self):
        """Find the best available track for a new clip"""
        # Strategy: Use the track with the least total duration
        available_tracks = [(i, track) for i, track in self.tracks.items() 
                           if track['available']]
        
        if not available_tracks:
            return None
        
        # Sort by total duration (ascending)
        available_tracks.sort(key=lambda x: x[1]['total_duration'])
        
        return available_tracks[0][0]
    
    def move_clip_to_track(self, clip_info, new_track_index):
        """Move a clip from one track to another"""
        try:
            old_track_index = clip_info['track_index']
            
            if new_track_index >= self.max_tracks or new_track_index < 0:
                QMessageBox.warning(self, "Invalid Track", f"Track {new_track_index + 1} does not exist.")
                return False
            
            # Remove from old track
            if clip_info in self.tracks[old_track_index]['clips']:
                self.tracks[old_track_index]['clips'].remove(clip_info)
                self.tracks[old_track_index]['total_duration'] -= clip_info['duration']
                self.recalculate_track_positions(old_track_index)
            
            # Add to new track
            new_start_position = self.tracks[new_track_index]['next_position']
            clip_info['track_index'] = new_track_index
            clip_info['start_time'] = new_start_position
            
            self.tracks[new_track_index]['clips'].append(clip_info)
            self.tracks[new_track_index]['next_position'] = new_start_position + clip_info['duration']
            self.tracks[new_track_index]['total_duration'] += clip_info['duration']
            
            # Update status
            self.update_track_status()
            
            logger.info(f"Moved clip '{clip_info['title']}' from track {old_track_index + 1} to track {new_track_index + 1}")
            return True
            
        except Exception as e:
            logger.error(f"Error moving clip: {e}")
            return False
    
    def recalculate_track_positions(self, track_index):
        """Recalculate positions for all clips in a track"""
        track = self.tracks[track_index]
        current_position = 0.0
        
        for clip in track['clips']:
            clip['start_time'] = current_position
            current_position += clip['duration']
        
        track['next_position'] = current_position
    
    def remove_clip(self, clip_info):
        """Remove a clip from its track"""
        try:
            track_index = clip_info['track_index']
            if clip_info in self.tracks[track_index]['clips']:
                self.tracks[track_index]['clips'].remove(clip_info)
                self.tracks[track_index]['total_duration'] -= clip_info['duration']
                self.recalculate_track_positions(track_index)
                self.update_track_status()
                logger.info(f"Removed clip '{clip_info['title']}' from track {track_index + 1}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error removing clip: {e}")
            return False
    
    def clear_track(self, track_index):
        """Clear all clips from a specific track"""
        if track_index in self.tracks:
            self.tracks[track_index]['clips'].clear()
            self.tracks[track_index]['next_position'] = 0.0
            self.tracks[track_index]['total_duration'] = 0.0
            self.tracks[track_index]['available'] = True
            self.update_track_status()
            logger.info(f"Cleared track {track_index + 1}")
    
    def clear_all_tracks(self):
        """Clear all tracks"""
        for track_index in self.tracks:
            self.clear_track(track_index)
        logger.info("Cleared all tracks")
    
    def reset_track_assignment(self):
        """Reset track assignment system"""
        self.clear_all_tracks()
        self.initialize_tracks()
        logger.info("Reset track assignment system")
    
    def update_track_status(self):
        """Update the track status display"""
        used_tracks = sum(1 for track in self.tracks.values() if track['clips'])
        total_clips = sum(len(track['clips']) for track in self.tracks.values())
        
        status_text = f"Tracks Used: {used_tracks}/{self.max_tracks} | Total Clips: {total_clips}"
        self.track_status_label.setText(status_text)
    
    def get_track_info(self, track_index):
        """Get information about a specific track"""
        if track_index in self.tracks:
            return self.tracks[track_index].copy()
        return None
    
    def get_all_clips(self):
        """Get all clips from all tracks"""
        all_clips = []
        for track in self.tracks.values():
            all_clips.extend(track['clips'])
        return all_clips
