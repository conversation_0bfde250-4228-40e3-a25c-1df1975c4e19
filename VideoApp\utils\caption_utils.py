# -*- coding: utf-8 -*-
"""Utilities for handling captions."""

import json
import os
from typing import List, Dict, Tuple
from moviepy.editor import VideoFileClip, TextClip, CompositeVideoClip, ImageClip, ColorClip
import moviepy.editor as mp

def load_captions(caption_path: str) -> List[Dict]:
    """
    Load captions from a file.
    
    Args:
        caption_path: Path to caption file
        
    Returns:
        List of caption dictionaries with text, start, and end times
    """
    try:
        with open(caption_path, 'r', encoding='utf-8') as f:
            if caption_path.lower().endswith('.json'):
                return json.load(f)
            elif caption_path.lower().endswith('.txt'):
                # For text files, create a single caption spanning the first 10 seconds
                txt = f.read()
                return [{"start": 0, "end": 10, "text": txt}]
            else:
                # Implement parsing for other formats if needed
                print(f"Unsupported caption format: {caption_path}")
                return []
    except Exception as e:
        print(f"Error loading captions: {e}")
        return []

def burn_captions(video_path: str, captions: List[Dict], output_path: str, 
                 font_family: str="Arial", font_size: int=48, text_color: str='white', 
                 bg_color: str=None, stroke_color: str='black', stroke_width: int=2) -> str:
    """
    Burn captions onto video.
    
    Args:
        video_path: Path to input video
        captions: List of caption dictionaries
        output_path: Path for output video
        font_family: Font family for captions
        font_size: Font size for captions
        text_color: Text color
        bg_color: Background color (optional)
        stroke_color: Text stroke color
        stroke_width: Text stroke width
        
    Returns:
        Path to output video
    """
    v = VideoFileClip(video_path)
    overlays = []
    
    for c in captions:
        # Create text clip
        txt_clip = TextClip(
            c["text"], 
            font=font_family, 
            fontsize=font_size, 
            color=text_color,
            stroke_color=stroke_color,
            stroke_width=stroke_width,
            method='caption', 
            size=(v.w*0.9, None)
        )
        
        # Add background if specified
        if bg_color:
            bg_width = int(v.w * 0.95)
            bg_height = int(txt_clip.h * 1.2)
            bg_clip = ColorClip(
                size=(bg_width, bg_height), 
                color=bg_color_to_rgb(bg_color)
            ).set_opacity(0.7)
            
            bg_clip = bg_clip.set_position(('center', 'bottom')).set_start(c['start']).set_duration(max(0.1, c['end']-c['start']))
            txt_clip = txt_clip.set_position(('center', 'bottom'))
            overlays.append(bg_clip)
        
        txt_clip = txt_clip.set_position(('center','bottom')).set_start(c['start']).set_duration(max(0.1, c['end']-c['start']))
        overlays.append(txt_clip)
    
    if not output_path:
        output_path = os.path.splitext(video_path)[0] + "_captioned.mp4"
    
    CompositeVideoClip([v, *overlays]).write_videofile(output_path, codec="libx264", audio_codec="aac")
    v.close()
    [o.close() for o in overlays]
    return output_path

def bg_color_to_rgb(color_str: str) -> Tuple[int, int, int]:
    """Convert color string to RGB tuple"""
    color_map = {
        'black': (0, 0, 0),
        'white': (255, 255, 255),
        'red': (255, 0, 0),
        'green': (0, 255, 0),
        'blue': (0, 0, 255),
        'yellow': (255, 255, 0),
        'cyan': (0, 255, 255),
        'magenta': (255, 0, 255),
    }
    return color_map.get(color_str.lower(), (0, 0, 0))

def add_animated_text(video_path: str, text: str, animation_type: str = "fade", 
                     duration: float = 5, position: Tuple = ('center', 'center'), 
                     font_size: int = 48, font_color: str = 'white', 
                     output_path: str = None) -> str:
    """Add animated text to video"""
    if not output_path:
        output_path = os.path.splitext(video_path)[0] + "_animated_text.mp4"
    
    with VideoFileClip(video_path) as video:
        # Create text clip
        txt_clip = TextClip(
            text, 
            fontsize=font_size, 
            color=font_color,
            size=(video.w * 0.8, None),
            method='caption'
        ).set_position(position).set_duration(duration)
        
        # Apply animation
        if animation_type == "fade":
            txt_clip = txt_clip.crossfadein(1).crossfadeout(1)
        elif animation_type == "slide_in":
            # Start off-screen and slide in
            start_pos = (-txt_clip.w, position[1])
            txt_clip = txt_clip.set_position(lambda t: (
                min(position[0], start_pos[0] + t * 200), 
                position[1]
            ))
        elif animation_type == "typewriter":
            # Simulate typewriter effect
            def mask_func(t):
                chars_to_show = int((t / duration) * len(text))
                return text[:chars_to_show]
            
            txt_clip = TextClip(
                mask_func, 
                fontsize=font_size, 
                color=font_color,
                size=(video.w * 0.8, None),
                method='caption'
            ).set_position(position).set_duration(duration)
        
        # Composite with video
        final = CompositeVideoClip([video, txt_clip])
        final.write_videofile(output_path, codec="libx264", audio_codec="aac")
        
        txt_clip.close()
        final.close()
    
    return output_path

def create_text_animation(text: str, duration: float, **kwargs) -> TextClip:
    """Create an animated text clip with various effects"""
    font_size = kwargs.get('font_size', 48)
    font_color = kwargs.get('font_color', 'white')
    bg_color = kwargs.get('bg_color', None)
    animation = kwargs.get('animation', 'fade')
    position = kwargs.get('position', ('center', 'center'))
    
    # Create base text clip
    txt_clip = TextClip(
        text, 
        fontsize=font_size, 
        color=font_color,
        method='caption'
    ).set_duration(duration).set_position(position)
    
    # Apply animation
    if animation == 'fade':
        txt_clip = txt_clip.fadein(1).fadeout(1)
    elif animation == 'slide':
        # Slide in from left, slide out to right
        # Note: This needs a video reference for proper positioning
        # For now, using a fixed approach
        txt_clip = txt_clip.set_position(lambda t: (
            min(position[0], -len(text)*10 + t * (800 + len(text)*10) / duration),
            position[1]
        ))
    
    return txt_clip
