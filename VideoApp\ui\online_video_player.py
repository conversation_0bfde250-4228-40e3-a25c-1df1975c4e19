# -*- coding: utf-8 -*-
"""
Enhanced Online Video Player Widget with multiple fallback strategies
"""

import os
import re
import logging
import webbrowser
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QMessageBox
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QTimer
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

# Try to import video widget with fallback
try:
    from PyQt5.QtMultimediaWidgets import QVideoWidget
    VIDEO_WIDGET_AVAILABLE = True
except ImportError:
    print("⚠️ QVideoWidget not available - using fallback")
    QVideoWidget = None
    VIDEO_WIDGET_AVAILABLE = False

# Try to import web engine with fallback
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    WEB_ENGINE_AVAILABLE = True
except ImportError:
    print("⚠️ QWebEngineView not available - using fallback")
    QWebEngineView = None
    WEB_ENGINE_AVAILABLE = False

import yt_dlp

# Set up logging
logger = logging.getLogger(__name__)

class OnlineVideoPlayer(QWidget):
    """Enhanced video player specifically for online content with multiple fallback strategies"""
    
    # Signals
    error_occurred = pyqtSignal(str)
    playback_started = pyqtSignal()
    status_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_video_url = None
        self.current_video_info = {}
        self.is_fallback_triggered = False
        self.setup_ui()
        self.setup_player()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # Video widget for direct streams
        if VIDEO_WIDGET_AVAILABLE:
            self.video_widget = QVideoWidget(self)
        else:
            self.video_widget = QLabel("Video playback not available\n(QVideoWidget missing)")
            self.video_widget.setAlignment(Qt.AlignCenter)
            self.video_widget.setStyleSheet("background-color: black; color: white; font-size: 14px;")
        
        # Web view for embedded content
        if WEB_ENGINE_AVAILABLE:
            self.webview = QWebEngineView(self)
        else:
            self.webview = QLabel("Web video playback not available\n(QWebEngineView missing)")
            self.webview.setAlignment(Qt.AlignCenter)
            self.webview.setStyleSheet("background-color: black; color: white; font-size: 14px;")
        
        # Add both widgets to layout
        self.layout.addWidget(self.video_widget)
        self.layout.addWidget(self.webview)
        
        # Initially hide web view
        self.webview.hide()
        
    def setup_player(self):
        """Setup the media player"""
        self.player = QMediaPlayer(self)
        
        if VIDEO_WIDGET_AVAILABLE:
            self.player.setVideoOutput(self.video_widget)
        
        # Set default volume
        self.player.setVolume(80)
        
        # Connect signals
        self.player.error.connect(self.handle_player_error)
        self.player.stateChanged.connect(self.on_state_changed)
        self.player.mediaStatusChanged.connect(lambda status: logger.info(f"Media status: {status}"))
        
    def load_video(self, video_info):
        """Load video with enhanced handling for different platforms"""
        try:
            self.current_video_info = video_info.copy()
            url = video_info.get('playable_url') or video_info.get('url', '')
            
            if not url:
                self.error_occurred.emit("No valid URL provided")
                return
            
            self.current_video_url = url
            
            # Determine playback strategy based on URL and platform
            platform = video_info.get('platform', '').lower()
            playback_method = video_info.get('playback_method', '')
            
            logger.info(f"🎬 Loading video: {video_info.get('title', 'Unknown')}")
            logger.info(f"📍 Platform: {platform}, Method: {playback_method}")
            
            if 'youtube' in platform or 'youtube.com' in url or 'youtu.be' in url:
                self._handle_youtube_video(url, video_info)
            elif playback_method == 'direct':
                self._handle_direct_video(url, video_info)
            elif playback_method == 'web':
                self._handle_web_video(url, video_info)
            else:
                # Auto-detect best method
                self._auto_detect_playback_method(url, video_info)
                
        except Exception as e:
            logger.error(f"Video loading error: {e}")
            self.error_occurred.emit(f"Failed to load video: {str(e)}")
    
    def _handle_youtube_video(self, url, video_info):
        """Handle YouTube with webview-first strategy"""
        try:
            logger.info("🌐 Using webview for YouTube (skipping unreliable direct stream)")
            if WEB_ENGINE_AVAILABLE:
                self._use_webview_for_youtube(url)
            else:
                logger.warning("WebEngine unavailable - opening in browser")
                webbrowser.open(url)
                self.status_changed.emit("🌐 Opened in browser")
        except Exception as e:
            logger.error(f"YouTube handling failed: {e}")
            self.error_occurred.emit(f"YouTube error: {str(e)}")
    
    def _extract_youtube_stream(self, url):
        """Extract direct stream URL from YouTube with better error handling"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'format': 'best[ext=mp4][height<=720]/best[ext=mp4]/best',
                'extractaudio': False,
                'noplaylist': True,
                'socket_timeout': 15,
                'retries': 3,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                return info.get('url')
                
        except Exception as e:
            logger.warning(f"Stream extraction failed: {e}")
            return None
    
    def _load_direct_stream(self, stream_url, video_info):
        """Load direct stream URL with proper validation"""
        try:
            # Validate the stream URL
            if not stream_url.startswith(('http://', 'https://')):
                raise ValueError("Invalid stream URL format")
            
            # Create media content
            media_content = QMediaContent(QUrl(stream_url))
            if media_content.isNull():
                raise ValueError("Failed to create media content from stream")
            
            # Set media and prepare for playback
            self.player.setMedia(media_content)
            self.current_video_url = stream_url
            
            # Show video widget, hide web view
            self._show_video_widget()
            
            # Auto-play after short delay
            QTimer.singleShot(1000, self.play)
            
            self.status_changed.emit(f"▶️ Loading: {video_info.get('title', 'Video')}")
            QTimer.singleShot(2000, lambda: logger.info(f"Post-load state: {self.player.state()}, mediaStatus: {self.player.mediaStatus()}, error: {self.player.errorString()}"))
            
        except Exception as e:
            logger.error(f"Direct stream loading failed: {e}")
            raise
    
    def _use_webview_for_youtube(self, url):
        """Use web view for YouTube embedding"""
        if not WEB_ENGINE_AVAILABLE:
            raise RuntimeError("Web engine not available")
        
        # Show web view, hide video widget
        self._show_webview()
        
        # Load YouTube embed URL for better compatibility
        video_id = self._extract_youtube_id(url)
        if video_id:
            embed_url = f"https://www.youtube.com/embed/{video_id}?autoplay=1"
            self.webview.setUrl(QUrl(embed_url))
        else:
            self.webview.setUrl(QUrl(url))
        
        self.status_changed.emit("🌐 Loading in web player...")
    
    def _extract_youtube_id(self, url):
        """Extract YouTube video ID from URL"""
        patterns = [
            r'(?:youtube\.com\/watch\?v=)([^&]+)',
            r'(?:youtu\.be\/)([^?]+)',
            r'(?:youtube\.com\/embed\/)([^\/]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _handle_direct_video(self, url, video_info):
        """Handle direct video file URLs"""
        try:
            media_content = QMediaContent(QUrl(url))
            self.player.setMedia(media_content)
            self._show_video_widget()
            self.play()
            self.status_changed.emit(f"▶️ Playing: {video_info.get('title', 'Video')}")
        except Exception as e:
            logger.error(f"Direct video playback failed: {e}")
            raise
    
    def _handle_web_video(self, url, video_info):
        """Handle web-based video content"""
        if WEB_ENGINE_AVAILABLE:
            self._show_webview()
            self.webview.setUrl(QUrl(url))
            self.status_changed.emit(f"🌐 Loading: {video_info.get('title', 'Video')}")
        else:
            webbrowser.open(url)
            self.status_changed.emit("🌐 Opened in browser")
    
    def _auto_detect_playback_method(self, url, video_info):
        """Auto-detect the best playback method for the URL"""
        try:
            # Check if it's a direct video file
            if any(url.lower().endswith(ext) for ext in ['.mp4', '.webm', '.avi', '.mov', '.mkv']):
                self._handle_direct_video(url, video_info)
            else:
                # Try web view for other content
                self._handle_web_video(url, video_info)
        except Exception as e:
            logger.error(f"Auto-detection failed: {e}")
            # Final fallback to browser
            webbrowser.open(url)
            self.status_changed.emit("🌐 Opened in browser")
    
    def _show_video_widget(self):
        """Show video widget and hide web view"""
        self.video_widget.show()
        self.webview.hide()
    
    def _show_webview(self):
        """Show web view and hide video widget"""
        self.player.stop()
        self.video_widget.hide()
        self.webview.show()
    
    def play(self):
        """Start playback"""
        if self.player.state() != QMediaPlayer.PlayingState:
            self.player.play()
    
    def pause(self):
        """Pause playback"""
        if self.player.state() == QMediaPlayer.PlayingState:
            self.player.pause()
    
    def stop(self):
        """Stop playback"""
        self.player.stop()

    def set_volume(self, volume):
        """Set volume (0-100)"""
        self.player.setVolume(max(0, min(100, volume)))
    
    def handle_player_error(self, error):
        """Handle player errors with detailed messages"""
        error_messages = {
            QMediaPlayer.ResourceError: "Resource error - Cannot access the media file",
            QMediaPlayer.FormatError: "Format error - Unsupported media format",
            QMediaPlayer.NetworkError: "Network error - Check your internet connection",
            QMediaPlayer.AccessDeniedError: "Access denied - Check permissions",
            QMediaPlayer.ServiceMissingError: "Service missing - Media service not available",
        }
        
        error_msg = error_messages.get(error, f"Media error: {self.player.errorString()}")
        logger.error(f"Player error: {error_msg}")
        self.error_occurred.emit(error_msg)
    
    def on_state_changed(self, state):
        """Handle player state changes"""
        logger.info(f"State: {state}, MediaStatus: {self.player.mediaStatus()}")
        if state == QMediaPlayer.PlayingState:
            self.playback_started.emit()
        elif state == QMediaPlayer.StoppedState:
            if self.player.mediaStatus() in [QMediaPlayer.InvalidMedia, QMediaPlayer.NoMedia, QMediaPlayer.BufferingMedia]:
                logger.warning("Invalid/Buffering media - fallback")
                QTimer.singleShot(100, self._trigger_fallback)
            else:
                # New: If not playing after load, force fallback after timeout
                if self.player.mediaStatus() == QMediaPlayer.LoadedMedia and not self.is_fallback_triggered:
                    self.is_fallback_triggered = True
                    QTimer.singleShot(3000, lambda: self._force_fallback_if_stuck())
    
    def _force_fallback_if_stuck(self):
        if self.player.state() == QMediaPlayer.StoppedState:
            logger.warning("Stuck in stopped state - forcing fallback")
            self._trigger_fallback()
    
    def _trigger_fallback(self):
        """Trigger fallback playback method"""
        if self.current_video_url:
            logger.info("🔄 Attempting fallback playback...")
            if WEB_ENGINE_AVAILABLE:
                self._use_webview_for_youtube(self.current_video_url)
            else:
                webbrowser.open(self.current_video_url)
            self.status_changed.emit("🌐 Opened in web player (fallback)" if WEB_ENGINE_AVAILABLE else "🌐 Opened in browser (fallback)")
