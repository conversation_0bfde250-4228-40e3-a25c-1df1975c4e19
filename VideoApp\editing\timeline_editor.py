# -*- coding: utf-8 -*-
# Timeline-based video editing functionality

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json

class ClipType(Enum):
    VIDEO = "video"
    AUDIO = "audio"
    TEXT = "text"
    IMAGE = "image"
    EFFECT = "effect"

@dataclass
class TimelineClip:
    type: ClipType
    source: str  # Path to media file or text content
    start_time: float  # Start time in timeline (seconds)
    duration: float  # Duration in timeline (seconds)
    layer: int = 0  # Layer/z-index
    config: Optional[Dict] = None  # Additional configuration

class TimelineEditor:
    def __init__(self):
        self.clips = []
        self.total_duration = 0
    
    def add_clip(self, clip: TimelineClip) -> 'TimelineEditor':
        """
        Add a clip to the timeline.
        
        Args:
            clip: The clip to add
            
        Returns:
            Self for method chaining
        """
        self.clips.append(clip)
        # Update total duration if this clip extends beyond current end
        clip_end = clip.start_time + clip.duration
        if clip_end > self.total_duration:
            self.total_duration = clip_end
        return self
    
    def remove_clip(self, index: int) -> 'TimelineEditor':
        """
        Remove a clip from the timeline by index.
        
        Args:
            index: Index of the clip to remove
            
        Returns:
            Self for method chaining
        """
        if 0 <= index < len(self.clips):
            self.clips.pop(index)
            # Recalculate total duration
            self._recalculate_duration()
        return self
    
    def _recalculate_duration(self):
        """Recalculate the total duration of the timeline."""
        self.total_duration = 0
        for clip in self.clips:
            clip_end = clip.start_time + clip.duration
            if clip_end > self.total_duration:
                self.total_duration = clip_end
    
    def get_timeline_json(self) -> str:
        """
        Get the timeline as JSON for serialization.
        
        Returns:
            JSON string representation of the timeline
        """
        timeline_data = {
            "clips": [{
                "type": clip.type.value,
                "source": clip.source,
                "start_time": clip.start_time,
                "duration": clip.duration,
                "layer": clip.layer,
                "config": clip.config or {}
            } for clip in self.clips],
            "total_duration": self.total_duration
        }
        return json.dumps(timeline_data, indent=2)
    
    def load_timeline_json(self, json_str: str) -> 'TimelineEditor':
        """
        Load a timeline from JSON.
        
        Args:
            json_str: JSON string representation of the timeline
            
        Returns:
            Self for method chaining
        """
        timeline_data = json.loads(json_str)
        self.clips = []
        for clip_data in timeline_data.get("clips", []):
            self.clips.append(TimelineClip(
                type=ClipType(clip_data["type"]),
                source=clip_data["source"],
                start_time=clip_data["start_time"],
                duration=clip_data["duration"],
                layer=clip_data.get("layer", 0),
                config=clip_data.get("config", {})
            ))
        self.total_duration = timeline_data.get("total_duration", 0)
        return self
    
    def render_timeline(self, output_path: str) -> Dict[str, Any]:
        """
        Render the timeline to a video file.
        
        Args:
            output_path: Path for the output video
            
        Returns:
            Dictionary with operation status and output file information
        """
        try:
            from utils.ffmpeg_wrapper import ffmpeg_render_timeline
            
            # Convert clips to dictionary format for FFmpeg
            clip_configs = []
            for clip in self.clips:
                clip_configs.append({
                    "type": clip.type.value,
                    "source": clip.source,
                    "start_time": clip.start_time,
                    "duration": clip.duration,
                    "layer": clip.layer,
                    "config": clip.config or {}
                })
            
            result = ffmpeg_render_timeline(clip_configs, output_path, self.total_duration)
            
            if result["success"]:
                return {
                    "success": True,
                    "output_path": output_path,
                    "clip_count": len(self.clips),
                    "total_duration": self.total_duration
                }
            else:
                return result
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to render timeline: {str(e)}",
                "clip_count": len(self.clips)
            }
    
    def export_edl(self, output_path: str) -> Dict[str, Any]:
        """
        Export timeline as Edit Decision List (EDL).
        
        Args:
            output_path: Path for the EDL file
            
        Returns:
            Dictionary with operation status
        """
        try:
            edl_content = "TITLE: VideoApp Timeline Export\n"
            edl_content += "FCM: NON-DROP FRAME\n\n"
            
            for i, clip in enumerate(self.clips, 1):
                edl_content += f"{i:03d}  AX       V     C        "
                edl_content += f"{clip.start_time:.3f} {clip.start_time + clip.duration:.3f} "
                edl_content += f"{0:.3f} {clip.duration:.3f}\n"
                edl_content += f"* FROM CLIP NAME: {clip.source}\n"
                if clip.config:
                    edl_content += f"* CONFIG: {json.dumps(clip.config)}\n"
                edl_content += "\n"
            
            with open(output_path, 'w') as f:
                f.write(edl_content)
            
            return {
                "success": True,
                "output_path": output_path,
                "clip_count": len(self.clips)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to export EDL: {str(e)}"
            }

def create_video_from_clips(
    video_clips: List[Dict[str, Any]],
    output_path: str,
    transition: str = "fade",
    transition_duration: float = 0.5
) -> Dict[str, Any]:
    """
    Create a video by concatenating multiple clips with transitions.
    
    Args:
        video_clips: List of video clip information
        output_path: Path for the output video
        transition: Type of transition between clips
        transition_duration: Duration of transitions (seconds)
        
    Returns:
        Dictionary with operation status and output file information
    """
    try:
        from utils.ffmpeg_wrapper import ffmpeg_concat_videos
        
        result = ffmpeg_concat_videos(
            video_clips, 
            output_path, 
            transition, 
            transition_duration
        )
        
        if result["success"]:
            return {
                "success": True,
                "output_path": output_path,
                "clip_count": len(video_clips),
                "transition": transition
            }
        else:
            return result
            
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to create video from clips: {str(e)}",
            "clip_count": len(video_clips)
        }
