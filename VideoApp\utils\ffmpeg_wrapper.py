# -*- coding: utf-8 -*-
"""
FFmpeg wrapper for video editing operations.
Handles trimming, merging, format conversion, and other video processing tasks.
"""

import subprocess
import os
import shutil
import json
import tempfile
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FFmpegWrapper:
    """Wrapper class for FFmpeg operations with error handling and progress tracking."""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg", ffprobe_path: str = "ffprobe"):
        """
        Initialize the FFmpeg wrapper.
        
        Args:
            ffmpeg_path: Path to ffmpeg executable
            ffprobe_path: Path to ffprobe executable
        """
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.check_ffmpeg_available()
    
    def check_ffmpeg_available(self) -> bool:
        """Check if <PERSON>mpeg and <PERSON><PERSON><PERSON> are available."""
        try:
            subprocess.run([self.ffmpeg_path, "-version"], 
                         capture_output=True, check=True)
            subprocess.run([self.ffprobe_path, "-version"], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise RuntimeError(
                "FFmpeg or FFprobe not found. Please install FFmpeg and ensure "
                "it's in your PATH, or provide the full path to the executables."
            )
    
    def get_video_info(self, input_path: str) -> Dict:
        """
        Get detailed information about a video file using ffprobe.
        
        Args:
            input_path: Path to the video file
            
        Returns:
            Dictionary containing video metadata
        """
        cmd = [
            self.ffprobe_path,
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams",
            input_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return json.loads(result.stdout)
        except subprocess.CalledProcessError as e:
            logger.error(f"FFprobe error: {e.stderr}")
            raise RuntimeError(f"Failed to get video info: {e.stderr}")
    
    def get_duration(self, input_path: str) -> float:
        """Get video duration in seconds."""
        info = self.get_video_info(input_path)
        return float(info['format']['duration'])
    
    def get_resolution(self, input_path: str) -> Tuple[int, int]:
        """Get video resolution (width, height)."""
        info = self.get_video_info(input_path)
        for stream in info['streams']:
            if stream['codec_type'] == 'video':
                return (int(stream['width']), int(stream['height']))
        raise RuntimeError("No video stream found")
    
    def trim_video(self, input_path: str, output_path: str, 
                  start_time: float, end_time: float) -> bool:
        """
        Trim a video from start_time to end_time.
        
        Args:
            input_path: Input video file path
            output_path: Output video file path
            start_time: Start time in seconds
            end_time: End time in seconds
            
        Returns:
            True if successful, False otherwise
        """
        duration = end_time - start_time
        
        cmd = [
            self.ffmpeg_path,
            "-i", input_path,
            "-ss", str(start_time),
            "-t", str(duration),
            "-c", "copy",  # Copy codecs (no re-encoding)
            "-y",  # Overwrite output file
            output_path
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            logger.info(f"Trimmed video saved to: {output_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Trim error: {e.stderr}")
            return False
    
    def merge_videos(self, input_paths: List[str], output_path: str) -> bool:
        """
        Merge multiple videos into one.
        
        Args:
            input_paths: List of input video file paths
            output_path: Output video file path
            
        Returns:
            True if successful, False otherwise
        """
        # Create temporary file list
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            for path in input_paths:
                f.write(f"file '{os.path.abspath(path)}'\n")
            list_path = f.name
        
        try:
            cmd = [
                self.ffmpeg_path,
                "-f", "concat",
                "-safe", "0",
                "-i", list_path,
                "-c", "copy",  # Copy codecs
                "-y",
                output_path
            ]
            
            subprocess.run(cmd, capture_output=True, check=True)
            logger.info(f"Merged video saved to: {output_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Merge error: {e.stderr}")
            return False
        finally:
            # Clean up temporary file
            os.unlink(list_path)
    
    def convert_format(self, input_path: str, output_path: str, 
                      output_format: str = "mp4") -> bool:
        """
        Convert video to different format.
        
        Args:
            input_path: Input video file path
            output_path: Output video file path
            output_format: Output format (mp4, avi, mov, etc.)
            
        Returns:
            True if successful, False otherwise
        """
        cmd = [
            self.ffmpeg_path,
            "-i", input_path,
            "-y",
            output_path
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            logger.info(f"Converted video saved to: {output_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Conversion error: {e.stderr}")
            return False
    
    def extract_audio(self, input_path: str, output_path: str) -> bool:
        """
        Extract audio from video.
        
        Args:
            input_path: Input video file path
            output_path: Output audio file path
            
        Returns:
            True if successful, False otherwise
        """
        cmd = [
            self.ffmpeg_path,
            "-i", input_path,
            "-vn",  # No video
            "-acodec", "copy",  # Copy audio codec
            "-y",
            output_path
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            logger.info(f"Audio extracted to: {output_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Audio extraction error: {e.stderr}")
            return False
    
    def add_subtitles(self, input_path: str, subtitle_path: str, 
                     output_path: str) -> bool:
        """
        Add subtitles to video.
        
        Args:
            input_path: Input video file path
            subtitle_path: Subtitle file path (srt, ass, etc.)
            output_path: Output video file path
            
        Returns:
            True if successful, False otherwise
        """
        cmd = [
            self.ffmpeg_path,
            "-i", input_path,
            "-i", subtitle_path,
            "-c", "copy",  # Copy both video and audio
            "-c:s", "mov_text",  # Subtitle codec
            "-metadata:s:s:0", "language=eng",
            "-y",
            output_path
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            logger.info(f"Video with subtitles saved to: {output_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Subtitle error: {e.stderr}")
            return False
    
    def adjust_volume(self, input_path: str, output_path: str, 
                     volume_factor: float) -> bool:
        """
        Adjust video audio volume.
        
        Args:
            input_path: Input video file path
            output_path: Output video file path
            volume_factor: Volume multiplier (2.0 = double volume, 0.5 = half volume)
            
        Returns:
            True if successful, False otherwise
        """
        cmd = [
            self.ffmpeg_path,
            "-i", input_path,
            "-af", f"volume={volume_factor}",
            "-c:v", "copy",  # Copy video
            "-y",
            output_path
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            logger.info(f"Volume adjusted video saved to: {output_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Volume adjustment error: {e.stderr}")
            return False
    
    def create_thumbnail(self, input_path: str, output_path: str, 
                        time_seconds: float = 0) -> bool:
        """
        Create thumbnail from video at specific time.
        
        Args:
            input_path: Input video file path
            output_path: Output image file path
            time_seconds: Time in seconds to capture thumbnail
            
        Returns:
            True if successful, False otherwise
        """
        cmd = [
            self.ffmpeg_path,
            "-i", input_path,
            "-ss", str(time_seconds),
            "-vframes", "1",  # Capture one frame
            "-y",
            output_path
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            logger.info(f"Thumbnail saved to: {output_path}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Thumbnail error: {e.stderr}")
            return False
    
    def get_video_duration(self, input_path: str) -> float:
        """Get video duration in seconds using ffprobe."""
        info = self.get_video_info(input_path)
        return float(info['format']['duration'])
    
    def get_video_codec(self, input_path: str) -> str:
        """Get video codec information."""
        info = self.get_video_info(input_path)
        for stream in info['streams']:
            if stream['codec_type'] == 'video':
                return stream['codec_name']
        return "Unknown"


# Utility functions for common operations
def trim_video(input_path: str, output_path: str, start_time: float, end_time: float) -> bool:
    """Trim video utility function."""
    ffmpeg = FFmpegWrapper()
    return ffmpeg.trim_video(input_path, output_path, start_time, end_time)


def merge_videos(input_paths: List[str], output_path: str) -> bool:
    """Merge videos utility function."""
    ffmpeg = FFmpegWrapper()
    return ffmpeg.merge_videos(input_paths, output_path)


def convert_video(input_path: str, output_path: str, output_format: str = "mp4") -> bool:
    """Convert video format utility function."""
    ffmpeg = FFmpegWrapper()
    return ffmpeg.convert_format(input_path, output_path, output_format)


def extract_audio(input_path: str, output_path: str) -> bool:
    """Extract audio utility function."""
    ffmpeg = FFmpegWrapper()
    return ffmpeg.extract_audio(input_path, output_path)


def get_video_info(input_path: str) -> Dict:
    """Get video info utility function."""
    ffmpeg = FFmpegWrapper()
    return ffmpeg.get_video_info(input_path)


def check_ffmpeg_available() -> Dict:
    """
    Check if FFmpeg is available and return status information.
    
    Returns:
        Dictionary with availability status and information
    """
    try:
        ffmpeg = FFmpegWrapper()
        return {
            "available": True,
            "message": "FFmpeg is installed and available",
            "ffmpeg_path": ffmpeg.ffmpeg_path,
            "ffprobe_path": ffmpeg.ffprobe_path
        }
    except RuntimeError as e:
        return {
            "available": False,
            "message": str(e),
            "installation_instructions": {
                "Windows": "Download from https://ffmpeg.org/download.html#build-windows",
                "macOS": "brew install ffmpeg",
                "Linux": "sudo apt install ffmpeg"
            }
        }


# Original functions from the first file
def check_ffmpeg_installed() -> Dict[str, Any]:
    """
    Check if FFmpeg is installed and accessible.

    Returns:
        Dictionary with installation status and path if available
    """
    ffmpeg_path = shutil.which('ffmpeg')

    if ffmpeg_path:
        try:
            result = subprocess.run(
                [ffmpeg_path, '-version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                # Extract version info
                version_line = result.stdout.split('\n')[0] if result.stdout else ''
                return {
                    "installed": True,
                    "path": ffmpeg_path,
                    "version": version_line,
                    "message": "FFmpeg is installed and working correctly"
                }
        except:
            pass

    # FFmpeg not found or not working
    return {
        "installed": False,
        "path": None,
        "version": None,
        "message": "FFmpeg is not installed or not in PATH. Please install FFmpeg to use video editing features."
    }


def get_ffmpeg_path() -> str:
    """
    Get the path to the FFmpeg executable.

    Returns:
        Path to FFmpeg or 'ffmpeg' if not found (will rely on PATH)
    """
    ffmpeg_path = shutil.which('ffmpeg')
    return ffmpeg_path or 'ffmpeg'


def run_ffmpeg_command(args: List[str]) -> Dict[str, Any]:
    """
    Run an FFmpeg command and return the result.

    Args:
        args: List of FFmpeg arguments
        
    Returns:
        Dictionary with command execution status
    """
    # Check if FFmpeg is installed first
    ffmpeg_check = check_ffmpeg_installed()
    if not ffmpeg_check["installed"]:
        return {
            "success": False,
            "error": ffmpeg_check["message"],
            "ffmpeg_installed": False
        }

    try:
        ffmpeg_path = get_ffmpeg_path()
        result = subprocess.run(
            [ffmpeg_path] + args,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            return {
                "success": True,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "ffmpeg_installed": True
            }
        else:
            return {
                "success": False,
                "error": result.stderr,
                "returncode": result.returncode,
                "ffmpeg_installed": True
            }
            
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "error": "FFmpeg command timed out",
            "ffmpeg_installed": True
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"FFmpeg command failed: {str(e)}",
            "ffmpeg_installed": True
        }


def ffmpeg_add_text(
    input_path: str,
    text: str,
    output_path: str,
    x: Any = "(w-text_w)/2",
    y: Any = "h-text_h-50",
    font_config: str = "font=Arial:fontsize=24:fontcolor=white",
    start_time: float = 0,
    duration: Optional[float] = None
) -> Dict[str, Any]:
    """
    Add text to a video using FFmpeg.

    Args:
        input_path: Input video path
        text: Text to add
        output_path: Output video path
        x: X position of text
        y: Y position of text
        font_config: Font configuration string
        start_time: When to start showing text
        duration: How long to show text
        
    Returns:
        Dictionary with operation status
    """
    # Escape text for FFmpeg filter
    text = text.replace(":", "\\:").replace("'", "\\'")

    # Build filter complex
    if duration is not None:
        filter_str = f"drawtext={font_config}:text='{text}':x={x}:y={y}:enable='between(t,{start_time},{start_time + duration})'"
    else:
        filter_str = f"drawtext={font_config}:text='{text}':x={x}:y={y}:enable='gte(t,{start_time})'"

    args = [
        "-i", input_path,
        "-vf", filter_str,
        "-c:a", "copy",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_add_image(
    input_path: str,
    image_path: str,
    output_path: str,
    x: Any = 50,
    y: Any = 50,
    size: Optional[Tuple[int, int]] = None,
    opacity: float = 1.0,
    start_time: float = 0,
    duration: Optional[float] = None
) -> Dict[str, Any]:
    """
    Add image overlay to a video using FFmpeg.

    Args:
        input_path: Input video path
        image_path: Image path to overlay
        output_path: Output video path
        x: X position of image
        y: Y position of image
        size: Size of image (width, height)
        opacity: Opacity of image (0.0 to 1.0)
        start_time: When to start showing image
        duration: How long to show image
        
    Returns:
        Dictionary with operation status
    """
    # Build filter complex
    size_str = ""
    if size:
        size_str = f":overlay_w={size[0]}:overlay_h={size[1]}"

    if duration is not None:
        enable_str = f":enable='between(t,{start_time},{start_time + duration})'"
    else:
        enable_str = f":enable='gte(t,{start_time})'"

    filter_str = f"movie={image_path}[img];[0][img]overlay={x}:{y}{size_str}:alpha={opacity}{enable_str}"

    args = [
        "-i", input_path,
        "-filter_complex", filter_str,
        "-c:a", "copy",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_extract_audio(
    input_path: str,
    output_path: str,
    format: str = "mp3"
) -> Dict[str, Any]:
    """
    Extract audio from a video using FFmpeg.

    Args:
        input_path: Input video path
        output_path: Output audio path
        format: Output audio format
        
    Returns:
        Dictionary with operation status
    """
    args = [
        "-i", input_path,
        "-vn",  # No video
        "-acodec", "libmp3lame" if format == "mp3" else "copy",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_replace_audio(
    video_path: str,
    audio_path: str,
    output_path: str
) -> Dict[str, Any]:
    """
    Replace audio in a video using FFmpeg.

    Args:
        video_path: Input video path
        audio_path: New audio path
        output_path: Output video path
        
    Returns:
        Dictionary with operation status
    """
    args = [
        "-i", video_path,
        "-i", audio_path,
        "-c:v", "copy",
        "-map", "0:v:0",
        "-map", "1:a:0",
        "-shortest",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_mix_audio(
    track_configs: List[Dict[str, Any]],
    output_path: str,
    format: str = "mp3"
) -> Dict[str, Any]:
    """
    Mix multiple audio tracks using FFmpeg.

    Args:
        track_configs: List of audio track configurations
        output_path: Output audio path
        format: Output audio format
        
    Returns:
        Dictionary with operation status
    """
    # This is a simplified implementation
    # In a real implementation, you would need to create a complex filter graph

    if not track_configs:
        return {
            "success": False,
            "error": "No audio tracks provided"
        }

    # For now, just use the first track
    first_track = track_configs[0]
    args = [
        "-i", first_track["path"],
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_add_audio(
    video_path: str,
    audio_path: str,
    output_path: str,
    audio_volume: float = 1.0,
    original_audio_volume: float = 0.5
) -> Dict[str, Any]:
    """
    Add audio to a video while keeping original audio using FFmpeg.

    Args:
        video_path: Input video path
        audio_path: Additional audio path
        output_path: Output video path
        audio_volume: Volume of additional audio
        original_audio_volume: Volume of original audio
        
    Returns:
        Dictionary with operation status
    """
    # This would normally use a complex filter to mix audio
    # For now, just replace the audio

    args = [
        "-i", video_path,
        "-i", audio_path,
        "-filter_complex", f"[0:a]volume={original_audio_volume}[a0];[1:a]volume={audio_volume}[a1];[a0][a1]amix=inputs=2:duration=longest[a]",
        "-map", "0:v",
        "-map", "[a]",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_freeze_frame(
    video_path: str,
    timestamp: float,
    duration: float,
    output_path: str,
    transition: str = "fade",
    transition_duration: float = 0.5
) -> Dict[str, Any]:
    """
    Freeze a frame in a video using FFmpeg.

    Args:
        video_path: Input video path
        timestamp: Time to freeze
        duration: Duration of freeze
        output_path: Output video path
        transition: Transition type
        transition_duration: Transition duration
        
    Returns:
        Dictionary with operation status
    """
    # Extract frame at timestamp
    frame_path = os.path.join(tempfile.gettempdir(), "freeze_frame.jpg")
    frame_args = [
        "-ss", str(timestamp),
        "-i", video_path,
        "-vframes", "1",
        "-y",  # Overwrite output file
        frame_path
    ]

    frame_result = run_ffmpeg_command(frame_args)
    if not frame_result["success"]:
        return frame_result

    # Create freeze video segment
    freeze_path = os.path.join(tempfile.gettempdir(), "freeze_segment.mp4")
    freeze_args = [
        "-loop", "1",
        "-i", frame_path,
        "-t", str(duration),
        "-c:v", "libx264",
        "-y",  # Overwrite output file
        freeze_path
    ]

    freeze_result = run_ffmpeg_command(freeze_args)
    if not freeze_result["success"]:
        return freeze_result

    # Concatenate original video with freeze segment
    concat_list = os.path.join(tempfile.gettempdir(), "concat_list.txt")
    with open(concat_list, 'w') as f:
        f.write(f"file '{os.path.abspath(video_path)}'\n")
        f.write(f"file '{os.path.abspath(freeze_path)}'\n")

    concat_args = [
        "-f", "concat",
        "-safe", "0",
        "-i", concat_list,
        "-c", "copy",
        "-y",  # Overwrite output file
        output_path
    ]

    result = run_ffmpeg_command(concat_args)

    # Clean up temporary files
    try:
        os.remove(frame_path)
        os.remove(freeze_path)
        os.remove(concat_list)
    except:
        pass

    return result


def ffmpeg_slow_motion(
    video_path: str,
    start_time: float,
    end_time: float,
    speed_factor: float,
    output_path: str
) -> Dict[str, Any]:
    """
    Create slow motion effect using FFmpeg.

    Args:
        video_path: Input video path
        start_time: Start time of slow motion
        end_time: End time of slow motion
        speed_factor: Speed factor (0.5 = half speed)
        output_path: Output video path
        
    Returns:
        Dictionary with operation status
    """
    # For video and audio
    video_filter = f"setpts={1/speed_factor}*PTS"
    audio_filter = f"atempo={speed_factor}"

    args = [
        "-i", video_path,
        "-filter_complex", 
        f"[0:v]trim=start={start_time}:end={end_time},setpts=PTS-STARTPTS,{video_filter}[v];" +
        f"[0:a]atrim=start={start_time}:end={end_time},asetpts=PTS-STARTPTS,{audio_filter}[a]",
        "-map", "[v]",
        "-map", "[a]",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_time_lapse(
    video_path: str,
    speed_factor: float,
    output_path: str
) -> Dict[str, Any]:
    """
    Create time lapse effect using FFmpeg.

    Args:
        video_path: Input video path
        speed_factor: Speed factor (2.0 = 2x speed)
        output_path: Output video path
        
    Returns:
        Dictionary with operation status
    """
    # For video and audio
    video_filter = f"setpts={1/speed_factor}*PTS"
    audio_filter = f"atempo={speed_factor}"

    args = [
        "-i", video_path,
        "-filter_complex", 
        f"[0:v]{video_filter}[v];[0:a]{audio_filter}[a]",
        "-map", "[v]",
        "-map", "[a]",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


def ffmpeg_render_timeline(
    clips: List[Dict[str, Any]],
    output_path: str,
    total_duration: float
) -> Dict[str, Any]:
    """
    Render a timeline of clips using FFmpeg.

    Args:
        clips: List of clip configurations
        output_path: Output video path
        total_duration: Total duration of timeline
        
    Returns:
        Dictionary with operation status
    """
    try:
        # This is a complex operation that would involve:
        # 1. Creating filter graphs for each clip
        # 2. Concatenating or overlaying clips based on their timing
        # 3. Applying transitions and effects
        
        # For now, we'll simulate a simple implementation
        # that just processes the first video clip
        
        if not clips:
            return {
                "success": False,
                "error": "No clips to render"
            }
        
        # Find the first video clip
        video_clip = None
        for clip in clips:
            if clip.get("type") == "video":
                video_clip = clip
                break
        
        if not video_clip:
            return {
                "success": False,
                "error": "No video clips found in timeline"
            }
        
        # Just copy the first video clip as a placeholder
        args = [
            "-i", video_clip.get("source"),
            "-t", str(total_duration),  # Set duration
            "-c", "copy",
            "-y",  # Overwrite output file
            output_path
        ]
        
        return run_ffmpeg_command(args)
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Timeline rendering failed: {str(e)}"
        }


def ffmpeg_concat_videos(
    video_clips: List[Dict[str, Any]],
    output_path: str,
    transition: str = "fade",
    transition_duration: float = 0.5
) -> Dict[str, Any]:
    """
    Concatenate videos with transitions using FFmpeg.

    Args:
        video_clips: List of video clip information
        output_path: Output video path
        transition: Transition type
        transition_duration: Transition duration
        
    Returns:
        Dictionary with operation status
    """
    # Create a temporary file listing all videos
    list_file = "concat_list.txt"
    with open(list_file, 'w') as f:
        for clip in video_clips:
            f.write(f"file '{os.path.abspath(clip['path'])}'\n")

    args = [
        "-f", "concat",
        "-safe", "0",
        "-i", list_file,
        "-c", "copy",
        "-y",  # Overwrite output file
        output_path
    ]

    result = run_ffmpeg_command(args)

    # Clean up
    try:
        os.remove(list_file)
    except:
        pass

    return result


def ffmpeg_add_multiple_texts(
    video_path: str,
    text_configs: List[Dict[str, Any]],
    output_path: str
) -> Dict[str, Any]:
    """
    Add multiple text overlays to a video using FFmpeg.

    Args:
        video_path: Input video path
        text_configs: List of text configurations
        output_path: Output video path
        
    Returns:
        Dictionary with operation status
    """
    # Build complex filter string for multiple texts
    filter_parts = []
    for i, config in enumerate(text_configs):
        text = config.get("text", "").replace(":", "\\:").replace("'", "\\'")
        x = config.get("x", "(w-text_w)/2")
        y = config.get("y", "h-text_h-50")
        font_config = config.get("font_config", "font=Arial:fontsize=24:fontcolor=white")
        start_time = config.get("start_time", 0)
        duration = config.get("duration")
        
        if duration is not None:
            filter_part = f"drawtext={font_config}:text='{text}':x={x}:y={y}:enable='between(t,{start_time},{start_time + duration})'"
        else:
            filter_part = f"drawtext={font_config}:text='{text}':x={x}:y={y}:enable='gte(t,{start_time})'"
        
        filter_parts.append(filter_part)

    filter_str = ",".join(filter_parts)

    args = [
        "-i", video_path,
        "-vf", filter_str,
        "-c:a", "copy",
        "-y",  # Overwrite output file
        output_path
    ]

    return run_ffmpeg_command(args)


# Example usage
if __name__ == "__main__":
    # Test FFmpeg availability
    status = check_ffmpeg_available()
    print("FFmpeg status:", status)
    
    if status["available"]:
        # Example: Get video info
        try:
            # Replace with actual video file path for testing
            test_video = "test.mp4"
            if os.path.exists(test_video):
                info = get_video_info(test_video)
                print("Video info:", json.dumps(info, indent=2))
            else:
                print("Test video not found, creating mock operations...")
                
                # Demonstrate other operations
                ffmpeg = FFmpegWrapper()
                print("FFmpeg wrapper initialized successfully")
                
        except Exception as e:
            print(f"Error: {e}")
