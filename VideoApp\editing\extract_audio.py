# -*- coding: utf-8 -*-
# Add this import
import os
from core.libraries import *

def extract_audio_from_video(video_path: str, fmt: str='mp3') -> str:
    if not os.path.exists(video_path):
        raise FileNotFoundError(video_path)
    clip = mp.VideoFileClip(video_path)
    out_path = os.path.splitext(video_path)[0] + f"_audio.{fmt}"
    clip.audio.write_audiofile(out_path)
    clip.close()
    return out_path
