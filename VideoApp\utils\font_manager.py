# -*- coding: utf-8 -*-
"""
Font Management System for Video Editing
Handles loading and managing English and Urdu fonts
"""

import os
import zipfile
import tempfile
import shutil
from typing import Dict, List, Optional, Tuple
from PyQt5.QtGui import QFont, QFontDatabase, QFontMetrics
from PyQt5.QtCore import QObject, pyqtSignal
import logging

logger = logging.getLogger(__name__)

class FontManager(QObject):
    """Manages fonts for video editing with support for English and Urdu"""
    
    fonts_loaded = pyqtSignal(int)  # Emits number of fonts loaded
    font_load_error = pyqtSignal(str)  # Emits error message
    
    def __init__(self):
        super().__init__()
        self.font_database = QFontDatabase()
        self.loaded_fonts = {}  # {font_name: font_info}
        self.english_fonts = {}
        self.urdu_fonts = {}
        self.temp_font_dir = None
        self.app_root = self._find_app_root()
        
    def _find_app_root(self):
        """Find the application root directory"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Go up from VideoApp/utils to find the root
        while current_dir and not os.path.exists(os.path.join(current_dir, "English Fonts")):
            parent = os.path.dirname(current_dir)
            if parent == current_dir:  # Reached filesystem root
                break
            current_dir = parent
        return current_dir
    
    def initialize_fonts(self):
        """Initialize and load all fonts from English and Urdu font folders"""
        try:
            logger.info("Initializing font system...")
            
            # Create temporary directory for extracted fonts
            self.temp_font_dir = tempfile.mkdtemp(prefix="bish_fonts_")
            logger.info(f"Created temp font directory: {self.temp_font_dir}")
            
            # Load English fonts
            english_font_dir = os.path.join(self.app_root, "English Fonts")
            if os.path.exists(english_font_dir):
                self._load_fonts_from_directory(english_font_dir, "English")
            
            # Load Urdu fonts
            urdu_font_dir = os.path.join(self.app_root, "Urdu Fonts")
            if os.path.exists(urdu_font_dir):
                self._load_fonts_from_directory(urdu_font_dir, "Urdu")
            
            total_fonts = len(self.english_fonts) + len(self.urdu_fonts)
            logger.info(f"Loaded {total_fonts} fonts ({len(self.english_fonts)} English, {len(self.urdu_fonts)} Urdu)")
            self.fonts_loaded.emit(total_fonts)
            
        except Exception as e:
            error_msg = f"Error initializing fonts: {str(e)}"
            logger.error(error_msg)
            self.font_load_error.emit(error_msg)
    
    def _load_fonts_from_directory(self, font_dir: str, language: str):
        """Load fonts from a directory (handles zip files and direct font files)"""
        try:
            for item in os.listdir(font_dir):
                item_path = os.path.join(font_dir, item)
                
                if item.endswith('.zip'):
                    self._extract_and_load_zip_fonts(item_path, language)
                elif item.endswith(('.ttf', '.otf', '.woff', '.woff2')):
                    self._load_font_file(item_path, language)
                    
        except Exception as e:
            logger.error(f"Error loading fonts from {font_dir}: {e}")
    
    def _extract_and_load_zip_fonts(self, zip_path: str, language: str):
        """Extract fonts from zip file and load them"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Extract to temporary directory
                extract_dir = os.path.join(self.temp_font_dir, os.path.splitext(os.path.basename(zip_path))[0])
                zip_ref.extractall(extract_dir)
                
                # Find and load font files
                for root, dirs, files in os.walk(extract_dir):
                    for file in files:
                        if file.endswith(('.ttf', '.otf', '.woff', '.woff2')):
                            font_path = os.path.join(root, file)
                            self._load_font_file(font_path, language)
                            
        except Exception as e:
            logger.error(f"Error extracting fonts from {zip_path}: {e}")
    
    def _load_font_file(self, font_path: str, language: str):
        """Load a single font file"""
        try:
            # Add font to Qt font database
            font_id = self.font_database.addApplicationFont(font_path)
            
            if font_id != -1:
                # Get font families from the loaded font
                font_families = self.font_database.applicationFontFamilies(font_id)
                
                for family in font_families:
                    font_info = {
                        'family': family,
                        'path': font_path,
                        'language': language,
                        'id': font_id,
                        'styles': self.font_database.styles(family),
                        'sizes': self.font_database.pointSizes(family)
                    }
                    
                    # Store in appropriate dictionary
                    if language == "English":
                        self.english_fonts[family] = font_info
                    else:
                        self.urdu_fonts[family] = font_info
                    
                    self.loaded_fonts[family] = font_info
                    logger.debug(f"Loaded {language} font: {family}")
            else:
                logger.warning(f"Failed to load font: {font_path}")
                
        except Exception as e:
            logger.error(f"Error loading font file {font_path}: {e}")
    
    def get_english_fonts(self) -> Dict[str, dict]:
        """Get all loaded English fonts"""
        return self.english_fonts.copy()
    
    def get_urdu_fonts(self) -> Dict[str, dict]:
        """Get all loaded Urdu fonts"""
        return self.urdu_fonts.copy()
    
    def get_all_fonts(self) -> Dict[str, dict]:
        """Get all loaded fonts"""
        return self.loaded_fonts.copy()
    
    def get_font(self, family_name: str, size: int = 12, bold: bool = False, italic: bool = False) -> QFont:
        """Create a QFont object for the specified font family"""
        font = QFont(family_name, size)
        font.setBold(bold)
        font.setItalic(italic)
        return font
    
    def get_font_preview_text(self, language: str) -> str:
        """Get preview text for font testing"""
        if language == "Urdu":
            return "یہ اردو فونٹ کا نمونہ ہے - All is Greatest"
        else:
            return "This is a sample English font - All is Greatest"
    
    def get_font_metrics(self, family_name: str, size: int = 12) -> QFontMetrics:
        """Get font metrics for text measurement"""
        font = self.get_font(family_name, size)
        return QFontMetrics(font)
    
    def is_font_available(self, family_name: str) -> bool:
        """Check if a font family is available"""
        return family_name in self.loaded_fonts
    
    def get_font_info(self, family_name: str) -> Optional[dict]:
        """Get detailed information about a font"""
        return self.loaded_fonts.get(family_name)
    
    def cleanup(self):
        """Clean up temporary files"""
        try:
            if self.temp_font_dir and os.path.exists(self.temp_font_dir):
                shutil.rmtree(self.temp_font_dir)
                logger.info("Cleaned up temporary font directory")
        except Exception as e:
            logger.error(f"Error cleaning up font directory: {e}")
    
    def __del__(self):
        """Destructor to clean up resources"""
        self.cleanup()

# Global font manager instance
_font_manager = None

def get_font_manager() -> FontManager:
    """Get the global font manager instance"""
    global _font_manager
    if _font_manager is None:
        _font_manager = FontManager()
        _font_manager.initialize_fonts()
    return _font_manager
