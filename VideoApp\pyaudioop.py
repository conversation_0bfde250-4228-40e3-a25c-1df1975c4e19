# -*- coding: utf-8 -*-
"""
PyAudioOp Compatibility Module
Provides basic audio operations for Python 3.13+ compatibility
"""

import array
import struct
import math
from typing import Union, Tuple


def _get_sample_size(width: int) -> int:
    """Get sample size for given width"""
    if width == 1:
        return 1
    elif width == 2:
        return 2
    elif width == 4:
        return 4
    else:
        raise ValueError(f"Unsupported sample width: {width}")


def _get_format_char(width: int, signed: bool = True) -> str:
    """Get struct format character for given width"""
    if width == 1:
        return 'b' if signed else 'B'
    elif width == 2:
        return 'h' if signed else 'H'
    elif width == 4:
        return 'i' if signed else 'I'
    else:
        raise ValueError(f"Unsupported sample width: {width}")


def getsample(fragment: bytes, width: int, index: int) -> int:
    """Get a sample from audio fragment"""
    sample_size = _get_sample_size(width)
    if index < 0 or index * sample_size >= len(fragment):
        raise IndexError("Sample index out of range")
    
    start = index * sample_size
    end = start + sample_size
    sample_bytes = fragment[start:end]
    
    format_char = _get_format_char(width)
    return struct.unpack(f'<{format_char}', sample_bytes)[0]


def max(fragment: bytes, width: int) -> int:
    """Return the maximum absolute value of all samples in fragment"""
    if not fragment:
        return 0
    
    sample_size = _get_sample_size(width)
    format_char = _get_format_char(width)
    
    max_val = 0
    for i in range(0, len(fragment), sample_size):
        if i + sample_size <= len(fragment):
            sample_bytes = fragment[i:i + sample_size]
            sample = struct.unpack(f'<{format_char}', sample_bytes)[0]
            max_val = max(max_val, abs(sample))
    
    return max_val


def minmax(fragment: bytes, width: int) -> Tuple[int, int]:
    """Return the minimum and maximum values of all samples in fragment"""
    if not fragment:
        return (0, 0)
    
    sample_size = _get_sample_size(width)
    format_char = _get_format_char(width)
    
    min_val = float('inf')
    max_val = float('-inf')
    
    for i in range(0, len(fragment), sample_size):
        if i + sample_size <= len(fragment):
            sample_bytes = fragment[i:i + sample_size]
            sample = struct.unpack(f'<{format_char}', sample_bytes)[0]
            min_val = min(min_val, sample)
            max_val = max(max_val, sample)
    
    return (int(min_val), int(max_val))


def avg(fragment: bytes, width: int) -> int:
    """Return the average of all samples in fragment"""
    if not fragment:
        return 0
    
    sample_size = _get_sample_size(width)
    format_char = _get_format_char(width)
    
    total = 0
    count = 0
    
    for i in range(0, len(fragment), sample_size):
        if i + sample_size <= len(fragment):
            sample_bytes = fragment[i:i + sample_size]
            sample = struct.unpack(f'<{format_char}', sample_bytes)[0]
            total += sample
            count += 1
    
    return int(total / count) if count > 0 else 0


def rms(fragment: bytes, width: int) -> int:
    """Return the root-mean-square of all samples in fragment"""
    if not fragment:
        return 0
    
    sample_size = _get_sample_size(width)
    format_char = _get_format_char(width)
    
    sum_squares = 0
    count = 0
    
    for i in range(0, len(fragment), sample_size):
        if i + sample_size <= len(fragment):
            sample_bytes = fragment[i:i + sample_size]
            sample = struct.unpack(f'<{format_char}', sample_bytes)[0]
            sum_squares += sample * sample
            count += 1
    
    return int(math.sqrt(sum_squares / count)) if count > 0 else 0


def mul(fragment: bytes, width: int, factor: float) -> bytes:
    """Multiply all samples in fragment by factor"""
    if not fragment:
        return fragment
    
    sample_size = _get_sample_size(width)
    format_char = _get_format_char(width)
    
    result = bytearray()
    
    for i in range(0, len(fragment), sample_size):
        if i + sample_size <= len(fragment):
            sample_bytes = fragment[i:i + sample_size]
            sample = struct.unpack(f'<{format_char}', sample_bytes)[0]
            new_sample = int(sample * factor)
            
            # Clamp to valid range
            if width == 1:
                new_sample = max(-128, min(127, new_sample))
            elif width == 2:
                new_sample = max(-32768, min(32767, new_sample))
            elif width == 4:
                new_sample = max(-2147483648, min(2147483647, new_sample))
            
            result.extend(struct.pack(f'<{format_char}', new_sample))
    
    return bytes(result)


def add(fragment1: bytes, fragment2: bytes, width: int) -> bytes:
    """Add two audio fragments sample by sample"""
    if not fragment1 or not fragment2:
        return fragment1 or fragment2
    
    sample_size = _get_sample_size(width)
    format_char = _get_format_char(width)
    
    # Use the shorter fragment length
    min_len = min(len(fragment1), len(fragment2))
    min_len = (min_len // sample_size) * sample_size  # Align to sample boundary
    
    result = bytearray()
    
    for i in range(0, min_len, sample_size):
        sample1_bytes = fragment1[i:i + sample_size]
        sample2_bytes = fragment2[i:i + sample_size]
        
        sample1 = struct.unpack(f'<{format_char}', sample1_bytes)[0]
        sample2 = struct.unpack(f'<{format_char}', sample2_bytes)[0]
        
        new_sample = sample1 + sample2
        
        # Clamp to valid range
        if width == 1:
            new_sample = max(-128, min(127, new_sample))
        elif width == 2:
            new_sample = max(-32768, min(32767, new_sample))
        elif width == 4:
            new_sample = max(-2147483648, min(2147483647, new_sample))
        
        result.extend(struct.pack(f'<{format_char}', new_sample))
    
    return bytes(result)


def bias(fragment: bytes, width: int, bias_value: int) -> bytes:
    """Add a bias to all samples in fragment"""
    if not fragment:
        return fragment
    
    sample_size = _get_sample_size(width)
    format_char = _get_format_char(width)
    
    result = bytearray()
    
    for i in range(0, len(fragment), sample_size):
        if i + sample_size <= len(fragment):
            sample_bytes = fragment[i:i + sample_size]
            sample = struct.unpack(f'<{format_char}', sample_bytes)[0]
            new_sample = sample + bias_value
            
            # Clamp to valid range
            if width == 1:
                new_sample = max(-128, min(127, new_sample))
            elif width == 2:
                new_sample = max(-32768, min(32767, new_sample))
            elif width == 4:
                new_sample = max(-2147483648, min(2147483647, new_sample))
            
            result.extend(struct.pack(f'<{format_char}', new_sample))
    
    return bytes(result)


def reverse(fragment: bytes, width: int) -> bytes:
    """Reverse the samples in fragment"""
    if not fragment:
        return fragment
    
    sample_size = _get_sample_size(width)
    
    # Split into samples and reverse
    samples = []
    for i in range(0, len(fragment), sample_size):
        if i + sample_size <= len(fragment):
            samples.append(fragment[i:i + sample_size])
    
    samples.reverse()
    return b''.join(samples)


# Additional compatibility functions
def lin2lin(fragment: bytes, width: int, newwidth: int) -> bytes:
    """Convert samples between different widths"""
    if width == newwidth:
        return fragment
    
    # This is a simplified implementation
    # In practice, you'd want proper sample conversion
    return fragment


def ratecv(fragment: bytes, width: int, nchannels: int, inrate: int, outrate: int, state, weightA: int = 1, weightB: int = 0):
    """Convert the frame rate of the input fragment"""
    # Simplified implementation - just return the fragment
    # In practice, you'd want proper rate conversion
    return (fragment, state)


print("✅ PyAudioOp compatibility module loaded")
