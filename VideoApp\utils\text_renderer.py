# -*- coding: utf-8 -*-
"""
Text Renderer for Video Overlays
Handles rendering text overlays on video frames
"""

import os
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class TextRenderer:
    """Renders text overlays on video frames"""
    
    def __init__(self):
        self.default_font_size = 24
        self.default_color = (255, 255, 255)  # White
        self.default_bg_color = (0, 0, 0, 128)  # Semi-transparent black
        
    def render_text_on_frame(self, frame: np.ndarray, text_overlay, frame_time: float) -> np.ndarray:
        """
        Render text overlay on a video frame
        
        Args:
            frame: Video frame as numpy array (BGR format)
            text_overlay: TextOverlay object with text properties
            frame_time: Current frame time in seconds
            
        Returns:
            Frame with text overlay rendered
        """
        try:
            # Check if text should be visible at this time
            if not self._is_text_visible(text_overlay, frame_time):
                return frame
            
            # Convert BGR to RGB for PIL
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # Create drawing context
            draw = ImageDraw.Draw(pil_image, 'RGBA')
            
            # Load font
            font = self._load_font(text_overlay.font_family, text_overlay.font_size)
            
            # Calculate text position
            text_x, text_y = self._calculate_text_position(
                pil_image.size, text_overlay, font
            )
            
            # Draw background if enabled
            if hasattr(text_overlay, 'background_color') and text_overlay.background_color.alpha() > 0:
                self._draw_text_background(draw, text_overlay.text, font, text_x, text_y, text_overlay)
            
            # Draw shadow if enabled
            if text_overlay.shadow_enabled:
                self._draw_text_shadow(draw, text_overlay.text, font, text_x, text_y, text_overlay)
            
            # Draw outline if enabled
            if text_overlay.outline_enabled:
                self._draw_text_outline(draw, text_overlay.text, font, text_x, text_y, text_overlay)
            
            # Draw main text
            text_color = (
                text_overlay.color.red(),
                text_overlay.color.green(),
                text_overlay.color.blue(),
                int(255 * text_overlay.opacity / 100)
            )
            
            draw.text((text_x, text_y), text_overlay.text, font=font, fill=text_color)
            
            # Convert back to BGR for OpenCV
            frame_with_text = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            return frame_with_text
            
        except Exception as e:
            logger.error(f"Error rendering text on frame: {e}")
            return frame
    
    def _is_text_visible(self, text_overlay, frame_time: float) -> bool:
        """Check if text should be visible at the given time"""
        return text_overlay.start_time <= frame_time <= text_overlay.end_time
    
    def _load_font(self, font_family: str, font_size: int) -> ImageFont.ImageFont:
        """Load font for text rendering"""
        try:
            # Try to load custom font first
            font_path = self._find_font_file(font_family)
            if font_path and os.path.exists(font_path):
                return ImageFont.truetype(font_path, font_size)
            
            # Fallback to system fonts
            system_fonts = [
                f"C:/Windows/Fonts/{font_family}.ttf",
                f"C:/Windows/Fonts/{font_family}",
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf"
            ]
            
            for font_path in system_fonts:
                if os.path.exists(font_path):
                    return ImageFont.truetype(font_path, font_size)
            
            # Ultimate fallback
            return ImageFont.load_default()
            
        except Exception as e:
            logger.warning(f"Error loading font {font_family}: {e}")
            return ImageFont.load_default()
    
    def _find_font_file(self, font_family: str) -> Optional[str]:
        """Find font file path for the given font family"""
        try:
            # This would integrate with the font manager to find the actual font file
            from .font_manager import get_font_manager
            font_manager = get_font_manager()
            font_info = font_manager.get_font_info(font_family)
            
            if font_info and 'path' in font_info:
                return font_info['path']
                
        except Exception as e:
            logger.debug(f"Could not find font file for {font_family}: {e}")
        
        return None
    
    def _calculate_text_position(self, image_size: Tuple[int, int], text_overlay, font) -> Tuple[int, int]:
        """Calculate text position based on percentage and alignment"""
        try:
            width, height = image_size
            
            # Get text dimensions
            bbox = font.getbbox(text_overlay.text)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # Calculate position based on percentage
            x = int((width * text_overlay.position_x / 100) - (text_width / 2))
            y = int((height * text_overlay.position_y / 100) - (text_height / 2))
            
            # Ensure text stays within frame bounds
            x = max(0, min(x, width - text_width))
            y = max(0, min(y, height - text_height))
            
            return x, y
            
        except Exception as e:
            logger.error(f"Error calculating text position: {e}")
            return 50, 50  # Default position
    
    def _draw_text_background(self, draw, text: str, font, x: int, y: int, text_overlay):
        """Draw background rectangle behind text"""
        try:
            bbox = font.getbbox(text)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            padding = 5
            bg_color = (
                text_overlay.background_color.red(),
                text_overlay.background_color.green(),
                text_overlay.background_color.blue(),
                text_overlay.background_color.alpha()
            )
            
            draw.rectangle(
                [x - padding, y - padding, x + text_width + padding, y + text_height + padding],
                fill=bg_color
            )
            
        except Exception as e:
            logger.error(f"Error drawing text background: {e}")
    
    def _draw_text_shadow(self, draw, text: str, font, x: int, y: int, text_overlay):
        """Draw text shadow"""
        try:
            shadow_color = (
                text_overlay.shadow_color.red(),
                text_overlay.shadow_color.green(),
                text_overlay.shadow_color.blue(),
                text_overlay.shadow_color.alpha()
            )
            
            shadow_x = x + text_overlay.shadow_offset_x
            shadow_y = y + text_overlay.shadow_offset_y
            
            draw.text((shadow_x, shadow_y), text, font=font, fill=shadow_color)
            
        except Exception as e:
            logger.error(f"Error drawing text shadow: {e}")
    
    def _draw_text_outline(self, draw, text: str, font, x: int, y: int, text_overlay):
        """Draw text outline"""
        try:
            outline_color = (
                text_overlay.outline_color.red(),
                text_overlay.outline_color.green(),
                text_overlay.outline_color.blue(),
                255
            )
            
            # Draw outline by drawing text in multiple positions
            outline_width = text_overlay.outline_width
            for dx in range(-outline_width, outline_width + 1):
                for dy in range(-outline_width, outline_width + 1):
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, font=font, fill=outline_color)
                        
        except Exception as e:
            logger.error(f"Error drawing text outline: {e}")
    
    def render_text_overlays_on_video(self, input_video_path: str, output_video_path: str, 
                                    text_overlays: List, progress_callback=None) -> bool:
        """
        Render text overlays on entire video
        
        Args:
            input_video_path: Path to input video
            output_video_path: Path to output video
            text_overlays: List of TextOverlay objects
            progress_callback: Optional callback for progress updates
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Open input video
            cap = cv2.VideoCapture(input_video_path)
            if not cap.isOpened():
                logger.error(f"Could not open video: {input_video_path}")
                return False
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
            
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Calculate current time
                current_time = frame_count / fps
                
                # Apply all text overlays
                for text_overlay in text_overlays:
                    frame = self.render_text_on_frame(frame, text_overlay, current_time)
                
                # Write frame
                out.write(frame)
                
                # Update progress
                if progress_callback:
                    progress = (frame_count / total_frames) * 100
                    progress_callback(progress)
                
                frame_count += 1
            
            # Clean up
            cap.release()
            out.release()
            
            logger.info(f"Successfully rendered text overlays to: {output_video_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error rendering text overlays on video: {e}")
            return False

# Global text renderer instance
_text_renderer = None

def get_text_renderer() -> TextRenderer:
    """Get the global text renderer instance"""
    global _text_renderer
    if _text_renderer is None:
        _text_renderer = TextRenderer()
    return _text_renderer
