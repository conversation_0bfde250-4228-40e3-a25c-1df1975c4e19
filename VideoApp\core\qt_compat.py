# -*- coding: utf-8 -*-
"""
Qt Compatibility Layer
Provides a unified interface for PyQt5/PySide6 compatibility
"""

import sys
import os

# Try to import Qt framework in order of preference
QT_FRAMEWORK = None
QT_VERSION = None

# First try PyQt5
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    from PyQt5.QtMultimedia import *
    from PyQt5.QtMultimediaWidgets import *
    try:
        from PyQt5.QtOpenGL import *
        OPENGL_AVAILABLE = True
    except ImportError:
        OPENGL_AVAILABLE = False
    try:
        from PyQt5.QtNetwork import *
        NETWORK_AVAILABLE = True
    except ImportError:
        NETWORK_AVAILABLE = False
    try:
        from PyQt5.QtWebEngineWidgets import *
        WEB_ENGINE_AVAILABLE = True
    except ImportError:
        WEB_ENGINE_AVAILABLE = False
    
    QT_FRAMEWORK = "PyQt5"
    QT_VERSION = "5"
    print(f"✅ Using {QT_FRAMEWORK}")
    
except ImportError:
    # Try PySide6 as fallback
    try:
        from PySide6.QtWidgets import *
        from PySide6.QtCore import *
        from PySide6.QtGui import *
        from PySide6.QtMultimedia import *
        from PySide6.QtMultimediaWidgets import *
        try:
            from PySide6.QtOpenGL import *
            OPENGL_AVAILABLE = True
        except ImportError:
            OPENGL_AVAILABLE = False
        try:
            from PySide6.QtNetwork import *
            NETWORK_AVAILABLE = True
        except ImportError:
            NETWORK_AVAILABLE = False
        try:
            from PySide6.QtWebEngineWidgets import *
            WEB_ENGINE_AVAILABLE = True
        except ImportError:
            WEB_ENGINE_AVAILABLE = False
        
        QT_FRAMEWORK = "PySide6"
        QT_VERSION = "6"
        print(f"✅ Using {QT_FRAMEWORK}")
        
    except ImportError:
        print("❌ ERROR: No Qt framework available!")
        print("Please install PyQt5 or PySide6:")
        print("  pip install PyQt5")
        print("  or")
        print("  pip install PySide6")
        sys.exit(1)

# Compatibility functions and constants
def get_qt_framework():
    """Get the currently used Qt framework name"""
    return QT_FRAMEWORK

def get_qt_version():
    """Get the Qt version (5 or 6)"""
    return QT_VERSION

def is_web_engine_available():
    """Check if web engine is available"""
    return WEB_ENGINE_AVAILABLE

# Signal compatibility
if QT_FRAMEWORK == "PyQt5":
    from PyQt5.QtCore import pyqtSignal as Signal
    from PyQt5.QtCore import pyqtSlot as Slot
elif QT_FRAMEWORK == "PySide6":
    from PySide6.QtCore import Signal, Slot

# Export commonly used classes and functions
__all__ = [
    # Framework info
    'QT_FRAMEWORK', 'QT_VERSION', 'WEB_ENGINE_AVAILABLE',
    'get_qt_framework', 'get_qt_version', 'is_web_engine_available',
    
    # Core classes
    'QApplication', 'QWidget', 'QMainWindow', 'QDialog',
    'QVBoxLayout', 'QHBoxLayout', 'QGridLayout', 'QFormLayout',
    'QPushButton', 'QLabel', 'QLineEdit', 'QTextEdit', 'QPlainTextEdit',
    'QListWidget', 'QListWidgetItem', 'QTreeWidget', 'QTreeWidgetItem',
    'QTableWidget', 'QTableWidgetItem', 'QComboBox', 'QSpinBox',
    'QDoubleSpinBox', 'QSlider', 'QProgressBar', 'QCheckBox',
    'QRadioButton', 'QGroupBox', 'QTabWidget', 'QStackedWidget',
    'QSplitter', 'QFrame', 'QScrollArea', 'QDockWidget',
    'QMenuBar', 'QMenu', 'QAction', 'QToolBar', 'QStatusBar',
    'QFileDialog', 'QMessageBox', 'QInputDialog', 'QColorDialog',
    'QFontDialog', 'QProgressDialog',
    
    # Core functionality
    'Qt', 'QTimer', 'QThread', 'QObject', 'QEvent', 'QEventLoop',
    'QUrl', 'QSize', 'QPoint', 'QRect', 'QRectF', 'QSizeF', 'QPointF',
    'QVariant', 'QSettings', 'QStandardPaths', 'QDir', 'QFile',
    'QTextStream', 'QDataStream', 'QIODevice',
    
    # Graphics and painting
    'QPainter', 'QPen', 'QBrush', 'QColor', 'QPixmap', 'QIcon',
    'QFont', 'QFontMetrics', 'QPalette', 'QGradient', 'QLinearGradient',
    'QRadialGradient', 'QConicalGradient', 'QTransform',
    'QPolygon', 'QPolygonF', 'QPainterPath', 'QRegion',
    
    # Multimedia
    'QMediaPlayer', 'QMediaContent', 'QVideoWidget', 'QAudioOutput',
    'QCamera', 'QCameraViewfinder', 'QCameraImageCapture',
    
    # Signals and slots
    'Signal', 'Slot', 'pyqtSignal' if QT_FRAMEWORK == "PyQt5" else 'Signal',
    
    # Style and appearance
    'QStyle', 'QStyleFactory', 'QStyleOption', 'QProxyStyle',
    'QSizePolicy', 'QSpacerItem',
    
    # Models and views
    'QAbstractItemModel', 'QStandardItemModel', 'QStandardItem',
    'QAbstractItemView', 'QItemSelectionModel', 'QHeaderView',
    'QAbstractTableModel', 'QAbstractListModel', 'QSortFilterProxyModel',

    # Graphics View Framework
    'QGraphicsView', 'QGraphicsScene', 'QGraphicsItem', 'QGraphicsRectItem',
    'QGraphicsEllipseItem', 'QGraphicsLineItem', 'QGraphicsTextItem',
    'QGraphicsPixmapItem', 'QGraphicsProxyWidget', 'QGraphicsEffect',
    'QGraphicsDropShadowEffect', 'QGraphicsBlurEffect', 'QGraphicsColorizeEffect',
    
    # Network (if available)
    'QNetworkAccessManager', 'QNetworkRequest', 'QNetworkReply',
    'QNetworkProxy', 'QNetworkProxyFactory', 'QNetworkConfiguration',
]

# Add web engine classes if available
if WEB_ENGINE_AVAILABLE:
    __all__.extend([
        'QWebEngineView', 'QWebEnginePage', 'QWebEngineProfile',
        'QWebEngineSettings', 'QWebEngineScript', 'QWebEngineScriptCollection'
    ])

# Add compatibility for deprecated classes
try:
    # QMatrix was deprecated in Qt 5.15 and removed in Qt 6
    if QT_VERSION == "5":
        from PyQt5.QtGui import QMatrix
        __all__.append('QMatrix')
    else:
        # Create a dummy QMatrix class for compatibility
        class QMatrix:
            def __init__(self, *args, **kwargs):
                pass
        globals()['QMatrix'] = QMatrix
        __all__.append('QMatrix')
except ImportError:
    # Create a dummy QMatrix class for compatibility
    class QMatrix:
        def __init__(self, *args, **kwargs):
            pass
    globals()['QMatrix'] = QMatrix
    __all__.append('QMatrix')

# Add compatibility for network classes if not available
if not globals().get('NETWORK_AVAILABLE', False):
    class QNetworkAccessManager:
        def __init__(self, *args, **kwargs):
            pass
    class QNetworkRequest:
        def __init__(self, *args, **kwargs):
            pass
    class QNetworkReply:
        def __init__(self, *args, **kwargs):
            pass
    class QNetworkProxy:
        def __init__(self, *args, **kwargs):
            pass
    class QNetworkProxyFactory:
        def __init__(self, *args, **kwargs):
            pass
    class QNetworkConfiguration:
        def __init__(self, *args, **kwargs):
            pass

    globals().update({
        'QNetworkAccessManager': QNetworkAccessManager,
        'QNetworkRequest': QNetworkRequest,
        'QNetworkReply': QNetworkReply,
        'QNetworkProxy': QNetworkProxy,
        'QNetworkProxyFactory': QNetworkProxyFactory,
        'QNetworkConfiguration': QNetworkConfiguration,
    })

print(f"✅ Qt compatibility layer loaded successfully with {QT_FRAMEWORK}")
