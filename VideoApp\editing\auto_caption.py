# -*- coding: utf-8 -*-
from core.libraries import *
from core.config import WHISPER_MODEL_NAME

def auto_transcribe(audio_or_video_path: str, language: str="ur") -> str:
    if whisper is None:
        raise RuntimeError("Whisper not available. Bundle a model or use an external service.")
    model = whisper.load_model(WHISPER_MODEL_NAME)
    result = model.transcribe(audio_or_video_path, language=language)
    # Save simple SRT-like output
    out_path = os.path.splitext(audio_or_video_path)[0] + ".srt"
    with open(out_path, "w", encoding="utf-8") as f:
        f.write("1\n00:00:00,000 --> 00:00:10,000\n" + result.get("text",""))
    return out_path
