# -*- coding: utf-8 -*-
from core.libraries import *
from utils.video_utils import to_seconds
from typing import Optional, List, <PERSON>ple
import numpy as np
import os

def cut_subclip(video_path: str, start: str, end: str, out_path: Optional[str]=None,
               freeze_frames: Optional[List[Tuple[str, float]]] = None) -> str:
    """Cut a subclip between HH:MM:SS timestamps using moviepy with optional frame freezing."""
    if not os.path.exists(video_path):
        raise FileNotFoundError(video_path)
    
    s, e = to_seconds(start), to_seconds(end)
    if e <= s:
        raise ValueError("End must be greater than start.")
    
    clip = mp.VideoFileClip(video_path)
    sub = clip.subclip(s, e)
    
    # Apply frame freezing if specified
    if freeze_frames:
        sub = apply_frame_freezing(sub, freeze_frames)
    
    out_path = out_path or os.path.splitext(video_path)[0] + f"_{start.replace(':','')}-{end.replace(':','')}.mp4"
    
    # Optimized output for social media
    sub.write_videofile(
        out_path, 
        codec="libx264", 
        audio_codec="aac",
        bitrate="8000k",
        preset="medium",
        threads=4,
        ffmpeg_params=["-movflags", "+faststart"]
    )
    
    clip.close()
    sub.close()
    
    return out_path

def apply_frame_freezing(clip, freeze_frames):
    """Apply frame freezing at specified timestamps."""
    clips = []
    current_time = 0
    
    # Sort freeze frames by timestamp
    freeze_frames.sort(key=lambda x: to_seconds(x[0]) if isinstance(x[0], str) else x[0])
    
    for freeze_time, duration in freeze_frames:
        if isinstance(freeze_time, str):
            freeze_seconds = to_seconds(freeze_time)
        else:
            freeze_seconds = freeze_time
        
        # Add clip segment before freeze
        if current_time < freeze_seconds:
            clips.append(clip.subclip(current_time, freeze_seconds))
        
        # Create freeze frame
        freeze_frame = clip.to_ImageClip(freeze_seconds).set_duration(duration)
        clips.append(freeze_frame)
        
        current_time = freeze_seconds
    
    # Add remaining clip after last freeze
    if current_time < clip.duration:
        clips.append(clip.subclip(current_time, clip.duration))
    
    return mp.concatenate_videoclips(clips)

def extract_multiple_clips(video_path: str, segments: List[Tuple[str, str]], 
                          out_dir: Optional[str] = None) -> List[str]:
    """Extract multiple clips from a video in one operation."""
    output_paths = []
    
    for i, (start, end) in enumerate(segments):
        if out_dir:
            base_name = f"clip_{i+1}_{start.replace(':','')}-{end.replace(':','')}"
            out_path = os.path.join(out_dir, f"{base_name}.mp4")
        else:
            out_path = None
        
        output_paths.append(cut_subclip(video_path, start, end, out_path))
    
    return output_paths

def create_clip_with_preview(video_path: str, start: str, end: str, 
                           preview_seconds: int = 3) -> str:
    """Create a clip with a short preview of what comes before the actual content."""
    s, e = to_seconds(start), to_seconds(end)
    preview_start = max(0, s - preview_seconds)
    
    clip = mp.VideoFileClip(video_path)
    preview = clip.subclip(preview_start, s)
    main_content = clip.subclip(s, e)
    
    # Add fade transition between preview and main content
    preview = preview.fadeout(0.5)
    main_content = main_content.fadein(0.5)
    
    final_clip = mp.concatenate_videoclips([preview, main_content])
    
    out_path = os.path.splitext(video_path)[0] + f"_preview_{start.replace(':','')}-{end.replace(':','')}.mp4"
    final_clip.write_videofile(out_path, codec="libx264", audio_codec="aac")
    
    clip.close()
    final_clip.close()
    
    return out_path
