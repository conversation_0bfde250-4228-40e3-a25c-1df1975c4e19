# -*- coding: utf-8 -*-
"""
Progress dialog for showing download/processing progress
"""

from core.libraries import *
from core.theme import *

# Import QProgressDialog specifically if not available from core.libraries
try:
    from PyQt5.QtWidgets import QProgressDialog
except ImportError:
    # Create a dummy class if PyQt5 is not available
    class QProgressDialog:
        def __init__(self, *args, **kwargs):
            pass


class ProgressDialog(QDialog):
    """A dialog that shows progress for long-running operations"""
    
    def __init__(self, title="Processing...", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 150)
        
        # Create layout
        layout = QVBoxLayout(self)
        
        # Title label
        self.title_label = QLabel(title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {TEXT_COLOR};
                margin: 10px;
            }}
        """)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {PANEL_COLOR};
                border-radius: 5px;
                text-align: center;
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
            }}
            QProgressBar::chunk {{
                background-color: {ACCENT_COLOR};
                border-radius: 3px;
            }}
        """)
        
        # Status label
        self.status_label = QLabel("Starting...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                margin: 5px;
            }}
        """)
        
        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        # Add widgets to layout
        layout.addWidget(self.title_label)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)
        layout.addWidget(self.cancel_button)
        
        # Apply dialog styling
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {BACKGROUND_COLOR};
                border: 2px solid {PANEL_COLOR};
                border-radius: 10px;
            }}
        """)
        
        self.cancelled = False
    
    def update_progress(self, percentage, status_text=""):
        """Update the progress bar and status text"""
        if self.cancelled:
            return
            
        self.progress_bar.setValue(int(percentage))
        
        if status_text:
            self.status_label.setText(status_text)
        else:
            self.status_label.setText(f"Progress: {percentage:.1f}%")
        
        # Process events to keep UI responsive
        QApplication.processEvents()
    
    def set_indeterminate(self):
        """Set progress bar to indeterminate mode"""
        self.progress_bar.setRange(0, 0)
    
    def set_determinate(self, maximum=100):
        """Set progress bar to determinate mode"""
        self.progress_bar.setRange(0, maximum)
    
    def set_title(self, title):
        """Update the dialog title"""
        self.setWindowTitle(title)
        self.title_label.setText(title)
    
    def set_status(self, status):
        """Update the status text"""
        self.status_label.setText(status)
    
    def reject(self):
        """Handle cancel button or dialog close"""
        self.cancelled = True
        super().reject()
    
    def is_cancelled(self):
        """Check if the operation was cancelled"""
        return self.cancelled
    
    def complete(self, message="Complete!"):
        """Mark the operation as complete"""
        self.progress_bar.setValue(100)
        self.status_label.setText(message)
        self.cancel_button.setText("Close")
        
        # Auto-close after 2 seconds
        QTimer.singleShot(2000, self.accept)


class SimpleProgressDialog(QProgressDialog):
    """A simpler progress dialog using Qt's built-in QProgressDialog"""
    
    def __init__(self, title="Processing...", parent=None):
        super().__init__(title, "Cancel", 0, 100, parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setMinimumDuration(0)  # Show immediately
        
        # Apply styling
        self.setStyleSheet(f"""
            QProgressDialog {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
            }}
            QProgressBar {{
                border: 2px solid {PANEL_COLOR};
                border-radius: 5px;
                text-align: center;
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
            }}
            QProgressBar::chunk {{
                background-color: {ACCENT_COLOR};
                border-radius: 3px;
            }}
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 5px;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
    
    def update_progress(self, percentage, status_text=""):
        """Update progress with percentage and optional status text"""
        self.setValue(int(percentage))
        if status_text:
            self.setLabelText(status_text)
        
        # Process events to keep UI responsive
        QApplication.processEvents()
        
        return not self.wasCanceled()


# Convenience function for quick progress dialogs
def show_progress_dialog(title="Processing...", parent=None, simple=False):
    """
    Create and return a progress dialog
    
    Args:
        title: Dialog title
        parent: Parent widget
        simple: Use SimpleProgressDialog instead of custom ProgressDialog
    
    Returns:
        ProgressDialog instance
    """
    if simple:
        return SimpleProgressDialog(title, parent)
    else:
        return ProgressDialog(title, parent)
