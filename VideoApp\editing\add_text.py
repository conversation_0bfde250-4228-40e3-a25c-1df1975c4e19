# -*- coding: utf-8 -*-
"""
Add text overlays to videos
"""

from core.libraries import *
from editing.color_selection import COLORS
from editing.font_selection import list_fonts
import os
import moviepy.editor as mp
from typing import Dict, Optional, <PERSON><PERSON>


def add_text_to_video(
    video_path: str,
    text: str,
    start_time: float = 0,
    duration: Optional[float] = None,
    position: str = "center",
    font_family: str = "Arial",
    font_size: int = 24,
    color: str = "#FFFFFF",
    stroke_color: str = "#000000",
    stroke_width: int = 2,
    background_color: Optional[str] = None,
    output_path: Optional[str] = None
) -> str:
    """
    Add text overlay to a video.
    
    Args:
        video_path: Path to the input video
        text: Text to overlay
        start_time: When to start showing the text (seconds)
        duration: How long to show the text (seconds, None for rest of video)
        position: Text position ("center", "top", "bottom", "left", "right", or tuple)
        font_family: Font family name
        font_size: Font size in pixels
        color: Text color (hex)
        stroke_color: Stroke/outline color (hex)
        stroke_width: Stroke width in pixels
        background_color: Background color (hex, optional)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Create text clip
        text_clip = create_text_clip(
            text=text,
            duration=duration or (video.duration - start_time),
            font_family=font_family,
            font_size=font_size,
            color=color,
            stroke_color=stroke_color,
            stroke_width=stroke_width,
            background_color=background_color
        )
        
        # Set timing and position
        text_clip = text_clip.set_start(start_time)
        text_clip = set_text_position(text_clip, position, video.size)
        
        # Composite video with text
        final_video = mp.CompositeVideoClip([video, text_clip])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_with_text.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        text_clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to add text to video: {str(e)}")


def create_text_clip(
    text: str,
    duration: float,
    font_family: str = "Arial",
    font_size: int = 24,
    color: str = "#FFFFFF",
    stroke_color: str = "#000000",
    stroke_width: int = 2,
    background_color: Optional[str] = None
) -> mp.TextClip:
    """Create a text clip with specified styling"""
    
    # Create basic text clip
    text_clip = mp.TextClip(
        text,
        fontsize=font_size,
        color=color,
        font=font_family,
        stroke_color=stroke_color,
        stroke_width=stroke_width,
        method='label'
    ).set_duration(duration)
    
    # Add background if specified
    if background_color:
        # Create background clip
        bg_clip = mp.ColorClip(
            size=(text_clip.w + 20, text_clip.h + 10),
            color=background_color
        ).set_duration(duration)
        
        # Composite text over background
        text_clip = mp.CompositeVideoClip([
            bg_clip,
            text_clip.set_position('center')
        ])
    
    return text_clip


def set_text_position(text_clip: mp.TextClip, position: str, video_size: Tuple[int, int]) -> mp.TextClip:
    """Set the position of text on the video"""
    
    if isinstance(position, tuple):
        # Custom position as (x, y) coordinates
        return text_clip.set_position(position)
    
    # Predefined positions
    position_map = {
        'center': 'center',
        'top': ('center', 'top'),
        'bottom': ('center', 'bottom'),
        'left': ('left', 'center'),
        'right': ('right', 'center'),
        'top-left': ('left', 'top'),
        'top-right': ('right', 'top'),
        'bottom-left': ('left', 'bottom'),
        'bottom-right': ('right', 'bottom')
    }
    
    pos = position_map.get(position.lower(), 'center')
    return text_clip.set_position(pos)


def add_animated_text(
    video_path: str,
    text: str,
    start_time: float = 0,
    duration: Optional[float] = None,
    animation: str = "fade",
    **text_kwargs
) -> str:
    """
    Add animated text overlay to a video.
    
    Args:
        video_path: Path to the input video
        text: Text to overlay
        start_time: When to start showing the text (seconds)
        duration: How long to show the text (seconds)
        animation: Animation type ("fade", "slide", "zoom", "typewriter")
        **text_kwargs: Additional text styling arguments
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Set default duration
        if duration is None:
            duration = min(5.0, video.duration - start_time)
        
        # Create text clip
        text_clip = create_text_clip(text, duration, **text_kwargs)
        
        # Apply animation
        text_clip = apply_text_animation(text_clip, animation)
        
        # Set timing and position
        text_clip = text_clip.set_start(start_time)
        position = text_kwargs.get('position', 'center')
        text_clip = set_text_position(text_clip, position, video.size)
        
        # Composite video with text
        final_video = mp.CompositeVideoClip([video, text_clip])
        
        # Generate output path
        base_name = os.path.splitext(video_path)[0]
        output_path = f"{base_name}_animated_text.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        text_clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to add animated text: {str(e)}")


def apply_text_animation(text_clip: mp.TextClip, animation: str) -> mp.TextClip:
    """Apply animation effects to text clip"""
    
    if animation == "fade":
        # Fade in and out
        return text_clip.fadein(0.5).fadeout(0.5)
    
    elif animation == "slide":
        # Slide in from left
        w, h = text_clip.size
        return text_clip.set_position(lambda t: (max(-w, -w + 100*t), 'center'))
    
    elif animation == "zoom":
        # Zoom in effect
        return text_clip.resize(lambda t: min(1, 0.5 + 0.5*t))
    
    elif animation == "typewriter":
        # Typewriter effect (simplified)
        return text_clip.fadein(0.1)
    
    else:
        # No animation
        return text_clip


def add_multiple_texts(
    video_path: str,
    text_configs: list,
    output_path: Optional[str] = None
) -> str:
    """
    Add multiple text overlays to a video.
    
    Args:
        video_path: Path to the input video
        text_configs: List of text configuration dictionaries
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Create all text clips
        text_clips = []
        for config in text_configs:
            text_clip = create_text_clip(
                text=config['text'],
                duration=config.get('duration', 5.0),
                font_family=config.get('font_family', 'Arial'),
                font_size=config.get('font_size', 24),
                color=config.get('color', '#FFFFFF'),
                stroke_color=config.get('stroke_color', '#000000'),
                stroke_width=config.get('stroke_width', 2),
                background_color=config.get('background_color')
            )
            
            # Set timing and position
            start_time = config.get('start_time', 0)
            text_clip = text_clip.set_start(start_time)
            
            position = config.get('position', 'center')
            text_clip = set_text_position(text_clip, position, video.size)
            
            # Apply animation if specified
            animation = config.get('animation')
            if animation:
                text_clip = apply_text_animation(text_clip, animation)
            
            text_clips.append(text_clip)
        
        # Composite all clips
        final_video = mp.CompositeVideoClip([video] + text_clips)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_multi_text.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        for clip in text_clips:
            clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to add multiple texts: {str(e)}")


# Convenience functions for common text styles
def add_title_text(video_path: str, title: str, duration: float = 3.0) -> str:
    """Add a title text overlay"""
    return add_text_to_video(
        video_path=video_path,
        text=title,
        start_time=0,
        duration=duration,
        position="top",
        font_size=36,
        color="#FFFFFF",
        stroke_color="#000000",
        stroke_width=3,
        background_color="#00000080"
    )


def add_subtitle_text(video_path: str, subtitle: str, start_time: float, duration: float = 2.0) -> str:
    """Add a subtitle text overlay"""
    return add_text_to_video(
        video_path=video_path,
        text=subtitle,
        start_time=start_time,
        duration=duration,
        position="bottom",
        font_size=20,
        color="#FFFFFF",
        stroke_color="#000000",
        stroke_width=2
    )


def add_watermark_text(video_path: str, watermark: str) -> str:
    """Add a watermark text overlay"""
    return add_text_to_video(
        video_path=video_path,
        text=watermark,
        start_time=0,
        duration=None,  # Full video duration
        position="bottom-right",
        font_size=16,
        color="#FFFFFF",
        stroke_color="#000000",
        stroke_width=1
    )
