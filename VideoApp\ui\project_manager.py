import json
import os
from datetime import datetime


class ProjectManager:
    def __init__(self):
        self.current_project = None
        self.projects_dir = "projects"
        
        if not os.path.exists(self.projects_dir):
            os.makedirs(self.projects_dir)
            
    def create_project(self, name, video_source):
        project_id = f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        project_path = os.path.join(self.projects_dir, project_id)
        
        os.makedirs(project_path)
        
        project_data = {
            "id": project_id,
            "name": name,
            "created": datetime.now().isoformat(),
            "video_source": video_source,
            "timeline": [],
            "assets": [],
            "exports": []
        }
        
        with open(os.path.join(project_path, "project.json"), "w") as f:
            json.dump(project_data, f, indent=4)
            
        self.current_project = project_data
        return project_data
        
    def save_project(self):
        if self.current_project:
            project_path = os.path.join(self.projects_dir, self.current_project["id"])
            with open(os.path.join(project_path, "project.json"), "w") as f:
                json.dump(self.current_project, f, indent=4)
                
    def load_project(self, project_id):
        project_path = os.path.join(self.projects_dir, project_id)
        with open(os.path.join(project_path, "project.json"), "r") as f:
            self.current_project = json.load(f)
        return self.current_project
        
    def add_asset(self, asset_type, asset_path):
        if self.current_project:
            asset_id = f"{asset_type}_{len(self.current_project['assets'])}"
            asset_data = {
                "id": asset_id,
                "type": asset_type,
                "path": asset_path,
                "added": datetime.now().isoformat()
            }
            self.current_project['assets'].append(asset_data)
            self.save_project()
            
    def add_timeline_event(self, event_type, start_time, end_time, data):
        if self.current_project:
            event_id = f"{event_type}_{len(self.current_project['timeline'])}"
            event_data = {
                "id": event_id,
                "type": event_type,
                "start": start_time,
                "end": end_time,
                "data": data,
                "added": datetime.now().isoformat()
            }
            self.current_project['timeline'].append(event_data)
            self.save_project()