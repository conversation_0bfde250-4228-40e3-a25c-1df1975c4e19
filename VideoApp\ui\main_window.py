# -*- coding: utf-8 -*-
from core.libraries import QM<PERSON><PERSON><PERSON><PERSON>, QW<PERSON>t, QHBox<PERSON><PERSON><PERSON>, QStackedWidget, QMessageBox, QAction, QMenu, QToolBar, QStatusBar, QLabel, QDockWidget, Q<PERSON><PERSON><PERSON>ayout, Q<PERSON><PERSON>Widget, <PERSON><PERSON><PERSON><PERSON>ter, QApplication
import core.theme as theme
from ui.side_panel import SidePanel
from dashboard.search_page import SearchPage
from dashboard.download_page import DownloadPage
from dashboard.play_page import PlayPage
from dashboard.edit_page import EditPage
from ui.timeline_widget import TimelineWidget
from core.project_manager import ProjectManager


class MainWindow(QMainWindow):
    def __init__(self, video_player=None):
        super().__init__()
        self.video_player = video_player  # Store the video player instance
        self.project_manager = ProjectManager()
        self.current_project = None
        
        self.setWindowTitle("🎬 Video Editor Pro")
        self.resize(1400, 900)

        # Set application style
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {theme.BACKGROUND_COLOR};
                color: {theme.TEXT_COLOR};
                font-family: '{theme.APP_FONT_FAMILY}';
            }}
            QMenuBar {{
                background-color: {theme.PANEL_COLOR};
                color: {theme.TEXT_COLOR};
                border-bottom: 1px solid {theme.BORDER_COLOR};
            }}
            QMenuBar::item:selected {{
                background-color: {theme.ACCENT_COLOR};
            }}
            QMenu {{
                background-color: {theme.PANEL_COLOR};
                color: {theme.TEXT_COLOR};
                border: 1px solid {theme.BORDER_COLOR};
            }}
            QMenu::item:selected {{
                background-color: {theme.ACCENT_COLOR};
                color: {theme.BACKGROUND_COLOR};
            }}
            QToolBar {{
                background-color: {theme.PANEL_COLOR};
                border: none;
                border-bottom: 1px solid {theme.BORDER_COLOR};
                spacing: 5px;
                padding: 3px;
            }}
            QStatusBar {{
                background-color: {theme.PANEL_COLOR};
                color: {theme.TEXT_COLOR};
                border-top: 1px solid {theme.BORDER_COLOR};
            }}
            QDockWidget {{
                titlebar-close-icon: url(close.png);
                titlebar-normal-icon: url(float.png);
            }}
            QDockWidget::title {{
                background: {theme.PANEL_COLOR};
                padding: 5px;
                border: 1px solid {theme.BORDER_COLOR};
            }}
        """)

        # Create menu bar
        self.create_menus()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create status bar
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("Ready")

        # Root widget
        central = QWidget(self)
        self.setCentralWidget(central)
        root = QHBoxLayout(central)
        root.setContentsMargins(0, 0, 0, 0)
        root.setSpacing(0)

        # Sidebar
        self.sidebar = SidePanel(self)
        root.addWidget(self.sidebar, 0)

        # Stacked pages
        self.pages = QStackedWidget(self)
        root.addWidget(self.pages, 1)

        # Instantiate pages with video_player reference
        self.page_search = SearchPage(self, video_player=video_player)
        self.page_download = DownloadPage(self, video_player=video_player)
        self.page_play = PlayPage(self, video_player=video_player)
        self.page_edit = EditPage(self, video_player=video_player)

        # Add to stack
        self.pages.addWidget(self.page_search)    # index 0
        self.pages.addWidget(self.page_download)  # index 1
        self.pages.addWidget(self.page_play)      # index 2
        self.pages.addWidget(self.page_edit)      # index 3

        # Default page
        self.pages.setCurrentIndex(0)

        # Connect sidebar buttons → pages
        self.sidebar.goto_search.clicked.connect(lambda: self.set_current_page(0))
        self.sidebar.goto_download.clicked.connect(lambda: self.set_current_page(1))
        self.sidebar.goto_play.clicked.connect(lambda: self.set_current_page(2))
        self.sidebar.goto_edit.clicked.connect(lambda: self.set_current_page(3))
        self.sidebar.goto_export.clicked.connect(self.export_project)
        
        # Connect video player signals
        if video_player:
            video_player.error_occurred.connect(self.show_error)
            video_player.duration_loaded.connect(self.on_duration_loaded)
        
    def set_current_page(self, index):
        """Set the current page and update UI accordingly"""
        self.pages.setCurrentIndex(index)
        
        # Update status bar
        page_names = ["Search", "Download", "Play", "Edit"]
        self.statusBar.showMessage(f"Viewing: {page_names[index]}")

    def create_menus(self):
        """Create the application menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        
        new_action = QAction('New Project', self)
        new_action.setShortcut('Ctrl+N')
        new_action.setStatusTip('Create a new project')
        new_action.triggered.connect(self.new_project)
        
        open_action = QAction('Open Project', self)
        open_action.setShortcut('Ctrl+O')
        open_action.setStatusTip('Open an existing project')
        open_action.triggered.connect(self.open_project)
        
        save_action = QAction('Save Project', self)
        save_action.setShortcut('Ctrl+S')
        save_action.setStatusTip('Save the current project')
        save_action.triggered.connect(self.save_project)
        
        export_action = QAction('Export Video', self)
        export_action.setShortcut('Ctrl+E')
        export_action.setStatusTip('Export the edited video')
        export_action.triggered.connect(self.export_project)
        
        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('Exit application')
        exit_action.triggered.connect(self.close)
        
        file_menu.addActions([new_action, open_action, save_action])
        file_menu.addSeparator()
        file_menu.addAction(export_action)
        file_menu.addSeparator()
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu('Edit')
        
        undo_action = QAction('Undo', self)
        undo_action.setShortcut('Ctrl+Z')
        undo_action.setStatusTip('Undo last action')
        
        redo_action = QAction('Redo', self)
        redo_action.setShortcut('Ctrl+Y')
        redo_action.setStatusTip('Redo last action')
        
        cut_action = QAction('Cut', self)
        cut_action.setShortcut('Ctrl+X')
        cut_action.setStatusTip('Cut selected segment')
        
        edit_menu.addActions([undo_action, redo_action])
        edit_menu.addSeparator()
        edit_menu.addAction(cut_action)
        
        # View menu
        view_menu = menubar.addMenu('View')
        
        fullscreen_action = QAction('Toggle Fullscreen', self)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.setStatusTip('Toggle fullscreen mode')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        
        view_menu.addAction(fullscreen_action)
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        
        about_action = QAction('About', self)
        about_action.setStatusTip('About this application')
        about_action.triggered.connect(self.show_about)
        
        help_menu.addAction(about_action)
        
    def create_toolbar(self):
        """Create the main toolbar"""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setMovable(False)
        self.addToolBar(toolbar)
        
        # Add common editing tools
        new_action = QAction('New', self)
        new_action.setStatusTip('Create new project')
        new_action.triggered.connect(self.new_project)
        
        save_action = QAction('Save', self)
        save_action.setStatusTip('Save project')
        save_action.triggered.connect(self.save_project)
        
        cut_action = QAction('Cut', self)
        cut_action.setStatusTip('Cut selected segment')
        
        text_action = QAction('Text', self)
        text_action.setStatusTip('Add text overlay')
        
        # Add widgets to toolbar
        toolbar.addAction(new_action)
        toolbar.addAction(save_action)
        toolbar.addSeparator()
        toolbar.addAction(cut_action)
        toolbar.addAction(text_action)
        toolbar.addSeparator()
        
        # Add a project name label
        self.project_label = QLabel("No Project Loaded")
        self.project_label.setStyleSheet("padding: 5px; color: #ccc;")
        toolbar.addWidget(self.project_label)
        
    def new_project(self):
        """Create a new project"""
        # This would typically open a dialog to set project parameters
        self.current_project = self.project_manager.create_project(
            "Untitled Project", 
            ""
        )
        self.project_label.setText(f"Project: {self.current_project['name']}")
        self.statusBar.showMessage("Created new project")
        
    def open_project(self):
        """Open an existing project"""
        # This would typically open a file dialog
        self.statusBar.showMessage("Open project dialog would appear here")
        
    def save_project(self):
        """Save the current project"""
        if self.current_project:
            self.project_manager.save_project()
            self.statusBar.showMessage("Project saved successfully")
        else:
            self.statusBar.showMessage("No project to save")
            
    def export_project(self):
        """Export the current project"""
        if self.current_project:
            self.statusBar.showMessage("Exporting project...")
            # Simulate export process
            QApplication.processEvents()
            # In a real application, this would be a longer process
            self.statusBar.showMessage("Project exported successfully")
        else:
            self.statusBar.showMessage("No project to export")
            
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
            
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About Video Editor Pro", 
                         "A comprehensive video editing application with social media integration.\n\n"
                         "Version 1.0.0\n"
                         "© 2023 Video Editor Pro Team")
        
    def show_error(self, message):
        """Helper method to show error messages"""
        QMessageBox.warning(self, "Error", message)
        self.statusBar.showMessage(f"Error: {message}")
        
    def on_duration_loaded(self, duration):
        """Handle video duration loaded event"""
        minutes = duration // 60000
        seconds = (duration % 60000) // 1000
        self.statusBar.showMessage(f"Video loaded: {minutes}m {seconds}s duration")
        
    def closeEvent(self, event):
        """Handle application close event"""
        # Check if there are unsaved changes
        reply = QMessageBox.question(self, 'Confirm Exit', 
                                    'Are you sure you want to exit?',
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
