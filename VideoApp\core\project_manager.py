# -*- coding: utf-8 -*-
"""
Project management functionality for VideoApp
"""

import os
import json
import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict


@dataclass
class ProjectSettings:
    """Project settings data class"""
    name: str
    description: str = ""
    created_date: str = ""
    modified_date: str = ""
    output_directory: str = ""
    default_resolution: str = "1920x1080"
    default_fps: int = 30
    default_codec: str = "libx264"
    default_audio_codec: str = "aac"


@dataclass
class VideoClip:
    """Video clip data class"""
    id: str
    path: str
    name: str
    start_time: float = 0.0
    end_time: float = 0.0
    duration: float = 0.0
    position: int = 0
    effects: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.effects is None:
            self.effects = []


@dataclass
class Project:
    """Project data class"""
    settings: ProjectSettings
    clips: List[VideoClip] = None
    timeline: List[str] = None  # List of clip IDs in order
    
    def __post_init__(self):
        if self.clips is None:
            self.clips = []
        if self.timeline is None:
            self.timeline = []


class ProjectManager:
    """Manages video editing projects"""
    
    def __init__(self, projects_directory: str = "projects"):
        self.projects_directory = projects_directory
        self.current_project: Optional[Project] = None
        self.current_project_path: Optional[str] = None
        
        # Ensure projects directory exists
        os.makedirs(self.projects_directory, exist_ok=True)
    
    def create_new_project(self, name: str, description: str = "") -> Project:
        """Create a new project"""
        # Create project settings
        settings = ProjectSettings(
            name=name,
            description=description,
            created_date=datetime.datetime.now().isoformat(),
            modified_date=datetime.datetime.now().isoformat(),
            output_directory=os.path.join(self.projects_directory, name, "output")
        )
        
        # Create project
        project = Project(settings=settings)
        
        # Set as current project
        self.current_project = project
        
        # Create project directory
        project_dir = os.path.join(self.projects_directory, name)
        os.makedirs(project_dir, exist_ok=True)
        os.makedirs(settings.output_directory, exist_ok=True)
        
        # Save project
        self.current_project_path = os.path.join(project_dir, f"{name}.json")
        self.save_project()
        
        return project
    
    def load_project(self, project_path: str) -> Optional[Project]:
        """Load a project from file"""
        try:
            if not os.path.exists(project_path):
                return None
            
            with open(project_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            # Convert to project object
            settings_data = project_data.get('settings', {})
            settings = ProjectSettings(**settings_data)
            
            clips_data = project_data.get('clips', [])
            clips = [VideoClip(**clip_data) for clip_data in clips_data]
            
            timeline = project_data.get('timeline', [])
            
            project = Project(settings=settings, clips=clips, timeline=timeline)
            
            # Set as current project
            self.current_project = project
            self.current_project_path = project_path
            
            return project
            
        except Exception as e:
            print(f"Error loading project: {e}")
            return None
    
    def save_project(self, project_path: Optional[str] = None) -> bool:
        """Save the current project"""
        if not self.current_project:
            return False
        
        try:
            # Use provided path or current project path
            save_path = project_path or self.current_project_path
            if not save_path:
                return False
            
            # Update modified date
            self.current_project.settings.modified_date = datetime.datetime.now().isoformat()
            
            # Convert project to dictionary
            project_data = {
                'settings': asdict(self.current_project.settings),
                'clips': [asdict(clip) for clip in self.current_project.clips],
                'timeline': self.current_project.timeline
            }
            
            # Save to file
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, indent=2, ensure_ascii=False)
            
            self.current_project_path = save_path
            return True
            
        except Exception as e:
            print(f"Error saving project: {e}")
            return False
    
    def add_clip(self, clip_path: str, name: Optional[str] = None) -> Optional[VideoClip]:
        """Add a video clip to the current project"""
        if not self.current_project:
            return None
        
        if not os.path.exists(clip_path):
            return None
        
        # Generate clip ID
        clip_id = f"clip_{len(self.current_project.clips)}_{int(datetime.datetime.now().timestamp())}"
        
        # Get clip name
        if not name:
            name = os.path.splitext(os.path.basename(clip_path))[0]
        
        # Create clip object
        clip = VideoClip(
            id=clip_id,
            path=clip_path,
            name=name,
            position=len(self.current_project.clips)
        )
        
        # Try to get video duration (basic implementation)
        try:
            import cv2
            cap = cv2.VideoCapture(clip_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS) or 30
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                duration = frame_count / fps
                clip.duration = duration
                clip.end_time = duration
                cap.release()
        except:
            # Fallback if cv2 is not available
            clip.duration = 0.0
        
        # Add to project
        self.current_project.clips.append(clip)
        self.current_project.timeline.append(clip_id)
        
        return clip
    
    def remove_clip(self, clip_id: str) -> bool:
        """Remove a clip from the current project"""
        if not self.current_project:
            return False
        
        # Find and remove clip
        clip_index = None
        for i, clip in enumerate(self.current_project.clips):
            if clip.id == clip_id:
                clip_index = i
                break
        
        if clip_index is not None:
            # Remove from clips list
            self.current_project.clips.pop(clip_index)
            
            # Remove from timeline
            if clip_id in self.current_project.timeline:
                self.current_project.timeline.remove(clip_id)
            
            return True
        
        return False
    
    def reorder_clips(self, new_order: List[str]) -> bool:
        """Reorder clips in the timeline"""
        if not self.current_project:
            return False
        
        # Validate that all clip IDs exist
        existing_ids = {clip.id for clip in self.current_project.clips}
        if not all(clip_id in existing_ids for clip_id in new_order):
            return False
        
        # Update timeline
        self.current_project.timeline = new_order
        return True
    
    def add_effect_to_clip(self, clip_id: str, effect_type: str, parameters: Dict[str, Any]) -> bool:
        """Add an effect to a specific clip"""
        if not self.current_project:
            return False
        
        # Find clip
        for clip in self.current_project.clips:
            if clip.id == clip_id:
                # Add effect
                effect = {
                    'type': effect_type,
                    'parameters': parameters,
                    'id': f"effect_{len(clip.effects)}_{int(datetime.datetime.now().timestamp())}"
                }
                clip.effects.append(effect)
                return True
        
        return False
    
    def remove_effect_from_clip(self, clip_id: str, effect_id: str) -> bool:
        """Remove an effect from a specific clip"""
        if not self.current_project:
            return False
        
        # Find clip
        for clip in self.current_project.clips:
            if clip.id == clip_id:
                # Find and remove effect
                for i, effect in enumerate(clip.effects):
                    if effect.get('id') == effect_id:
                        clip.effects.pop(i)
                        return True
        
        return False
    
    def get_project_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the current project"""
        if not self.current_project:
            return None
        
        return {
            'name': self.current_project.settings.name,
            'description': self.current_project.settings.description,
            'created_date': self.current_project.settings.created_date,
            'modified_date': self.current_project.settings.modified_date,
            'clip_count': len(self.current_project.clips),
            'total_duration': sum(clip.duration for clip in self.current_project.clips),
            'output_directory': self.current_project.settings.output_directory
        }
    
    def list_projects(self) -> List[Dict[str, str]]:
        """List all available projects"""
        projects = []
        
        if not os.path.exists(self.projects_directory):
            return projects
        
        for item in os.listdir(self.projects_directory):
            project_dir = os.path.join(self.projects_directory, item)
            if os.path.isdir(project_dir):
                # Look for project file
                project_file = os.path.join(project_dir, f"{item}.json")
                if os.path.exists(project_file):
                    try:
                        with open(project_file, 'r', encoding='utf-8') as f:
                            project_data = json.load(f)
                        
                        settings = project_data.get('settings', {})
                        projects.append({
                            'name': settings.get('name', item),
                            'description': settings.get('description', ''),
                            'path': project_file,
                            'created_date': settings.get('created_date', ''),
                            'modified_date': settings.get('modified_date', '')
                        })
                    except:
                        # Skip invalid project files
                        continue
        
        return projects
    
    def export_project_settings(self) -> Optional[Dict[str, Any]]:
        """Export current project settings for rendering"""
        if not self.current_project:
            return None
        
        return {
            'resolution': self.current_project.settings.default_resolution,
            'fps': self.current_project.settings.default_fps,
            'codec': self.current_project.settings.default_codec,
            'audio_codec': self.current_project.settings.default_audio_codec,
            'output_directory': self.current_project.settings.output_directory,
            'clips': [asdict(clip) for clip in self.current_project.clips],
            'timeline': self.current_project.timeline
        }
    
    def close_project(self) -> bool:
        """Close the current project"""
        if self.current_project:
            # Save before closing
            saved = self.save_project()
            
            # Clear current project
            self.current_project = None
            self.current_project_path = None
            
            return saved
        
        return True


# Global project manager instance
_project_manager = None


def get_project_manager() -> ProjectManager:
    """Get the global project manager instance"""
    global _project_manager
    if _project_manager is None:
        _project_manager = ProjectManager()
    return _project_manager


def create_project(name: str, description: str = "") -> Project:
    """Convenience function to create a new project"""
    return get_project_manager().create_new_project(name, description)


def load_project(project_path: str) -> Optional[Project]:
    """Convenience function to load a project"""
    return get_project_manager().load_project(project_path)


def save_current_project() -> bool:
    """Convenience function to save the current project"""
    return get_project_manager().save_project()


def get_current_project() -> Optional[Project]:
    """Convenience function to get the current project"""
    return get_project_manager().current_project


def add_video_clip(clip_path: str, name: Optional[str] = None) -> Optional[VideoClip]:
    """Convenience function to add a video clip"""
    return get_project_manager().add_clip(clip_path, name)
