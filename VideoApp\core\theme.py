# -*- coding: utf-8 -*-
"""Global theme and branding for the whole app."""

# ===================== 🎨 DARK THEME =====================
DARK_THEME = {
    "PRIMARY_COLOR": "#2D2D30",      # VS Code dark panel
    "ACCENT_COLOR": "#007ACC",       # VS Code blue accent
    "ACCENT_COLOR_DARK": "#005A9E",  # Darker blue variant
    "BACKGROUND_COLOR": "#1E1E1E",   # VS Code dark background
    "PANEL_COLOR": "#252526",        # VS Code side panel
    "TEXT_COLOR": "#CCCCCC",         # VS Code light text
    "TEXT_SECONDARY": "#969696",     # VS Code secondary text
    "BORDER_COLOR": "#3E3E42",       # VS Code border
    "WARN_COLOR": "#F14C4C",         # VS Code error red
    "OK_COLOR": "#89D185",           # VS Code success green
    "APP_FONT_FAMILY": "Segoe UI",   # Standard Windows font
    "APP_FONT_SIZE": 13,             # Larger readable size
}

# ===================== 🎨 LIGHT THEME =====================
LIGHT_THEME = {
    "PRIMARY_COLOR": "#E5E7EB",      # Gray-200
    "ACCENT_COLOR": "#2563EB",       # Blue-600
    "ACCENT_COLOR_DARK": "#1D4ED8",  # Blue-700 (darker variant)
    "BACKGROUND_COLOR": "#F9FAFB",   # Gray-50
    "PANEL_COLOR": "#E5E7EB",        # Gray-200
    "TEXT_COLOR": "#111827",         # Gray-900
    "TEXT_SECONDARY": "#6B7280",     # Gray-500 (secondary text)
    "BORDER_COLOR": "#D1D5DB",       # Gray-300
    "WARN_COLOR": "#DC2626",         # Red-600
    "OK_COLOR": "#16A34A",           # Green-600
    "APP_FONT_FAMILY": "Jameel Noori Nastaleeq",
    "APP_FONT_SIZE": 11,
}


def app_styles(theme: dict = DARK_THEME) -> str:
    """Return centralized QSS stylesheet for the entire application."""
    return f"""
        QWidget {{
            font-family: "{theme['APP_FONT_FAMILY']}";
            font-size: {theme['APP_FONT_SIZE']}pt;
            background-color: {theme['BACKGROUND_COLOR']};
            color: {theme['TEXT_COLOR']};
        }}

        QLineEdit, QTextEdit {{
            border: 1px solid {theme['BORDER_COLOR']};
            border-radius: 4px;
            padding: 8px;
            background: {theme['PANEL_COLOR']};
            color: {theme['TEXT_COLOR']};
            font-size: {theme['APP_FONT_SIZE']}pt;
        }}
        QLineEdit:focus, QTextEdit:focus {{
            border: 1px solid {theme['ACCENT_COLOR']};
        }}

        QPushButton {{
            background-color: {theme['PANEL_COLOR']};
            color: {theme['TEXT_COLOR']};
            border: 1px solid {theme['BORDER_COLOR']};
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: 500;
            font-size: {theme['APP_FONT_SIZE']}pt;
        }}
        QPushButton:hover {{
            background-color: {theme['ACCENT_COLOR']};
            color: #ffffff;
        }}
        QPushButton:pressed {{
            background-color: {theme['ACCENT_COLOR_DARK']};
        }}

        QListWidget {{
            border: 1px solid {theme['BORDER_COLOR']};
            border-radius: 4px;
            background: {theme['PANEL_COLOR']};
            color: {theme['TEXT_COLOR']};
            font-size: {theme['APP_FONT_SIZE']}pt;
            padding: 4px;
        }}
        QListWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {theme['BORDER_COLOR']};
        }}
        QListWidget::item:selected {{
            background: {theme['ACCENT_COLOR']};
            color: #ffffff;
        }}
        QListWidget::item:hover {{
            background: {theme['PRIMARY_COLOR']};
        }}

        QLabel {{
            color: {theme['TEXT_COLOR']};
            font-size: {theme['APP_FONT_SIZE']}pt;
            font-weight: 500;
        }}

        QComboBox {{
            border: 1px solid {theme['BORDER_COLOR']};
            border-radius: 4px;
            padding: 6px;
            background: {theme['PANEL_COLOR']};
            color: {theme['TEXT_COLOR']};
            font-size: {theme['APP_FONT_SIZE']}pt;
        }}

        QSlider::groove:horizontal {{
            height: 6px;
            background: {theme['BORDER_COLOR']};
            border-radius: 3px;
        }}
        QSlider::handle:horizontal {{
            background: {theme['ACCENT_COLOR']};
            width: 14px;
            border-radius: 7px;
            margin: -4px 0;
        }}
    """


# ========== Aliases for backward compatibility ==========
ACCENT_COLOR = DARK_THEME["ACCENT_COLOR"]
ACCENT_COLOR_DARK = DARK_THEME["ACCENT_COLOR_DARK"]
TEXT_COLOR = DARK_THEME["TEXT_COLOR"]
TEXT_SECONDARY = DARK_THEME["TEXT_SECONDARY"]
BACKGROUND_COLOR = DARK_THEME["BACKGROUND_COLOR"]
PRIMARY_COLOR = DARK_THEME["PRIMARY_COLOR"]
PANEL_COLOR = DARK_THEME["PANEL_COLOR"]
BORDER_COLOR = DARK_THEME["BORDER_COLOR"]
WARN_COLOR = DARK_THEME["WARN_COLOR"]
OK_COLOR = DARK_THEME["OK_COLOR"]
APP_FONT_FAMILY = DARK_THEME["APP_FONT_FAMILY"]
APP_FONT_SIZE = DARK_THEME["APP_FONT_SIZE"]
