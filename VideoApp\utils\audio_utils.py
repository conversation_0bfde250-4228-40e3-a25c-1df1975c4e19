# -*- coding: utf-8 -*-
# Audio utility functions

import os
import subprocess
import numpy as np
from typing import List, Optional, Dict, Any
from pydub import AudioSegment
from pydub.effects import compress_dynamic_range, high_pass_filter, low_pass_filter
import moviepy.editor as mp

def extract_audio(video_path: str, fmt: str, start_time: Optional[float] = None,
                 end_time: Optional[float] = None, bitrate: Optional[int] = None,
                 output_dir: Optional[str] = None) -> str:
    """Extract audio from video file using ffmpeg (preferred) or moviepy as fallback"""
    try:
        # Try using ffmpeg first (more efficient)
        output_path = os.path.join(output_dir or os.path.dirname(video_path), 
                                 f"{os.path.splitext(os.path.basename(video_path))[0]}.{fmt}")
        
        cmd = ['ffmpeg', '-i', video_path]
        if start_time:
            cmd.extend(['-ss', str(start_time)])
        if end_time:
            cmd.extend(['-to', str(end_time)])
        if bitrate:
            cmd.extend(['-b:a', f'{bitrate}k'])
        
        cmd.extend(['-vn', '-acodec', 'libmp3lame' if fmt == 'mp3' else 'copy', output_path])
        
        subprocess.run(cmd, check=True, capture_output=True)
        return output_path
    except (subprocess.CalledProcessError, FileNotFoundError):
        # Fallback to moviepy if ffmpeg is not available
        if not os.path.exists(video_path):
            raise FileNotFoundError(video_path)
        
        clip = mp.VideoFileClip(video_path)
        out_path = os.path.splitext(video_path)[0] + f"_audio.{fmt}"
        clip.audio.write_audiofile(out_path)
        clip.close()
        return out_path

def normalize_audio(audio_path: str) -> str:
    """Normalize audio levels using ffmpeg"""
    output_path = audio_path.replace('.', '_normalized.')
    cmd = ['ffmpeg', '-i', audio_path, '-af', 'loudnorm', output_path]
    subprocess.run(cmd, check=True, capture_output=True)
    return output_path

def validate_audio_format(fmt: str) -> bool:
    """Validate audio format"""
    return fmt.lower() in ['mp3', 'aac', 'wav', 'ogg', 'flac', 'm4a']

def get_audio_metadata(audio_path: str) -> Dict[str, Any]:
    """Get audio file metadata using pydub"""
    audio = AudioSegment.from_file(audio_path)
    return {
        'duration': len(audio) / 1000.0,  # Convert ms to seconds
        'channels': audio.channels,
        'sample_width': audio.sample_width,
        'frame_rate': audio.frame_rate,
        'frame_count': audio.frame_count(),
        'max_dBFS': audio.max_dBFS,
        'rms': audio.rms
    }

def mix_audios(audio_paths: list, output_path: str, volumes: list = None) -> str:
    """Mix multiple audio files together"""
    if not audio_paths:
        raise ValueError("No audio files provided")
    
    # Load the first audio file
    mixed = AudioSegment.from_file(audio_paths[0])
    
    # Set volume for first track if specified
    if volumes and len(volumes) > 0:
        mixed = mixed + volumes[0]  # Positive values increase volume
    
    # Mix with other audio files
    for i, path in enumerate(audio_paths[1:], 1):
        audio = AudioSegment.from_file(path)
        
        # Adjust volume if specified
        if volumes and i < len(volumes):
            audio = audio + volumes[i]
        
        # Overlay the audio
        mixed = mixed.overlay(audio, position=0)
    
    # Export the mixed audio
    mixed.export(output_path, format=output_path.split('.')[-1])
    return output_path

def adjust_audio_speed(audio_path: str, speed_factor: float, output_path: str) -> str:
    """Change audio speed without changing pitch"""
    audio = AudioSegment.from_file(audio_path)
    
    # Adjust speed
    speed_changed = audio._spawn(audio.raw_data, overrides={
        "frame_rate": int(audio.frame_rate * speed_factor)
    }).set_frame_rate(audio.frame_rate)
    
    speed_changed.export(output_path, format=output_path.split('.')[-1])
    return output_path

def apply_audio_effects(audio_path: str, output_path: str, effects: dict = None) -> str:
    """Apply various audio effects"""
    if effects is None:
        effects = {}
    
    audio = AudioSegment.from_file(audio_path)
    
    # Apply compression
    if effects.get('compression', False):
        audio = compress_dynamic_range(audio)
    
    # Apply high pass filter
    if effects.get('high_pass', 0) > 0:
        audio = high_pass_filter(audio, effects['high_pass'])
    
    # Apply low pass filter
    if effects.get('low_pass', 0) > 0:
        audio = low_pass_filter(audio, effects['low_pass'])
    
    # Adjust volume
    if effects.get('volume_change', 0) != 0:
        audio = audio + effects['volume_change']
    
    # Apply fade in/out
    if effects.get('fade_in', 0) > 0:
        audio = audio.fade_in(effects['fade_in'])
    
    if effects.get('fade_out', 0) > 0:
        audio = audio.fade_out(effects['fade_out'])
    
    # Export the processed audio
    audio.export(output_path, format=output_path.split('.')[-1])
    return output_path

def extract_audio_segment(audio_path: str, start_time: float, end_time: float, output_path: str) -> str:
    """Extract a segment from an audio file"""
    audio = AudioSegment.from_file(audio_path)
    
    # Convert times to milliseconds
    start_ms = start_time * 1000
    end_ms = end_time * 1000
    
    # Extract segment
    segment = audio[start_ms:end_ms]
    segment.export(output_path, format=output_path.split('.')[-1])
    return output_path

def generate_silence(duration: float, output_path: str, sample_rate: int = 44100) -> str:
    """Generate a silent audio segment of specified duration"""
    silence = AudioSegment.silent(duration=duration * 1000)  # Convert to ms
    silence.export(output_path, format=output_path.split('.')[-1])
    return output_path

def audio_to_spectrogram(audio_path: str, output_path: str) -> str:
    """Generate a spectrogram from audio"""
    import matplotlib.pyplot as plt
    from scipy import signal
    from scipy.io import wavfile
    
    # First convert to WAV if needed
    if not audio_path.lower().endswith('.wav'):
        audio = AudioSegment.from_file(audio_path)
        wav_path = os.path.splitext(audio_path)[0] + "_temp.wav"
        audio.export(wav_path, format="wav")
    else:
        wav_path = audio_path
    
    # Read the WAV file
    sample_rate, samples = wavfile.read(wav_path)
    
    # Generate spectrogram
    frequencies, times, spectrogram = signal.spectrogram(samples, sample_rate)
    
    # Plot the spectrogram
    plt.figure(figsize=(10, 4))
    plt.pcolormesh(times, frequencies, 10 * np.log10(spectrogram), shading='gouraud')
    plt.ylabel('Frequency [Hz]')
    plt.xlabel('Time [sec]')
    plt.colorbar(label='Intensity [dB]')
    plt.savefig(output_path)
    plt.close()
    
    # Clean up temporary file if created
    if wav_path != audio_path:
        os.remove(wav_path)
    
    return output_path
