# -*- coding: utf-8 -*-
"""Global app configuration and paths."""
import os

APP_NAME = "VideoApp"
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PROJECT_ROOT = os.path.abspath(os.path.join(BASE_DIR))
ASSETS_DIR = os.path.join(PROJECT_ROOT, "assets")
FONTS_DIR = os.path.join(ASSETS_DIR, "fonts")
ICONS_DIR = os.path.join(ASSETS_DIR, "icons")
MODELS_DIR = os.path.join(PROJECT_ROOT, "models")
DOWNLOADS_DIR = os.path.join(PROJECT_ROOT, "downloads")

# Make sure runtime dirs exist
for _d in (ASSETS_DIR, FONTS_DIR, ICONS_DIR, MODELS_DIR, DOWNLOADS_DIR):
    os.makedirs(_d, exist_ok=True)

# Default Whisper model (replace with local path if bundling)
WHISPER_MODEL_NAME = "base"  # or path to models/base.pt when packaged
