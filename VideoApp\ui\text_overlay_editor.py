# -*- coding: utf-8 -*-
"""
Text Overlay Editor for Video Editing
Allows users to add text overlays with font selection and styling
"""

import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from typing import Dict, List, Optional
import logging

try:
    from ..utils.font_manager import get_font_manager
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.font_manager import get_font_manager

logger = logging.getLogger(__name__)

class TextOverlay:
    """Represents a text overlay on video"""
    def __init__(self):
        self.text = "All is Greatest"
        self.font_family = "Arial"
        self.font_size = 24
        self.font_bold = False
        self.font_italic = False
        self.color = QColor(255, 255, 255)  # White
        self.background_color = QColor(0, 0, 0, 128)  # Semi-transparent black
        self.position_x = 50  # Percentage from left
        self.position_y = 50  # Percentage from top
        self.start_time = 0.0  # Start time in seconds
        self.end_time = 5.0    # End time in seconds
        self.opacity = 100     # 0-100
        self.outline_enabled = False
        self.outline_color = QColor(0, 0, 0)
        self.outline_width = 2
        self.shadow_enabled = False
        self.shadow_offset_x = 2
        self.shadow_offset_y = 2
        self.shadow_color = QColor(0, 0, 0, 128)

class FontPreviewWidget(QWidget):
    """Widget to preview fonts with sample text"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.font_family = "Arial"
        self.font_size = 16
        self.preview_text = "All is Greatest"
        self.setMinimumHeight(40)
        
    def set_font(self, family: str, size: int = 16):
        self.font_family = family
        self.font_size = size
        self.update()
        
    def set_preview_text(self, text: str):
        self.preview_text = text
        self.update()
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Set font
        font = QFont(self.font_family, self.font_size)
        painter.setFont(font)
        
        # Draw text
        rect = self.rect()
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(rect, Qt.AlignCenter, self.preview_text)

class TextOverlayEditor(QWidget):
    """Text overlay editor with font selection and styling options"""

    overlay_changed = pyqtSignal(object)  # Emits TextOverlay object
    time_sync_requested = pyqtSignal(float, float)  # Emits start_time, end_time for video sync

    def __init__(self, parent=None):
        super().__init__(parent)
        self.font_manager = get_font_manager()
        self.current_overlay = TextOverlay()
        self.video_player = None  # Reference to video player for sync
        self.current_video_time = 0.0
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """Setup the user interface with scroll area to prevent overlapping"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Title
        title = QLabel("📝 Text Overlay Editor")
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 10px;
                background-color: #2d2d30;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        main_layout.addWidget(title)

        # Create scroll area for content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #2b2b2b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666;
            }
        """)

        # Content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(8)
        content_layout.setContentsMargins(5, 5, 5, 5)

        # Add all groups to content layout
        content_layout.addWidget(self.create_text_input_group())
        content_layout.addWidget(self.create_font_selection_group())
        content_layout.addWidget(self.create_font_preview_group())
        content_layout.addWidget(self.create_styling_group())
        content_layout.addWidget(self.create_position_timing_group())

        # Add stretch to push everything up
        content_layout.addStretch()

        # Set content widget to scroll area
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        # Action buttons (fixed at bottom)
        button_group = self.create_action_buttons()
        main_layout.addWidget(button_group)
        
    def create_text_input_group(self):
        """Create text input group"""
        group = QGroupBox("Text Content")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
                color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)

        # Text input
        self.text_input = QTextEdit()
        self.text_input.setPlainText(self.current_overlay.text)
        self.text_input.setMaximumHeight(70)
        self.text_input.setMinimumHeight(50)
        self.text_input.setStyleSheet("""
            QTextEdit {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 5px;
                font-size: 13px;
                color: white;
            }
        """)
        layout.addWidget(self.text_input)
        
        # Language selection
        lang_layout = QHBoxLayout()
        lang_layout.setSpacing(8)

        lang_label = QLabel("Language:")
        lang_label.setStyleSheet("color: #cccccc; font-size: 12px;")
        lang_layout.addWidget(lang_label)

        self.language_combo = QComboBox()
        self.language_combo.addItems(["English", "Urdu", "Mixed"])
        self.language_combo.setStyleSheet("""
            QComboBox {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 4px;
                min-width: 100px;
                color: white;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #cccccc;
            }
        """)
        lang_layout.addWidget(self.language_combo)
        lang_layout.addStretch()

        layout.addLayout(lang_layout)

        return group
        
    def create_font_selection_group(self):
        """Create font selection group"""
        group = QGroupBox("Font Selection")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
                color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)

        # Font family selection
        font_layout = QHBoxLayout()
        font_layout.setSpacing(8)

        font_label = QLabel("Font:")
        font_label.setStyleSheet("color: #cccccc; font-size: 12px;")
        font_layout.addWidget(font_label)

        self.font_combo = QComboBox()
        self.font_combo.setStyleSheet("""
            QComboBox {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 4px;
                min-width: 180px;
                color: white;
                font-size: 12px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.populate_font_combo()
        font_layout.addWidget(self.font_combo)
        font_layout.addStretch()

        layout.addLayout(font_layout)

        # Font size and style
        size_style_layout = QHBoxLayout()
        size_style_layout.setSpacing(8)

        size_label = QLabel("Size:")
        size_label.setStyleSheet("color: #cccccc; font-size: 12px;")
        size_style_layout.addWidget(size_label)

        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 200)
        self.font_size_spin.setValue(self.current_overlay.font_size)
        self.font_size_spin.setFixedWidth(60)
        self.font_size_spin.setStyleSheet("""
            QSpinBox {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 4px;
                color: white;
                font-size: 12px;
            }
        """)
        size_style_layout.addWidget(self.font_size_spin)

        self.bold_check = QCheckBox("Bold")
        self.bold_check.setStyleSheet("color: #cccccc; font-size: 12px;")
        self.italic_check = QCheckBox("Italic")
        self.italic_check.setStyleSheet("color: #cccccc; font-size: 12px;")
        size_style_layout.addWidget(self.bold_check)
        size_style_layout.addWidget(self.italic_check)
        size_style_layout.addStretch()

        layout.addLayout(size_style_layout)

        return group
        
    def create_font_preview_group(self):
        """Create font preview group"""
        group = QGroupBox("Font Preview")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        self.font_preview = FontPreviewWidget()
        self.font_preview.setStyleSheet("""
            FontPreviewWidget {
                background-color: #2d2d30;
                border: 1px solid #555;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.font_preview)
        
        return group

    def create_styling_group(self):
        """Create styling options group"""
        group = QGroupBox("Text Styling")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)

        layout = QVBoxLayout(group)

        # Color selection
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("Text Color:"))

        self.color_button = QPushButton()
        self.color_button.setFixedSize(50, 30)
        self.color_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.current_overlay.color.name()};
                border: 1px solid #555;
                border-radius: 3px;
            }}
        """)
        color_layout.addWidget(self.color_button)

        color_layout.addWidget(QLabel("Background:"))
        self.bg_color_button = QPushButton()
        self.bg_color_button.setFixedSize(50, 30)
        self.bg_color_button.setStyleSheet(f"""
            QPushButton {{
                background-color: rgba({self.current_overlay.background_color.red()},
                                      {self.current_overlay.background_color.green()},
                                      {self.current_overlay.background_color.blue()},
                                      {self.current_overlay.background_color.alpha()});
                border: 1px solid #555;
                border-radius: 3px;
            }}
        """)
        color_layout.addWidget(self.bg_color_button)
        color_layout.addStretch()

        layout.addLayout(color_layout)

        # Opacity
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("Opacity:"))
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(self.current_overlay.opacity)
        opacity_layout.addWidget(self.opacity_slider)

        self.opacity_label = QLabel(f"{self.current_overlay.opacity}%")
        self.opacity_label.setMinimumWidth(40)
        opacity_layout.addWidget(self.opacity_label)

        layout.addLayout(opacity_layout)

        # Effects
        effects_layout = QHBoxLayout()
        self.outline_check = QCheckBox("Outline")
        self.shadow_check = QCheckBox("Shadow")
        effects_layout.addWidget(self.outline_check)
        effects_layout.addWidget(self.shadow_check)
        effects_layout.addStretch()

        layout.addLayout(effects_layout)

        return group

    def create_position_timing_group(self):
        """Create position and timing group"""
        group = QGroupBox("Position & Timing")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)

        layout = QVBoxLayout(group)

        # Position
        pos_layout = QHBoxLayout()
        pos_layout.addWidget(QLabel("X:"))
        self.pos_x_spin = QSpinBox()
        self.pos_x_spin.setRange(0, 100)
        self.pos_x_spin.setValue(self.current_overlay.position_x)
        self.pos_x_spin.setSuffix("%")
        pos_layout.addWidget(self.pos_x_spin)

        pos_layout.addWidget(QLabel("Y:"))
        self.pos_y_spin = QSpinBox()
        self.pos_y_spin.setRange(0, 100)
        self.pos_y_spin.setValue(self.current_overlay.position_y)
        self.pos_y_spin.setSuffix("%")
        pos_layout.addWidget(self.pos_y_spin)
        pos_layout.addStretch()

        layout.addLayout(pos_layout)

        # Timing with sync controls
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("Start:"))
        self.start_time_spin = QDoubleSpinBox()
        self.start_time_spin.setRange(0, 3600)
        self.start_time_spin.setValue(self.current_overlay.start_time)
        self.start_time_spin.setSuffix("s")
        self.start_time_spin.setDecimals(2)
        time_layout.addWidget(self.start_time_spin)

        # Sync start time button
        self.sync_start_btn = QPushButton("📍")
        self.sync_start_btn.setToolTip("Set start time to current video position")
        self.sync_start_btn.setFixedSize(30, 25)
        self.sync_start_btn.clicked.connect(self.sync_start_time)
        self.sync_start_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        time_layout.addWidget(self.sync_start_btn)

        time_layout.addWidget(QLabel("End:"))
        self.end_time_spin = QDoubleSpinBox()
        self.end_time_spin.setRange(0, 3600)
        self.end_time_spin.setValue(self.current_overlay.end_time)
        self.end_time_spin.setSuffix("s")
        self.end_time_spin.setDecimals(2)
        time_layout.addWidget(self.end_time_spin)

        # Sync end time button
        self.sync_end_btn = QPushButton("📍")
        self.sync_end_btn.setToolTip("Set end time to current video position")
        self.sync_end_btn.setFixedSize(30, 25)
        self.sync_end_btn.clicked.connect(self.sync_end_time)
        self.sync_end_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        time_layout.addWidget(self.sync_end_btn)
        time_layout.addStretch()

        layout.addLayout(time_layout)

        # Video sync controls
        sync_layout = QHBoxLayout()

        # Current video time display
        self.current_time_label = QLabel("Video: 00:00:00")
        self.current_time_label.setStyleSheet("color: #888; font-size: 11px;")
        sync_layout.addWidget(self.current_time_label)

        sync_layout.addStretch()

        # Quick duration buttons
        duration_label = QLabel("Quick Duration:")
        duration_label.setStyleSheet("color: #ccc; font-size: 11px;")
        sync_layout.addWidget(duration_label)

        for duration in [1.0, 3.0, 5.0, 10.0]:
            btn = QPushButton(f"{duration}s")
            btn.setFixedSize(35, 20)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 2px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            btn.clicked.connect(lambda checked, d=duration: self.set_quick_duration(d))
            sync_layout.addWidget(btn)

        layout.addLayout(sync_layout)

        return group

    def create_action_buttons(self):
        """Create action buttons"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        self.apply_button = QPushButton("✅ Apply Text")
        self.apply_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        layout.addWidget(self.apply_button)

        self.preview_button = QPushButton("👁️ Preview")
        self.preview_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        layout.addWidget(self.preview_button)

        self.reset_button = QPushButton("🔄 Reset")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        layout.addWidget(self.reset_button)

        layout.addStretch()

        return widget

    def populate_font_combo(self):
        """Populate font combo box with available fonts"""
        try:
            self.font_combo.clear()

            # Add system fonts first
            self.font_combo.addItem("--- System Fonts ---")
            system_fonts = ["Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma"]
            for font in system_fonts:
                self.font_combo.addItem(f"📝 {font}", font)

            # Add English fonts
            english_fonts = self.font_manager.get_english_fonts()
            if english_fonts:
                self.font_combo.addItem("--- English Fonts ---")
                for font_name in sorted(english_fonts.keys()):
                    self.font_combo.addItem(f"🔤 {font_name}", font_name)

            # Add Urdu fonts
            urdu_fonts = self.font_manager.get_urdu_fonts()
            if urdu_fonts:
                self.font_combo.addItem("--- Urdu Fonts ---")
                for font_name in sorted(urdu_fonts.keys()):
                    self.font_combo.addItem(f"🕌 {font_name}", font_name)

        except Exception as e:
            logger.error(f"Error populating font combo: {e}")
            # Fallback to system fonts
            self.font_combo.addItems(["Arial", "Times New Roman", "Calibri"])

    def connect_signals(self):
        """Connect UI signals to handlers"""
        try:
            # Text changes
            self.text_input.textChanged.connect(self.update_overlay)
            self.language_combo.currentTextChanged.connect(self.on_language_changed)

            # Font changes
            self.font_combo.currentTextChanged.connect(self.on_font_changed)
            self.font_size_spin.valueChanged.connect(self.update_overlay)
            self.bold_check.toggled.connect(self.update_overlay)
            self.italic_check.toggled.connect(self.update_overlay)

            # Color changes
            self.color_button.clicked.connect(self.choose_text_color)
            self.bg_color_button.clicked.connect(self.choose_background_color)

            # Opacity changes
            self.opacity_slider.valueChanged.connect(self.on_opacity_changed)

            # Position and timing
            self.pos_x_spin.valueChanged.connect(self.update_overlay)
            self.pos_y_spin.valueChanged.connect(self.update_overlay)
            self.start_time_spin.valueChanged.connect(self.update_overlay)
            self.end_time_spin.valueChanged.connect(self.update_overlay)

            # Effects
            self.outline_check.toggled.connect(self.update_overlay)
            self.shadow_check.toggled.connect(self.update_overlay)

            # Action buttons
            self.apply_button.clicked.connect(self.apply_overlay)
            self.preview_button.clicked.connect(self.preview_overlay)
            self.reset_button.clicked.connect(self.reset_overlay)

        except Exception as e:
            logger.error(f"Error connecting signals: {e}")

    def update_overlay(self):
        """Update the current overlay with UI values"""
        try:
            self.current_overlay.text = self.text_input.toPlainText()
            self.current_overlay.font_size = self.font_size_spin.value()
            self.current_overlay.font_bold = self.bold_check.isChecked()
            self.current_overlay.font_italic = self.italic_check.isChecked()
            self.current_overlay.position_x = self.pos_x_spin.value()
            self.current_overlay.position_y = self.pos_y_spin.value()
            self.current_overlay.start_time = self.start_time_spin.value()
            self.current_overlay.end_time = self.end_time_spin.value()
            self.current_overlay.opacity = self.opacity_slider.value()
            self.current_overlay.outline_enabled = self.outline_check.isChecked()
            self.current_overlay.shadow_enabled = self.shadow_check.isChecked()

            # Update font preview
            self.update_font_preview()

        except Exception as e:
            logger.error(f"Error updating overlay: {e}")

    def set_video_player(self, video_player):
        """Set reference to video player for timeline sync"""
        self.video_player = video_player
        if video_player:
            # Connect to video player time updates if available
            if hasattr(video_player, 'positionChanged'):
                video_player.positionChanged.connect(self.on_video_time_changed)

    def on_video_time_changed(self, position_ms):
        """Handle video time change"""
        self.current_video_time = position_ms / 1000.0  # Convert to seconds
        self.update_current_time_display()

    def update_current_time_display(self):
        """Update the current video time display"""
        minutes = int(self.current_video_time // 60)
        seconds = int(self.current_video_time % 60)
        milliseconds = int((self.current_video_time % 1) * 100)
        time_str = f"Video: {minutes:02d}:{seconds:02d}:{milliseconds:02d}"
        self.current_time_label.setText(time_str)

    def sync_start_time(self):
        """Sync start time to current video position"""
        if self.video_player and hasattr(self.video_player, 'player'):
            # Get current position from video player
            current_pos = self.video_player.player.position() / 1000.0  # Convert to seconds
            self.start_time_spin.setValue(current_pos)
            self.current_overlay.start_time = current_pos
            print(f"Synced start time to: {current_pos:.2f}s")
        else:
            # Use stored current time
            self.start_time_spin.setValue(self.current_video_time)
            self.current_overlay.start_time = self.current_video_time
            print(f"Synced start time to: {self.current_video_time:.2f}s")

    def sync_end_time(self):
        """Sync end time to current video position"""
        if self.video_player and hasattr(self.video_player, 'player'):
            # Get current position from video player
            current_pos = self.video_player.player.position() / 1000.0  # Convert to seconds
            self.end_time_spin.setValue(current_pos)
            self.current_overlay.end_time = current_pos
            print(f"Synced end time to: {current_pos:.2f}s")
        else:
            # Use stored current time
            self.end_time_spin.setValue(self.current_video_time)
            self.current_overlay.end_time = self.current_video_time
            print(f"Synced end time to: {self.current_video_time:.2f}s")

    def set_quick_duration(self, duration):
        """Set a quick duration from current start time"""
        start_time = self.start_time_spin.value()
        end_time = start_time + duration
        self.end_time_spin.setValue(end_time)
        self.current_overlay.end_time = end_time
        print(f"Set duration to {duration}s (Start: {start_time:.2f}s, End: {end_time:.2f}s)")

    def sync_to_video_selection(self, start_time, end_time):
        """Sync text overlay to video selection"""
        self.start_time_spin.setValue(start_time)
        self.end_time_spin.setValue(end_time)
        self.current_overlay.start_time = start_time
        self.current_overlay.end_time = end_time
        print(f"Synced to video selection: {start_time:.2f}s - {end_time:.2f}s")

    def update_font_preview(self):
        """Update the font preview widget"""
        try:
            preview_text = self.text_input.toPlainText() or "All is Greatest"
            self.font_preview.set_preview_text(preview_text)
            self.font_preview.set_font(self.current_overlay.font_family, self.current_overlay.font_size)
        except Exception as e:
            logger.error(f"Error updating font preview: {e}")

    def on_language_changed(self, language):
        """Handle language selection change"""
        try:
            # Update preview text based on language
            if language == "Urdu":
                preview_text = "یہ اردو فونٹ کا نمونہ ہے - All is Greatest"
            elif language == "Mixed":
                preview_text = "Mixed Text: All is Greatest - یہ بہترین ہے"
            else:
                preview_text = "This is English font sample - All is Greatest"

            self.font_preview.set_preview_text(preview_text)

        except Exception as e:
            logger.error(f"Error changing language: {e}")

    def on_font_changed(self, font_name):
        """Handle font selection change"""
        try:
            # Get the actual font family name from combo data
            current_index = self.font_combo.currentIndex()
            font_data = self.font_combo.itemData(current_index)

            if font_data:
                self.current_overlay.font_family = font_data
                self.update_font_preview()

        except Exception as e:
            logger.error(f"Error changing font: {e}")

    def on_opacity_changed(self, value):
        """Handle opacity slider change"""
        try:
            self.opacity_label.setText(f"{value}%")
            self.current_overlay.opacity = value
        except Exception as e:
            logger.error(f"Error changing opacity: {e}")

    def choose_text_color(self):
        """Open color dialog for text color"""
        try:
            color = QColorDialog.getColor(self.current_overlay.color, self)
            if color.isValid():
                self.current_overlay.color = color
                self.color_button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color.name()};
                        border: 1px solid #555;
                        border-radius: 3px;
                    }}
                """)
        except Exception as e:
            logger.error(f"Error choosing text color: {e}")

    def choose_background_color(self):
        """Open color dialog for background color"""
        try:
            color = QColorDialog.getColor(self.current_overlay.background_color, self)
            if color.isValid():
                self.current_overlay.background_color = color
                self.bg_color_button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: rgba({color.red()}, {color.green()}, {color.blue()}, {color.alpha()});
                        border: 1px solid #555;
                        border-radius: 3px;
                    }}
                """)
        except Exception as e:
            logger.error(f"Error choosing background color: {e}")

    def apply_overlay(self):
        """Apply the text overlay"""
        try:
            self.update_overlay()
            self.overlay_changed.emit(self.current_overlay)
            QMessageBox.information(self, "Text Applied",
                                  f"Text overlay '{self.current_overlay.text[:20]}...' has been applied to the video!")
        except Exception as e:
            logger.error(f"Error applying overlay: {e}")
            QMessageBox.warning(self, "Error", f"Failed to apply text overlay: {str(e)}")

    def preview_overlay(self):
        """Preview the text overlay"""
        try:
            self.update_overlay()
            # This would show a preview of how the text looks on the video
            QMessageBox.information(self, "Preview",
                                  f"Preview: '{self.current_overlay.text}'\n"
                                  f"Font: {self.current_overlay.font_family}\n"
                                  f"Size: {self.current_overlay.font_size}px\n"
                                  f"Position: {self.current_overlay.position_x}%, {self.current_overlay.position_y}%\n"
                                  f"Duration: {self.current_overlay.start_time}s - {self.current_overlay.end_time}s")
        except Exception as e:
            logger.error(f"Error previewing overlay: {e}")

    def reset_overlay(self):
        """Reset overlay to default values"""
        try:
            self.current_overlay = TextOverlay()
            self.refresh_ui()
        except Exception as e:
            logger.error(f"Error resetting overlay: {e}")

    def refresh_ui(self):
        """Refresh UI with current overlay values"""
        try:
            self.text_input.setPlainText(self.current_overlay.text)
            self.font_size_spin.setValue(self.current_overlay.font_size)
            self.bold_check.setChecked(self.current_overlay.font_bold)
            self.italic_check.setChecked(self.current_overlay.font_italic)
            self.pos_x_spin.setValue(self.current_overlay.position_x)
            self.pos_y_spin.setValue(self.current_overlay.position_y)
            self.start_time_spin.setValue(self.current_overlay.start_time)
            self.end_time_spin.setValue(self.current_overlay.end_time)
            self.opacity_slider.setValue(self.current_overlay.opacity)
            self.outline_check.setChecked(self.current_overlay.outline_enabled)
            self.shadow_check.setChecked(self.current_overlay.shadow_enabled)

            # Update color buttons
            self.color_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {self.current_overlay.color.name()};
                    border: 1px solid #555;
                    border-radius: 3px;
                }}
            """)

            self.bg_color_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba({self.current_overlay.background_color.red()},
                                          {self.current_overlay.background_color.green()},
                                          {self.current_overlay.background_color.blue()},
                                          {self.current_overlay.background_color.alpha()});
                    border: 1px solid #555;
                    border-radius: 3px;
                }}
            """)

            self.update_font_preview()

        except Exception as e:
            logger.error(f"Error refreshing UI: {e}")

    def get_current_overlay(self):
        """Get the current text overlay"""
        self.update_overlay()
        return self.current_overlay
