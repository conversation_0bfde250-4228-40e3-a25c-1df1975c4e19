#!/usr/bin/env python3
try:
    from ui.local_video_player import LocalVideoPlayer
    print("LocalVideoPlayer imported successfully")
except Exception as e:
    print(f"Error importing LocalVideoPlayer: {e}")

try:
    from ui.online_video_player import OnlineVideoPlayer
    print("OnlineVideoPlayer imported successfully")
except Exception as e:
    print(f"Error importing OnlineVideoPlayer: {e}")

try:
    from ui.unified_video_player import UnifiedVideoPlayer
    print("UnifiedVideoPlayer imported successfully")
except Exception as e:
    print(f"Error importing UnifiedVideoPlayer: {e}")
