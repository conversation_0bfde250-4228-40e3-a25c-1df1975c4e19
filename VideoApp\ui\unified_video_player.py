# -*- coding: utf-8 -*-
"""
Unified Video Player Widget that manages both online and local video players
"""

import os
import logging
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QStackedWidget, QHBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt, pyqtSignal
from .online_video_player import OnlineVideoPlayer
from .local_video_player import LocalVideoPlayer
from .playback_controls import PlaybackControls

# Set up logging
logger = logging.getLogger(__name__)

class UnifiedVideoPlayer(QWidget):
    """Unified video player that automatically switches between online and local players"""
    
    # Signals
    error_occurred = pyqtSignal(str)
    playback_started = pyqtSignal()
    status_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_player = None
        self.current_video_info = {}
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)
        
        # Status bar
        self.status_bar = QWidget()
        status_layout = QHBoxLayout(self.status_bar)
        status_layout.setContentsMargins(10, 5, 10, 5)
        
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #888; font-size: 12px;")
        
        self.codec_help_btn = QPushButton("Codec Help")
        self.codec_help_btn.setMaximumWidth(100)
        self.codec_help_btn.clicked.connect(self.show_codec_help)
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.codec_help_btn)
        
        # Stacked widget for switching between players
        self.player_stack = QStackedWidget()
        
        # Create player instances
        self.online_player = OnlineVideoPlayer(self)
        self.local_player = LocalVideoPlayer(self)
        
        # Add players to stack
        self.player_stack.addWidget(self.online_player)
        self.player_stack.addWidget(self.local_player)
        
        # Playback controls
        self.controls = PlaybackControls(self)
        
        # Add widgets to main layout
        self.layout.addWidget(self.status_bar)
        self.layout.addWidget(self.player_stack, 1)  # Give player most space
        self.layout.addWidget(self.controls)
        
        # Initially show local player
        self.player_stack.setCurrentWidget(self.local_player)
        self.current_player = self.local_player
        
    def connect_signals(self):
        """Connect signals from players and controls"""
        # Online player signals
        self.online_player.error_occurred.connect(self.error_occurred.emit)
        self.online_player.playback_started.connect(self.playback_started.emit)
        self.online_player.status_changed.connect(self.update_status)
        
        # Local player signals
        self.local_player.error_occurred.connect(self.error_occurred.emit)
        self.local_player.playback_started.connect(self.playback_started.emit)
        self.local_player.status_changed.connect(self.update_status)
        self.local_player.duration_changed.connect(self.controls.set_duration)
        self.local_player.position_changed.connect(self.controls.set_position)
        
        # Control signals
        self.controls.play_clicked.connect(self.play)
        self.controls.pause_clicked.connect(self.pause)
        self.controls.stop_clicked.connect(self.stop)
        self.controls.seek_requested.connect(self.seek)
        self.controls.volume_changed.connect(self.set_volume)
        
    def load(self, url_or_path):
        """Load video content, automatically detecting type"""
        try:
            logger.info(f"🎬 Loading content: {url_or_path}")
            
            if isinstance(url_or_path, dict):
                # Video info object from search
                self.load_video_info(url_or_path)
            elif isinstance(url_or_path, str):
                if self._is_url(url_or_path):
                    # Online content
                    video_info = {
                        'url': url_or_path,
                        'playable_url': url_or_path,
                        'title': 'Online Video',
                        'platform': self._detect_platform(url_or_path)
                    }
                    self.load_video_info(video_info)
                else:
                    # Local file
                    self._switch_to_local_player()
                    self.local_player.load_video(url_or_path)
                    self.current_player.play()
            else:
                raise ValueError("Invalid input type for video loading")
                
        except Exception as e:
            logger.error(f"Load error: {e}")
            self.error_occurred.emit(f"Failed to load content: {str(e)}")
    
    def load_video_info(self, video_info):
        """Load video from video info dictionary"""
        try:
            self.current_video_info = video_info.copy()
            
            # Determine if this is online or local content
            url = video_info.get('playable_url') or video_info.get('url', '')
            
            if self._is_url(url):
                # Online content
                self._switch_to_online_player()
                self.online_player.load_video(video_info)
            else:
                # Local file
                self._switch_to_local_player()
                self.local_player.load_video(url)
                self.current_player.play()
                
        except Exception as e:
            logger.error(f"Video info load error: {e}")
            self.error_occurred.emit(f"Failed to load video: {str(e)}")
    
    def _is_url(self, path):
        """Check if path is a URL"""
        return isinstance(path, str) and path.startswith(('http://', 'https://', 'www.'))
    
    def _detect_platform(self, url):
        """Detect platform from URL"""
        url_lower = url.lower()
        if 'youtube.com' in url_lower or 'youtu.be' in url_lower:
            return 'youtube'
        elif 'facebook.com' in url_lower or 'fb.watch' in url_lower:
            return 'facebook'
        elif 'instagram.com' in url_lower:
            return 'instagram'
        elif 'twitter.com' in url_lower or 'x.com' in url_lower:
            return 'twitter'
        else:
            return 'unknown'
    
    def _switch_to_online_player(self):
        """Switch to online player"""
        self.player_stack.setCurrentWidget(self.online_player)
        self.current_player = self.online_player
        logger.info("🌐 Switched to online player")
    
    def _switch_to_local_player(self):
        """Switch to local player"""
        self.player_stack.setCurrentWidget(self.local_player)
        self.current_player = self.local_player
        logger.info("📁 Switched to local player")
    
    def play(self):
        """Start playback on current player"""
        if self.current_player:
            self.current_player.play()
            self.controls.set_playing()
    
    def pause(self):
        """Pause playback on current player"""
        if self.current_player:
            self.current_player.pause()
            self.controls.set_paused()
    
    def stop(self):
        """Stop playback on current player"""
        if self.current_player:
            self.current_player.stop()
            self.controls.set_stopped()
    
    def seek(self, position_seconds):
        """Seek to position (only works for local player)"""
        if self.current_player == self.local_player:
            position_ms = position_seconds * 1000
            self.local_player.seek(position_ms)
    
    def set_volume(self, volume):
        """Set volume on current player"""
        if self.current_player:
            self.current_player.set_volume(volume)
    
    def update_status(self, message):
        """Update status label"""
        self.status_label.setText(message)
        self.status_changed.emit(message)
    
    def show_codec_help(self):
        """Show codec installation help"""
        if self.current_player == self.local_player:
            self.local_player.show_codec_installation_guide()
        else:
            # Show general help for online content
            from PyQt5.QtWidgets import QMessageBox
            help_text = """
🌐 Online Video Playback Help

For online videos, the app tries multiple methods:

1. 🎬 Direct Stream: Extracts direct video URL for best quality
2. 🌐 Web Player: Uses embedded web view for compatibility  
3. 🔗 Browser: Opens in your default browser as fallback

💡 Tips:
• Ensure you have a stable internet connection
• Some platforms may block direct streaming
• Browser playback always works as final fallback
• For best experience, download videos for offline viewing

🔧 Troubleshooting:
• Clear browser cache if web player has issues
• Check firewall/antivirus settings
• Try different video quality settings
            """
            
            msg = QMessageBox(self)
            msg.setWindowTitle("Online Video Help")
            msg.setText(help_text)
            msg.setIcon(QMessageBox.Information)
            msg.exec_()
    
    def get_current_video_path(self):
        """Get current video path/URL"""
        if self.current_player == self.local_player:
            return self.local_player.current_video_path
        elif self.current_player == self.online_player:
            return self.online_player.current_video_url
        return None
    
    def has_video_loaded(self):
        """Check if video is loaded"""
        return self.get_current_video_path() is not None
    
    # Legacy compatibility methods
    def load_video(self, url_or_path):
        """Legacy method for compatibility"""
        self.load(url_or_path)

    def _emit_playback_started(self):
        """Legacy method for compatibility"""
        self.playback_started.emit()

    def on_error(self, error_message):
        """Legacy error handler"""
        self.error_occurred.emit(error_message)

    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if self.current_player == self.local_player:
            if hasattr(self.local_player.player, 'state'):
                if self.local_player.player.state() == self.local_player.player.PlayingState:
                    self.pause()
                else:
                    self.play()
            else:
                self.play()
        else:
            # For online player, just try to play
            self.play()

    def seek_forward(self):
        """Seek forward (only works for local player)"""
        if self.current_player == self.local_player:
            current_pos = self.local_player.get_position()
            new_pos = current_pos + 5000  # 5 seconds forward
            self.local_player.seek(new_pos)

    def seek_backward(self):
        """Seek backward (only works for local player)"""
        if self.current_player == self.local_player:
            current_pos = self.local_player.get_position()
            new_pos = max(0, current_pos - 5000)  # 5 seconds backward
            self.local_player.seek(new_pos)
