# -*- coding: utf-8 -*-
from core.libraries import *
from utils.caption_utils import load_captions, burn_captions
from editing.color_selection import CAPTION_PRESETS
from editing.font_selection import list_fonts
from typing import Dict, Optional
import textwrap
import os
import moviepy.editor as mp
from utils.video_utils import to_seconds 

def render_captions_on_video(video_path: str, caption_path: str, 
                           font_family: str="Jameel Noori Nastaleeq",
                           preset_name: str="default",
                           custom_style: Optional[Dict]=None,
                           position: str="bottom",
                           animation: Optional[str]=None) -> str:
    """Render captions with advanced styling options."""
    captions = load_captions(caption_path)
    
    # Get style configuration
    style = get_style_config(preset_name, custom_style)
    
    return burn_advanced_captions(
        video_path, captions, font_family, style, position, animation
    )

def get_style_config(preset_name: str, custom_style: Optional[Dict]) -> Dict:
    """Get style configuration merging preset and custom styles."""
    if preset_name not in CAPTION_PRESETS:
        preset_name = "default"
    
    style = CAPTION_PRESETS[preset_name].copy()
    if custom_style:
        style.update(custom_style)
    
    return style

def burn_advanced_captions(video_path: str, captions, font_family: str, style: Dict, 
                         position: str, animation: Optional[str]) -> str:
    """Apply advanced caption rendering with effects."""
    clip = mp.VideoFileClip(video_path)
    
    # Create text clips with advanced styling
    text_clips = []
    for caption in captions:
        txt_clip = create_styled_text_clip(
            caption['text'], caption['start'], caption['end'], 
            font_family, style, position, animation
        )
        text_clips.append(txt_clip)
    
    # Composite text clips onto video
    final_clip = mp.CompositeVideoClip([clip] + text_clips)
    
    # Generate output path
    out_path = os.path.splitext(video_path)[0] + "_captioned.mp4"
    
    # Write output
    final_clip.write_videofile(
        out_path,
        codec="libx264",
        audio_codec="aac",
        threads=4
    )
    
    clip.close()
    final_clip.close()
    
    return out_path

def create_styled_text_clip(text: str, start: float, end: float,
                          font_family: str, style: Dict, 
                          position: str, animation: Optional[str]):
    """Create a text clip with advanced styling and animation."""
    # Wrap text for better readability
    wrapped_text = wrap_text(text, font_family, style.get('font_size', 24))
    
    # Create base text clip
    txt_clip = mp.TextClip(
        wrapped_text,
        fontsize=style.get('font_size', 24),
        color=style['text'],
        font=font_family,
        stroke_color=style.get('stroke'),
        stroke_width=style.get('stroke_width', 2),
        method='label'
    )
    
    # Set duration and position
    txt_clip = txt_clip.set_start(start).set_duration(end - start)
    txt_clip = set_text_position(txt_clip, position)
    
    # Apply background if specified
    if style.get('bg'):
        txt_clip = add_text_background(txt_clip, style['bg'])
    
    # Apply animation
    if animation:
        txt_clip = apply_animation(txt_clip, animation)
    
    return txt_clip

def wrap_text(text: str, font_family: str, font_size: int, max_width: int = 30) -> str:
    """Wrap text to fit within video width."""
    return "\n".join(textwrap.wrap(text, width=max_width))

def set_text_position(text_clip, position: str):
    """Set text position on screen."""
    if position == "top":
        return text_clip.set_position(('center', 'top'))
    elif position == "center":
        return text_clip.set_position('center')
    else:  # bottom
        return text_clip.set_position(('center', 'bottom'))

def add_text_background(text_clip, bg_color: str):
    """Add background to text clip."""
    # Create a background clip slightly larger than text
    bg = mp.ColorClip(
        size=(text_clip.w + 20, text_clip.h + 10),
        color=bg_color
    ).set_opacity(0.8)
    
    # Composite text over background
    return mp.CompositeVideoClip([bg, text_clip.set_position('center')])

def apply_animation(text_clip, animation: str):
    """Apply animation effects to text."""
    if animation == "fade":
        return text_clip.crossfadein(0.5).crossfadeout(0.5)
    elif animation == "slide":
        # Slide in from bottom, slide out to top
        return text_clip.set_position(
            lambda t: ('center', 'bottom' if t < 0.5 else 'top')
        )
    return text_clip

def create_animated_text_overlay(video_path: str, text: str, start: float, end: float,
                               style: Dict, animation: str = "fade") -> str:
    """Create a video with animated text overlay."""
    clip = mp.VideoFileClip(video_path)
    
    # Create animated text clip
    txt_clip = mp.TextClip(
        text,
        fontsize=style.get('font_size', 36),
        color=style.get('text', 'white'),
        font=style.get('font_family', 'Arial'),
        stroke_color=style.get('stroke', 'black'),
        stroke_width=style.get('stroke_width', 2)
    ).set_start(start).set_duration(end - start)
    
    # Apply animation
    if animation == "fade":
        txt_clip = txt_clip.fadein(0.5).fadeout(0.5)
    
    # Composite and save
    final = mp.CompositeVideoClip([clip, txt_clip.set_position('center')])
    out_path = os.path.splitext(video_path)[0] + "_text_overlay.mp4"
    final.write_videofile(out_path, codec="libx264", audio_codec="aac")
    
    clip.close()
    final.close()
    
    return out_path
