# -*- coding: utf-8 -*-
"""
Font selection utilities for video editing
"""

from core.libraries import *
from core.theme import *
import os
import platform

# Try to import FONTS_DIR from config, fallback if not available
try:
    from core.config import FONTS_DIR
except ImportError:
    FONTS_DIR = "assets/fonts"

def list_fonts():
    """List available fonts from both system and custom fonts directory"""
    fonts = []

    # Get system fonts
    system_fonts = get_system_fonts()
    fonts.extend(system_fonts)

    # Get custom fonts from assets directory
    if os.path.exists(FONTS_DIR):
        custom_fonts = [f for f in os.listdir(FONTS_DIR) if f.lower().endswith(('.ttf','.otf'))]
        fonts.extend(custom_fonts)

    # Add some common fallback fonts
    fallback_fonts = [
        "Arial", "Times New Roman", "Courier New", "Helvetica",
        "Georgia", "Verdana", "Tahoma", "Comic Sans MS"
    ]

    # Combine and remove duplicates
    all_fonts = list(set(fonts + fallback_fonts))
    return sorted(all_fonts)


def get_system_fonts():
    """Get fonts available on the system"""
    system = platform.system()
    fonts = []

    if system == "Windows":
        fonts = get_windows_fonts()
    elif system == "Darwin":  # macOS
        fonts = get_macos_fonts()
    else:  # Linux and others
        fonts = get_linux_fonts()

    return fonts


def get_windows_fonts():
    """Get fonts available on Windows"""
    fonts = []
    try:
        import winreg
        # Read fonts from Windows registry
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                           r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Fonts")

        i = 0
        while True:
            try:
                font_name, font_file, _ = winreg.EnumValue(key, i)
                # Extract font family name (remove style info)
                font_family = font_name.split('(')[0].strip()
                if font_family not in fonts:
                    fonts.append(font_family)
                i += 1
            except WindowsError:
                break

        winreg.CloseKey(key)
    except ImportError:
        # Fallback if winreg is not available
        fonts = get_common_fonts()

    return fonts


def get_macos_fonts():
    """Get fonts available on macOS"""
    fonts = []
    try:
        import subprocess
        # Use system_profiler to get font list
        result = subprocess.run(['system_profiler', 'SPFontsDataType'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Family:' in line:
                    font_name = line.split('Family:')[1].strip()
                    if font_name and font_name not in fonts:
                        fonts.append(font_name)
    except:
        fonts = get_common_fonts()

    return fonts


def get_linux_fonts():
    """Get fonts available on Linux"""
    fonts = []
    try:
        import subprocess
        # Use fc-list to get font list
        result = subprocess.run(['fc-list', '--format=%{family}\n'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if line.strip():
                    # Handle multiple family names separated by commas
                    family_names = line.split(',')
                    for family in family_names:
                        family = family.strip()
                        if family and family not in fonts:
                            fonts.append(family)
    except:
        fonts = get_common_fonts()

    return fonts


def get_common_fonts():
    """Get a list of commonly available fonts as fallback"""
    return [
        "Arial", "Times New Roman", "Courier New", "Helvetica",
        "Georgia", "Verdana", "Tahoma", "Comic Sans MS",
        "Calibri", "Cambria", "Consolas", "Franklin Gothic Medium",
        "Lucida Console", "Lucida Sans Unicode", "Microsoft Sans Serif",
        "Palatino Linotype", "Segoe UI", "Trebuchet MS",
        "DejaVu Sans", "Liberation Sans", "Ubuntu", "Roboto"
    ]


class FontSelector(QWidget):
    """Widget for selecting fonts"""

    font_changed = pyqtSignal(str)  # Font family name

    def __init__(self, initial_font="Arial", parent=None):
        super().__init__(parent)
        self.current_font = initial_font
        self.available_fonts = list_fonts()
        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """Setup the font selector UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Font combo box
        self.font_combo = QComboBox()
        self.font_combo.addItems(self.available_fonts)
        self.font_combo.setCurrentText(self.current_font)
        self.font_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 5px;
                min-width: 150px;
            }}
            QComboBox::drop-down {{
                border: none;
                background-color: {PANEL_COLOR};
            }}
            QComboBox::down-arrow {{
                image: none;
                border: none;
            }}
            QComboBox QAbstractItemView {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                selection-background-color: {ACCENT_COLOR};
                border: 1px solid {PANEL_COLOR};
            }}
        """)

        # Font preview
        self.preview_label = QLabel("Sample Text")
        self.preview_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                background-color: {PANEL_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 3px;
                padding: 5px;
                min-width: 100px;
                font-family: {self.current_font};
                font-size: 14px;
            }}
        """)

        # Add widgets to main layout
        layout.addWidget(QLabel("Font:"))
        layout.addWidget(self.font_combo, 1)
        layout.addWidget(self.preview_label)

    def connect_signals(self):
        """Connect widget signals"""
        self.font_combo.currentTextChanged.connect(self.on_font_changed)

    def on_font_changed(self, font_name):
        """Handle font selection changes"""
        self.current_font = font_name
        self.update_preview()
        self.font_changed.emit(font_name)

    def update_preview(self):
        """Update the font preview"""
        self.preview_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                background-color: {PANEL_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 3px;
                padding: 5px;
                min-width: 100px;
                font-family: {self.current_font};
                font-size: 14px;
            }}
        """)

    def set_font(self, font_name):
        """Set the current font"""
        if font_name in self.available_fonts:
            self.current_font = font_name
            self.font_combo.setCurrentText(font_name)
            self.update_preview()

    def get_font(self):
        """Get the current font"""
        return self.current_font


# Convenience functions
def select_font(initial_font="Arial", parent=None):
    """Show font selection dialog and return selected font"""
    try:
        font, ok = QInputDialog.getItem(parent, "Select Font", "Font:", list_fonts(), 0, False)
        if ok:
            return font
    except:
        pass
    return None


def create_font_selector(initial_font="Arial", parent=None):
    """Create a font selector widget"""
    return FontSelector(initial_font, parent)
