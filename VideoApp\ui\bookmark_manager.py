# -*- coding: utf-8 -*-
"""
Bookmark manager widget for saving and managing video bookmarks
"""

from core.libraries import *
from core.theme import *
import json
import os
from datetime import datetime


class BookmarkItem(QWidget):
    """Widget representing a single bookmark"""
    
    # Signals
    bookmark_selected = pyqtSignal(dict)  # bookmark data
    bookmark_deleted = pyqtSignal(str)    # bookmark id
    
    def __init__(self, bookmark_data, parent=None):
        super().__init__(parent)
        self.bookmark_data = bookmark_data
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """Setup the bookmark item UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Bookmark info
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(2)
        
        # Title
        title = self.bookmark_data.get('title', 'Untitled')
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-weight: bold;
                font-size: 12px;
            }}
        """)
        
        # Time and description
        time_str = self.format_time(self.bookmark_data.get('time', 0))
        description = self.bookmark_data.get('description', '')
        if description:
            detail_text = f"{time_str} - {description}"
        else:
            detail_text = time_str
        
        self.detail_label = QLabel(detail_text)
        self.detail_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_COLOR};
                font-size: 10px;
                opacity: 0.8;
            }}
        """)
        
        # Created date
        created = self.bookmark_data.get('created', '')
        if created:
            self.date_label = QLabel(f"Created: {created}")
            self.date_label.setStyleSheet(f"""
                QLabel {{
                    color: {TEXT_COLOR};
                    font-size: 9px;
                    opacity: 0.6;
                }}
            """)
        else:
            self.date_label = QLabel("")
        
        info_layout.addWidget(self.title_label)
        info_layout.addWidget(self.detail_label)
        info_layout.addWidget(self.date_label)
        
        # Action buttons
        buttons_widget = QWidget()
        buttons_layout = QVBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        # Go to bookmark button
        self.goto_btn = QPushButton("Go")
        self.goto_btn.setFixedSize(40, 25)
        self.goto_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 3px;
                font-size: 10px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
        """)
        
        # Delete bookmark button
        self.delete_btn = QPushButton("×")
        self.delete_btn.setFixedSize(25, 25)
        self.delete_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #b91c1c;
            }}
        """)
        
        buttons_layout.addWidget(self.goto_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addStretch()
        
        # Add to main layout
        layout.addWidget(info_widget, 1)
        layout.addWidget(buttons_widget)
        
        # Apply item styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 5px;
                margin: 2px;
            }}
            QWidget:hover {{
                background-color: {BACKGROUND_COLOR};
                border: 2px solid {ACCENT_COLOR};
            }}
        """)
    
    def connect_signals(self):
        """Connect widget signals"""
        self.goto_btn.clicked.connect(lambda: self.bookmark_selected.emit(self.bookmark_data))
        self.delete_btn.clicked.connect(lambda: self.bookmark_deleted.emit(self.bookmark_data.get('id', '')))
    
    def format_time(self, seconds):
        """Format seconds as MM:SS or HH:MM:SS"""
        seconds = int(seconds)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"


class BookmarkManagerWidget(QWidget):
    """Widget for managing video bookmarks"""
    
    # Signals
    bookmark_selected = pyqtSignal(float)  # time in seconds
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.bookmarks = {}  # id -> bookmark_data
        self.current_video_path = None
        self.bookmarks_file = "bookmarks.json"
        self.setup_ui()
        self.connect_signals()
        self.load_bookmarks()
    
    def setup_ui(self):
        """Setup the bookmark manager UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("Bookmarks")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {ACCENT_COLOR};
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 10px;
            }}
        """)
        
        # Add bookmark section
        add_widget = QWidget()
        add_layout = QHBoxLayout(add_widget)
        add_layout.setContentsMargins(0, 0, 0, 0)
        
        self.time_input = QLineEdit()
        self.time_input.setPlaceholderText("MM:SS or HH:MM:SS")
        self.time_input.setFixedWidth(100)
        self.time_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 5px;
                font-family: monospace;
            }}
        """)
        
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("Bookmark title")
        self.title_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {BACKGROUND_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {PANEL_COLOR};
                border-radius: 3px;
                padding: 5px;
            }}
        """)
        
        self.add_btn = QPushButton("Add")
        self.add_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
                border: none;
                border-radius: 3px;
                padding: 5px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {TEXT_COLOR};
            }}
        """)
        
        add_layout.addWidget(QLabel("Time:"))
        add_layout.addWidget(self.time_input)
        add_layout.addWidget(QLabel("Title:"))
        add_layout.addWidget(self.title_input, 1)
        add_layout.addWidget(self.add_btn)
        
        # Bookmarks list
        self.bookmarks_widget = QWidget()
        self.bookmarks_layout = QVBoxLayout(self.bookmarks_widget)
        self.bookmarks_layout.setContentsMargins(0, 0, 0, 0)
        self.bookmarks_layout.setSpacing(5)
        
        # Scroll area for bookmarks
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.bookmarks_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid {PANEL_COLOR};
                border-radius: 5px;
                background-color: {BACKGROUND_COLOR};
            }}
        """)
        
        # Controls
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        
        self.clear_btn = QPushButton("Clear All")
        self.clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #dc2626;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #b91c1c;
            }}
        """)
        
        self.export_btn = QPushButton("Export")
        self.export_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {PANEL_COLOR};
                color: {TEXT_COLOR};
                border: 1px solid {ACCENT_COLOR};
                border-radius: 3px;
                padding: 5px 15px;
            }}
            QPushButton:hover {{
                background-color: {ACCENT_COLOR};
                color: {BACKGROUND_COLOR};
            }}
        """)
        
        controls_layout.addWidget(self.clear_btn)
        controls_layout.addWidget(self.export_btn)
        controls_layout.addStretch()
        
        # Add all widgets to main layout
        layout.addWidget(title_label)
        layout.addWidget(add_widget)
        layout.addWidget(scroll_area, 1)
        layout.addWidget(controls_widget)
        
        # Apply overall styling
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PANEL_COLOR};
                border: 2px solid {ACCENT_COLOR};
                border-radius: 10px;
            }}
        """)
    
    def connect_signals(self):
        """Connect widget signals"""
        self.add_btn.clicked.connect(self.add_bookmark)
        self.clear_btn.clicked.connect(self.clear_bookmarks)
        self.export_btn.clicked.connect(self.export_bookmarks)
        self.title_input.returnPressed.connect(self.add_bookmark)
        self.time_input.returnPressed.connect(self.add_bookmark)
    
    def add_bookmark(self):
        """Add a new bookmark"""
        time_str = self.time_input.text().strip()
        title = self.title_input.text().strip()
        
        if not time_str:
            return
        
        try:
            # Parse time string
            time_seconds = self.parse_time(time_str)
            
            # Generate unique ID
            bookmark_id = f"bookmark_{len(self.bookmarks)}_{int(datetime.now().timestamp())}"
            
            # Create bookmark data
            bookmark_data = {
                'id': bookmark_id,
                'time': time_seconds,
                'title': title or f"Bookmark at {time_str}",
                'description': '',
                'created': datetime.now().strftime("%Y-%m-%d %H:%M"),
                'video_path': self.current_video_path
            }
            
            # Add to bookmarks
            self.bookmarks[bookmark_id] = bookmark_data
            self.add_bookmark_widget(bookmark_data)
            
            # Clear inputs
            self.time_input.clear()
            self.title_input.clear()
            
            # Save bookmarks
            self.save_bookmarks()
            
        except ValueError:
            QMessageBox.warning(self, "Invalid Time", "Please enter time in MM:SS or HH:MM:SS format")
    
    def add_bookmark_widget(self, bookmark_data):
        """Add a bookmark widget to the list"""
        bookmark_widget = BookmarkItem(bookmark_data)
        bookmark_widget.bookmark_selected.connect(self.on_bookmark_selected)
        bookmark_widget.bookmark_deleted.connect(self.remove_bookmark)
        
        self.bookmarks_layout.addWidget(bookmark_widget)
    
    def on_bookmark_selected(self, bookmark_data):
        """Handle bookmark selection"""
        time_seconds = bookmark_data.get('time', 0)
        self.bookmark_selected.emit(time_seconds)
    
    def remove_bookmark(self, bookmark_id):
        """Remove a bookmark"""
        if bookmark_id in self.bookmarks:
            del self.bookmarks[bookmark_id]
            self.refresh_bookmarks_display()
            self.save_bookmarks()
    
    def clear_bookmarks(self):
        """Clear all bookmarks"""
        reply = QMessageBox.question(
            self, "Clear Bookmarks", 
            "Are you sure you want to clear all bookmarks?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.bookmarks.clear()
            self.refresh_bookmarks_display()
            self.save_bookmarks()
    
    def refresh_bookmarks_display(self):
        """Refresh the bookmarks display"""
        # Clear existing widgets
        for i in reversed(range(self.bookmarks_layout.count())):
            child = self.bookmarks_layout.itemAt(i).widget()
            if child:
                child.deleteLater()
        
        # Add current bookmarks
        for bookmark_data in sorted(self.bookmarks.values(), key=lambda x: x.get('time', 0)):
            self.add_bookmark_widget(bookmark_data)
    
    def parse_time(self, time_str):
        """Parse time string to seconds"""
        parts = time_str.split(':')
        if len(parts) == 2:  # MM:SS
            minutes, seconds = map(int, parts)
            return minutes * 60 + seconds
        elif len(parts) == 3:  # HH:MM:SS
            hours, minutes, seconds = map(int, parts)
            return hours * 3600 + minutes * 60 + seconds
        else:
            raise ValueError("Invalid time format")
    
    def set_current_video(self, video_path):
        """Set the current video path"""
        self.current_video_path = video_path
    
    def add_bookmark_at_time(self, time_seconds, title=""):
        """Add a bookmark at a specific time"""
        self.time_input.setText(self.format_time(time_seconds))
        if title:
            self.title_input.setText(title)
        self.add_bookmark()
    
    def format_time(self, seconds):
        """Format seconds as MM:SS or HH:MM:SS"""
        seconds = int(seconds)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def save_bookmarks(self):
        """Save bookmarks to file"""
        try:
            with open(self.bookmarks_file, 'w') as f:
                json.dump(self.bookmarks, f, indent=2)
        except Exception as e:
            print(f"Failed to save bookmarks: {e}")
    
    def load_bookmarks(self):
        """Load bookmarks from file"""
        try:
            if os.path.exists(self.bookmarks_file):
                with open(self.bookmarks_file, 'r') as f:
                    self.bookmarks = json.load(f)
                self.refresh_bookmarks_display()
        except Exception as e:
            print(f"Failed to load bookmarks: {e}")
    
    def export_bookmarks(self):
        """Export bookmarks to a file"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Bookmarks", "bookmarks.json", "JSON Files (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    json.dump(self.bookmarks, f, indent=2)
                QMessageBox.information(self, "Export Complete", f"Bookmarks exported to {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "Export Failed", f"Failed to export bookmarks: {e}")


class BookmarkManagerGroupBox(QGroupBox):
    """Bookmark manager wrapped in a group box (for compatibility)"""
    
    bookmark_selected = pyqtSignal(float)
    
    def __init__(self, parent=None):
        super().__init__("Bookmarks", parent)
        layout = QVBoxLayout(self)
        
        self.manager = BookmarkManagerWidget()
        self.manager.bookmark_selected.connect(self.bookmark_selected.emit)
        
        layout.addWidget(self.manager)
        
        # Apply group box styling
        self.setStyleSheet(f"""
            QGroupBox {{
                color: {TEXT_COLOR};
                border: 2px solid {PANEL_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                font-weight: bold;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {ACCENT_COLOR};
            }}
        """)


# For backward compatibility - use the widget version as the main class
BookmarkManager = BookmarkManagerWidget
