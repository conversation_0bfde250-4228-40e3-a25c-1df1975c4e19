# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_submodules
from PyInstaller.utils.hooks import collect_all

datas = [('D:\\BISH\\VideoApp\\assets', 'assets'), ('D:\\BISH\\VideoApp\\core', 'core'), ('D:\\BISH\\VideoApp\\dashboard', 'dashboard'), ('D:\\BISH\\VideoApp\\ui', 'ui'), ('D:\\BISH\\VideoApp\\utils', 'utils'), ('D:\\BISH\\VideoApp\\editing', 'editing'), ('D:\\BISH\\VideoApp\\export', 'export'), ('D:\\BISH\\VideoApp\\social', 'social')]
binaries = []
hiddenimports = ['PyQt5', 'PyQt5.QtCore', 'PyQt5.QtWidgets', 'PyQt5.QtGui', 'PyQt5.QtMultimedia', 'PyQt5.QtMultimediaWidgets', 'PyQt5.QtWebEngineWidgets', 'PyQt5.QtNetwork', 'yt_dlp', 'yt_dlp.extractor', 'requests', 'json', 'threading', 'urllib.parse', 'urllib.request', 'datetime', 'os', 'sys', 'time', 're', 'typing', 'concurrent.futures', 'subprocess', 'tempfile', 'shutil', 'pathlib', 'webbrowser']
hiddenimports += collect_submodules('PyQt5')
tmp_ret = collect_all('yt_dlp')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'scipy', 'pandas'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='VideoApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['D:\\BISH\\VideoApp\\assets\\app_icon.ico'],
)
