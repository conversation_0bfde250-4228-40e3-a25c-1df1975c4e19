# -*- coding: utf-8 -*-
# Social media search functionality

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import re

class Platform(Enum):
    YOUTUBE = "youtube"
    VIMEO = "vimeo"
    INSTAGRAM = "instagram"
    TIKTOK = "tiktok"
    FACEBOOK = "facebook"
    TWITTER = "twitter"
    DAILYMOTION = "dailymotion"

@dataclass
class SearchResult:
    title: str
    url: str
    platform: Platform
    thumbnail: Optional[str] = None
    duration: Optional[float] = None
    views: Optional[int] = None
    upload_date: Optional[str] = None
    uploader: Optional[str] = None

def search_videos(
    query: str,
    platform: Optional[Platform] = None,
    max_results: int = 20,
    filters: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Search for videos across social media platforms.
    
    Args:
        query: Search query
        platform: Specific platform to search (None for all platforms)
        max_results: Maximum number of results to return
        filters: Additional filters (duration, date, etc.)
        
    Returns:
        Dictionary with search results
    """
    try:
        from utils.video_downloader import search_videos as yt_search
        
        # Convert platform to string if provided
        platform_str = platform.value if platform else None
        
        # Perform search
        raw_results = yt_search(
            query, 
            platform=platform_str, 
            max_results=max_results,
            filters=filters or {}
        )
        
        # Process results
        processed_results = []
        for result in raw_results:
            # Determine platform from URL
            result_platform = detect_platform_from_url(result.get('url', ''))
            
            processed_results.append(SearchResult(
                title=result.get('title', ''),
                url=result.get('url', ''),
                platform=result_platform,
                thumbnail=result.get('thumbnail'),
                duration=result.get('duration'),
                views=result.get('view_count'),
                upload_date=result.get('upload_date'),
                uploader=result.get('uploader')
            ))
        
        return {
            "success": True,
            "results": processed_results,
            "query": query,
            "platform": platform.value if platform else "all",
            "result_count": len(processed_results)
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Search failed: {str(e)}",
            "query": query
        }

def detect_platform_from_url(url: str) -> Platform:
    """
    Detect the platform from a URL.
    
    Args:
        url: URL to analyze
        
    Returns:
        Detected platform
    """
    url_lower = url.lower()
    
    if 'youtube.com' in url_lower or 'youtu.be' in url_lower:
        return Platform.YOUTUBE
    elif 'vimeo.com' in url_lower:
        return Platform.VIMEO
    elif 'instagram.com' in url_lower:
        return Platform.INSTAGRAM
    elif 'tiktok.com' in url_lower:
        return Platform.TIKTOK
    elif 'facebook.com' in url_lower or 'fb.com' in url_lower:
        return Platform.FACEBOOK
    elif 'twitter.com' in url_lower or 'x.com' in url_lower:
        return Platform.TWITTER
    elif 'dailymotion.com' in url_lower:
        return Platform.DAILYMOTION
    else:
        # Try to detect based on patterns
        if re.search(r'(youtube|youtu\.be)', url_lower):
            return Platform.YOUTUBE
        elif 'vimeo' in url_lower:
            return Platform.VIMEO
        elif 'instagram' in url_lower:
            return Platform.INSTAGRAM
        elif 'tiktok' in url_lower:
            return Platform.TIKTOK
        elif 'facebook' in url_lower:
            return Platform.FACEBOOK
        elif 'twitter' in url_lower:
            return Platform.TWITTER
        elif 'dailymotion' in url_lower:
            return Platform.DAILYMOTION
        else:
            return Platform.YOUTUBE  # Default to YouTube

def get_trending_videos(
    platform: Platform = Platform.YOUTUBE,
    category: Optional[str] = None,
    region: str = "US",
    max_results: int = 20
) -> Dict[str, Any]:
    """
    Get trending videos from a platform.
    
    Args:
        platform: Platform to get trending videos from
        category: Content category (e.g., "music", "gaming")
        region: Region code (e.g., "US", "GB")
        max_results: Maximum number of results to return
        
    Returns:
        Dictionary with trending videos
    """
    try:
        from utils.video_downloader import get_trending_videos as yt_trending
        
        raw_results = yt_trending(
            platform=platform.value,
            category=category,
            region=region,
            max_results=max_results
        )
        
        # Process results
        processed_results = []
        for result in raw_results:
            processed_results.append(SearchResult(
                title=result.get('title', ''),
                url=result.get('url', ''),
                platform=platform,
                thumbnail=result.get('thumbnail'),
                duration=result.get('duration'),
                views=result.get('view_count'),
                upload_date=result.get('upload_date'),
                uploader=result.get('uploader')
            ))
        
        return {
            "success": True,
            "results": processed_results,
            "platform": platform.value,
            "category": category,
            "region": region,
            "result_count": len(processed_results)
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to get trending videos: {str(e)}",
            "platform": platform.value
        }

def search_channels(
    query: str,
    platform: Platform = Platform.YOUTUBE,
    max_results: int = 10
) -> Dict[str, Any]:
    """
    Search for channels on a platform.
    
    Args:
        query: Search query
        platform: Platform to search on
        max_results: Maximum number of results to return
        
    Returns:
        Dictionary with channel search results
    """
    try:
        from utils.video_downloader import search_channels as yt_search_channels
        
        raw_results = yt_search_channels(
            query, 
            platform=platform.value,
            max_results=max_results
        )
        
        return {
            "success": True,
            "results": raw_results,
            "query": query,
            "platform": platform.value,
            "result_count": len(raw_results)
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Channel search failed: {str(e)}",
            "query": query
        }
