# -*- coding: utf-8 -*-

import os
from core.libraries import *

def replace_video_audio(video_path: str, audio_path: str) -> str:
    if not os.path.exists(video_path):
        raise FileNotFoundError(video_path)
    if not os.path.exists(audio_path):
        raise FileNotFoundError(audio_path)
    v = mp.VideoFileClip(video_path)
    a = mp.AudioFileClip(audio_path)
    out_path = os.path.splitext(video_path)[0] + "_with_new_audio.mp4"
    v.set_audio(a).write_videofile(out_path, codec="libx264", audio_codec="aac")
    v.close(); a.close()
    return out_path
