# -*- coding: utf-8 -*-
"""
Enhanced Video Player Widget with editing capabilities
"""

import os
import threading
import logging
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QSlider, QMessageBox, QLabel, QFrame
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QTimer
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
import subprocess
import platform
import json
import webbrowser

# Try to import video widget with fallback
try:
    from PyQt5.QtMultimediaWidgets import QVideoWidget
    VIDEO_WIDGET_AVAILABLE = True
except ImportError:
    print("⚠️ QVideoWidget not available - using fallback")
    QVideoWidget = None
    VIDEO_WIDGET_AVAILABLE = False

# Try to import web engine with fallback
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    WEB_ENGINE_AVAILABLE = True
except ImportError:
    print("⚠️ QWebEngineView not available - using fallback")
    QWebEngineView = None
    WEB_ENGINE_AVAILABLE = False

from PyQt5.QtGui import QImage, QPixmap, QPainter, QFont, QIcon
import yt_dlp

# Set up logging
logger = logging.getLogger(__name__)

class VideoPlayerWidget(QWidget):
    # Signals for cross-thread communication and editing features
    error_occurred = pyqtSignal(str)
    playback_started = pyqtSignal()
    frame_captured = pyqtSignal(QImage)
    time_changed = pyqtSignal(int)
    duration_loaded = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.error_message = ""
        self.is_playing = False
        self.current_frame = None
        self.markers = []
        self.current_video_path = None
        self.status_label = parent.status_label if hasattr(parent, 'status_label') else None  # Access parent's status_label if available

        # Enable drag and drop
        self.setAcceptDrops(True)

        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)

        # --- Multimedia Player (local files) ---
        if VIDEO_WIDGET_AVAILABLE:
            self.video_widget = QVideoWidget(self)
        else:
            # Fallback to a simple label
            self.video_widget = QLabel("Video playback not available\n(QVideoWidget missing)")
            self.video_widget.setAlignment(Qt.AlignCenter)
            self.video_widget.setStyleSheet("background-color: black; color: white; font-size: 14px;")

        self.player = QMediaPlayer(self)

        if VIDEO_WIDGET_AVAILABLE:
            self.player.setVideoOutput(self.video_widget)

        # Initialize audio with proper volume
        self.player.setVolume(80)  # Set default volume to 80%

        # Connect player signals
        self.player.error.connect(self.handle_player_error)
        self.player.durationChanged.connect(self._on_dur)
        self.player.positionChanged.connect(self._on_pos)
        self.player.stateChanged.connect(self._on_state_changed)
        self.error_occurred.connect(self.on_error)  # Connect error signal

        # --- Web Player (online links) ---
        if WEB_ENGINE_AVAILABLE:
            self.webview = QWebEngineView(self)
        else:
            # Fallback to a simple label
            self.webview = QLabel("Web video playback not available\n(QWebEngineView missing)")
            self.webview.setAlignment(Qt.AlignCenter)
            self.webview.setStyleSheet("background-color: black; color: white; font-size: 14px;")

        # Player container with aspect ratio control
        player_container = QWidget(self)
        player_layout = QVBoxLayout(player_container)
        player_layout.setContentsMargins(0, 0, 0, 0)
        player_layout.addWidget(self.video_widget, 1)
        player_layout.addWidget(self.webview, 1)
        if hasattr(self.webview, 'hide'):
            self.webview.hide()

        # Time display
        time_display = QHBoxLayout()
        self.current_time_label = QLabel("00:00:00")
        self.total_time_label = QLabel("00:00:00")
        self.current_time_label.setStyleSheet("color: #ccc; font-size: 12px;")
        self.total_time_label.setStyleSheet("color: #ccc; font-size: 12px;")
        
        time_display.addWidget(self.current_time_label)
        time_display.addStretch()
        time_display.addWidget(self.total_time_label)
        
        # Timeline with markers
        timeline_container = QWidget()
        timeline_layout = QVBoxLayout(timeline_container)
        timeline_layout.setContentsMargins(0, 0, 0, 0)
        
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(0, 1000)
        self.slider.sliderMoved.connect(self._on_seek)
        
        # Markers area
        self.markers_widget = QWidget()
        self.markers_widget.setFixedHeight(20)
        self.markers_widget.setStyleSheet("background: transparent;")
        
        timeline_layout.addWidget(self.slider)
        timeline_layout.addWidget(self.markers_widget)

        # Controls
        controls = QHBoxLayout()
        controls.setSpacing(5)
        
        self.btn_play = QPushButton("▶️")
        self.btn_play.setIcon(QIcon.fromTheme("media-playback-start"))
        self.btn_play.setToolTip("Play/Pause")
        self.btn_play.setFixedSize(40, 32)

        self.btn_stop = QPushButton("⏹️")
        self.btn_stop.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.btn_stop.setToolTip("Stop")
        self.btn_stop.setFixedSize(40, 32)

        self.btn_capture = QPushButton("📷")
        self.btn_capture.setIcon(QIcon.fromTheme("camera-photo"))
        self.btn_capture.setToolTip("Capture Frame")
        self.btn_capture.setFixedSize(50, 32)

        self.btn_marker = QPushButton("📌")
        self.btn_marker.setIcon(QIcon.fromTheme("bookmark-new"))
        self.btn_marker.setToolTip("Add Marker")
        self.btn_marker.setFixedSize(40, 32)

        self.btn_fullscreen = QPushButton("⛶")
        self.btn_fullscreen.setIcon(QIcon.fromTheme("view-fullscreen"))
        self.btn_fullscreen.setToolTip("Toggle Fullscreen")
        self.btn_fullscreen.setFixedSize(40, 32)

        # Volume controls
        self.volume_icon = QLabel("🔊")
        self.volume_icon.setFixedSize(24, 24)
        self.volume_icon.setAlignment(Qt.AlignCenter)
        self.volume_icon.setStyleSheet("color: #ccc; font-size: 16px;")

        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)
        self.volume_slider.setFixedWidth(100)
        self.volume_slider.setToolTip("Volume")
        self.volume_slider.valueChanged.connect(self._on_volume_changed)

        self.volume_label = QLabel("80%")
        self.volume_label.setFixedWidth(30)
        self.volume_label.setStyleSheet("color: #ccc; font-size: 11px;")

        # Add spacing between button groups to prevent overlapping
        controls.addWidget(self.btn_play)
        controls.addSpacing(5)
        controls.addWidget(self.btn_stop)
        controls.addStretch()
        controls.addWidget(self.btn_capture)
        controls.addSpacing(5)
        controls.addWidget(self.btn_marker)
        controls.addStretch()
        controls.addWidget(self.volume_icon)
        controls.addSpacing(3)
        controls.addWidget(self.volume_slider)
        controls.addSpacing(3)
        controls.addWidget(self.volume_label)
        controls.addSpacing(5)
        controls.addWidget(self.btn_fullscreen)

        # Edit Part button
        self.btn_edit_part = QPushButton("✂️ Edit Part")
        self.btn_edit_part.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: black;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:pressed {
                background-color: #d39e00;
            }
        """)
        self.btn_edit_part.setToolTip("Edit selected video segment")
        controls.addWidget(self.btn_edit_part)

        # Add crop controls section
        crop_controls = QHBoxLayout()
        
        self.crop_start_btn = QPushButton("📍 Start Crop")
        self.crop_start_btn.setStyleSheet("""
            QPushButton {
                background-color: #2e7d32;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388e3c;
            }
        """)
        
        self.crop_end_btn = QPushButton("🏁 End Crop")
        self.crop_end_btn.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f44336;
            }
        """)
        
        self.crop_btn = QPushButton("✂️ Add to Timeline")
        self.crop_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:disabled {
                background-color: #666;
                color: #999;
            }
        """)
        self.crop_btn.setEnabled(False)
        
        self.crop_start_label = QLabel("Start: --:--")
        self.crop_end_label = QLabel("End: --:--")
        self.crop_duration_label = QLabel("Duration: --:--")
        
        for label in [self.crop_start_label, self.crop_end_label, self.crop_duration_label]:
            label.setStyleSheet("color: #ccc; font-size: 12px;")
            label.setMinimumWidth(80)
        
        crop_controls.addWidget(self.crop_start_btn)
        crop_controls.addWidget(self.crop_start_label)
        crop_controls.addWidget(self.crop_end_btn)
        crop_controls.addWidget(self.crop_end_label)
        crop_controls.addWidget(self.crop_btn)
        crop_controls.addWidget(self.crop_duration_label)
        crop_controls.addStretch()
        
        # Add to main layout
        self.layout.addWidget(player_container, 1)
        self.layout.addLayout(time_display)
        self.layout.addWidget(timeline_container)
        self.layout.addLayout(controls)
        self.layout.addLayout(crop_controls)

        # Connections
        self.btn_play.clicked.connect(self.toggle_play)
        self.btn_stop.clicked.connect(self.stop)
        self.btn_capture.clicked.connect(self.capture_current_frame)
        self.btn_marker.clicked.connect(self.add_marker)
        self.btn_fullscreen.clicked.connect(self.toggle_fullscreen)
        self.btn_edit_part.clicked.connect(self.open_video_editor)
        self.crop_start_btn.clicked.connect(self.set_crop_start)
        self.crop_end_btn.clicked.connect(self.set_crop_end)
        self.crop_btn.clicked.connect(self.add_crop_to_timeline)
        
        # Connect signals
        self.playback_started.connect(self.on_playback_start)

        # Set styles
        self.set_control_styles()

        # Add drop indicator
        self.drop_indicator = QLabel("Drop video files here to preview")
        self.drop_indicator.setAlignment(Qt.AlignCenter)
        self.drop_indicator.setStyleSheet("""
            QLabel {
                color: #888;
                font-size: 16px;
                background-color: rgba(0, 0, 0, 0.1);
                border: 2px dashed #555;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)
        self.drop_indicator.hide()  # Initially hidden

        # Crop state variables
        self.crop_start_time = -1
        self.crop_end_time = -1

    def dragEnterEvent(self, event):
        """Handle drag enter event"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and len(urls) > 0:
                file_path = urls[0].toLocalFile()
                if file_path and self.is_video_file(file_path):
                    event.acceptProposedAction()
                    self.show_drop_indicator()
                    return
        event.ignore()

    def dragLeaveEvent(self, event):
        """Handle drag leave event"""
        self.hide_drop_indicator()
        event.accept()

    def dropEvent(self, event):
        """Handle drop event"""
        self.hide_drop_indicator()
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and len(urls) > 0:
                file_path = urls[0].toLocalFile()
                if file_path and self.is_video_file(file_path):
                    self.load(file_path)
                    event.acceptProposedAction()
                    return
        event.ignore()

    def is_video_file(self, file_path):
        """Check if file is a video file"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
        return any(file_path.lower().endswith(ext) for ext in video_extensions)

    def show_drop_indicator(self):
        """Show drop indicator overlay"""
        if hasattr(self, 'drop_indicator'):
            self.drop_indicator.show()
            self.drop_indicator.raise_()

    def hide_drop_indicator(self):
        """Hide drop indicator overlay"""
        if hasattr(self, 'drop_indicator'):
            self.drop_indicator.hide()

    def set_control_styles(self):
        """Set styles for controls"""
        style = """
            QPushButton {
                background: #444;
                color: white;
                border: 1px solid #666;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #555;
                border: 1px solid #777;
            }
            QPushButton:pressed {
                background: #666;
                border: 1px solid #888;
            }
            QSlider::groove:horizontal {
                border: 1px solid #444;
                height: 6px;
                background: #666;
                margin: 0px;
            }
            QSlider::handle:horizontal {
                background: #0078d4;
                border: 1px solid #005a9e;
                width: 12px;
                margin: -3px 0;
                border-radius: 6px;
            }
            QSlider::sub-page:horizontal {
                background: #0078d4;
            }
            QSlider::add-page:horizontal {
                background: #333;
                border-radius: 3px;
            }
        """
        self.setStyleSheet(style)

    def handle_player_error(self, error):
        """Handle QMediaPlayer errors with crash protection"""
        try:
            error_messages = {
                QMediaPlayer.NoError: "No error",
                QMediaPlayer.ResourceError: "Resource error - Cannot play the media file.\n\n🔧 Possible solutions:\n• Install Windows Media Feature Pack\n• Install K-Lite Codec Pack\n• Try a different video format (MP4 recommended)\n• Check if file is corrupted",
                QMediaPlayer.FormatError: "Format error - Unsupported video format.\n\n🔧 Possible solutions:\n• Convert to MP4 format\n• Install additional codecs\n• Try a different player",
                QMediaPlayer.NetworkError: "Network error - Connection issue or invalid URL.\n\n🔧 Possible solutions:\n• Check internet connection\n• Verify URL is correct\n• Try again later",
                QMediaPlayer.AccessDeniedError: "Access denied - Permission issue or file is in use.\n\n🔧 Possible solutions:\n• Close other applications using the file\n• Run as administrator\n• Check file permissions",
                QMediaPlayer.ServiceMissingError: "Service missing - Media service is not available.\n\n🔧 Possible solutions:\n• Install Qt multimedia components\n• Install FFmpeg\n• Restart the application",
            }

            error_str = self.player.errorString()
            self.error_message = error_messages.get(error, f"Media error {error}: {error_str}")
            logger.error(f"🚨 Media Player Error: {self.error_message}")

            # Stop player safely to prevent crashes
            try:
                if self.player and hasattr(self.player, 'stop'):
                    if self.player.state() != QMediaPlayer.StoppedState:
                        self.player.stop()
                    self.player.setMedia(QMediaContent())
            except Exception as stop_error:
                logger.error(f"⚠️ Error stopping player: {stop_error}")

            # Emit error signal safely
            try:
                self.error_occurred.emit(self.error_message)
            except Exception as emit_error:
                logger.error(f"⚠️ Error emitting signal: {emit_error}")

        except Exception as e:
            logger.error(f"❌ Critical error in error handler: {e}")
            # Last resort - try to emit a generic error
            try:
                self.error_occurred.emit("Critical playback error occurred")
            except:
                pass

    def on_error(self, message):
        """Handle error signal with enhanced help"""
        # Show enhanced error dialog with codec help
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("Playback Error")
        msg.setText("Media Playback Error")
        msg.setInformativeText(message)

        # Add codec help button for common errors
        if any(keyword in message.lower() for keyword in ['resource error', 'format error', 'codec', 'cannot play']):
            codec_help_btn = msg.addButton("Codec Help", QMessageBox.ActionRole)
            msg.addButton(QMessageBox.Ok)

            result = msg.exec_()
            if msg.clickedButton() == codec_help_btn:
                self.show_codec_help()
        else:
            msg.exec_()

        # Trigger fallback
        self._try_alternative_playback(self.current_video_path or message)

    def show_codec_help(self):
        """Show codec installation help"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("Codec Help")
        msg.setText("Recommended Codec Solutions")
        msg.setInformativeText(
            "For Windows:\n"
            "• Install Windows Media Feature Pack (for N versions)\n"
            "• Install K-Lite Codec Pack Standard\n\n"
            "For all systems:\n"
            "• Convert videos to MP4 format\n"
            "• Use H.264 video codec with AAC audio\n"
            "• Ensure files are not corrupted"
        )
        msg.exec_()

    def on_playback_start(self):
        """Handle playback start signal"""
        self.is_playing = True
        self.btn_play.setText("⏸️")
        if self.status_label:
            self.status_label.setText("▶️ Playing")
        logger.info("Playback started successfully")

    def load(self, url_or_path: str):
        """Load a video from URL or file path with enhanced error handling"""
        if not url_or_path:
            self.error_occurred.emit("No URL or path provided")
            return

        try:
            logger.info(f"🎬 Loading video: {url_or_path}")

            # Store current video path
            self.current_video_path = url_or_path if os.path.isfile(url_or_path) else None
            self.error_message = ""
            
            # Reset previous content
            self._reset_player_state()

            # Handle different types of URLs
            if isinstance(url_or_path, str):
                # Check if it's a URL or file path
                if url_or_path.startswith(('http://', 'https://', 'www.')):
                    # Handle online videos
                    if 'youtube.com' in url_or_path or 'youtu.be' in url_or_path:
                        # Extract direct MP4 stream
                        if hasattr(self.parent(), 'engine'):
                            video_info = self.parent().engine.fetch_from_link(url_or_path)
                            direct_url = video_info.get('stream_url')
                            if direct_url:
                                url_or_path = direct_url
                                logger.info(f"Extracted direct stream: {direct_url}")
                    self._load_online_video(url_or_path)
                else:
                    # Validate local file
                    validation = self._validate_video_file(url_or_path)
                    if not validation['valid']:
                        raise ValueError(validation['error'])
                    self._load_local_file(url_or_path)
            else:
                raise ValueError("Invalid URL or file path")

            # Emit signal after successful load
            QTimer.singleShot(500, self._emit_playback_started)

            # Monitor for errors
            QTimer.singleShot(2000, self._check_playback_status)

        except Exception as e:
            error_msg = f"Failed to load video: {str(e)}\n\nTry: Install codecs (K-Lite) or use MP4 format."
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)

    def get_current_video_path(self):
        """Get the currently loaded video path"""
        return self.current_video_path

    def has_video_loaded(self):
        """Check if a video is currently loaded"""
        return self.current_video_path is not None

    def _reset_player_state(self):
        """Reset player state safely"""
        try:
            if self.player and hasattr(self.player, 'stop'):
                if self.player.state() != QMediaPlayer.StoppedState:
                    self.player.stop()
                self.player.setMedia(QMediaContent())
        except Exception as e:
            logger.error(f"⚠️ Error resetting player: {e}")

    def _emit_playback_started(self):
        """Safely emit playback started signal"""
        try:
            self.playback_started.emit()
        except Exception as e:
            logger.error(f"⚠️ Error emitting signal: {e}")

    def _load_local_file(self, file_path):
        """Load a local video file"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")

            # Validate file
            abs_path = os.path.abspath(file_path)
            if not os.access(abs_path, os.R_OK):
                raise PermissionError(f"Cannot read file: {abs_path}")

            file_size = os.path.getsize(abs_path)
            if file_size == 0:
                raise ValueError(f"File is empty: {abs_path}")

            logger.info(f"📊 File size: {file_size} bytes")

            # Check file format compatibility
            file_ext = os.path.splitext(abs_path)[1].lower()
            if not self.is_supported_format(file_ext):
                self.show_format_help(file_ext)
                return

            # Create media content
            file_url = QUrl.fromLocalFile(abs_path)
            if not file_url.isValid():
                file_url = QUrl(f"file:///{abs_path.replace(os.sep, '/')}")
            media_content = QMediaContent(file_url)
            if media_content.isNull():
                raise ValueError("Failed to create valid media content")

            # Set the media content
            self.player.setMedia(media_content)
            self.current_video_path = file_path

            # Show video widget and hide webview for local files
            if hasattr(self.video_widget, 'show'):
                self.video_widget.show()
            if hasattr(self.webview, 'hide'):
                self.webview.hide()

            # Start playback
            QTimer.singleShot(1000, self._try_auto_play)

        except Exception as e:
            logger.error(f"Failed to load local file: {str(e)}")
            self.error_occurred.emit(f"Failed to load local file: {str(e)}")

    def is_supported_format(self, file_ext):
        """Check if file format is supported"""
        supported_formats = [
            '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp',
            '.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a', '.wma'
        ]
        return file_ext in supported_formats

    def show_format_help(self, file_ext):
        """Show help for unsupported formats"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("Unsupported Format")
        msg.setText(f"The file format '{file_ext}' is not supported.")
        msg.setInformativeText("""
Supported video formats:
• MP4 (recommended)
• AVI, MOV, MKV, WMV
• FLV, WebM, M4V, 3GP

Supported audio formats:
• MP3, WAV, AAC, FLAC
• OGG, M4A, WMA

To fix this issue:
1. Convert your file to MP4 format
2. Install additional codecs (K-Lite Codec Pack)
3. Install Windows Media Feature Pack
        """)
        msg.exec_()

    def _load_online_video(self, url):
        """Load an online video with fallback options"""
        try:
            # Check URL type
            if any(x in url for x in ["youtube.com", "youtu.be", "facebook.com", "fb.watch", "instagram.com", "twitter.com", "x.com"]):
                logger.info("🌐 Loading web content")
                self._use_webview(url)
            else:
                logger.info("🔗 Loading as direct stream")
                self._extract_and_load_stream(url)
                
        except Exception as e:
            logger.error(f"Failed to load online video: {str(e)}")
            self.error_occurred.emit(f"Failed to load online video: {str(e)}")

    def _extract_and_load_stream(self, url):
        """Extract direct stream URL using yt-dlp"""
        try:
            # Show loading message
            if hasattr(self, 'video_widget') and isinstance(self.video_widget, QLabel):
                self.video_widget.setText("Extracting video stream...")
            
            # Use yt-dlp to extract stream URL
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'format': 'best[ext=mp4]/best[height<=720]',  # Force MP4 for QMediaPlayer compatibility
                'extractaudio': False,
                'noplaylist': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                if 'url' in info:
                    stream_url = info['url']
                    media_content = QMediaContent(QUrl(stream_url))
                    self.player.setMedia(media_content)
                    self.current_video_path = url
                    
                    # Show video widget
                    if hasattr(self, 'webview'):
                        self.webview.hide()
                    if hasattr(self, 'video_widget'):
                        self.video_widget.show()
                        
                    self.player.play()
                else:
                    raise Exception("No stream URL found")
                    
        except Exception as e:
            logger.error(f"Failed to extract stream: {str(e)}")
            self.error_occurred.emit(f"Failed to extract stream: {str(e)}")

    def _use_webview(self, url):
        """Use QWebEngineView for web content with error handling"""
        try:
            if not WEB_ENGINE_AVAILABLE:
                raise RuntimeError("Web engine not available - cannot play web content")

            self.player.stop()
            if hasattr(self.video_widget, 'hide'):
                self.video_widget.hide()
            if hasattr(self.webview, 'show'):
                self.webview.show()

            # Validate URL
            if not url.startswith(('http://', 'https://')):
                raise ValueError(f"Invalid web URL: {url}")

            self.webview.setUrl(QUrl(url))
            self.current_video_path = url
            logger.info(f"WebView loading: {url}")

        except Exception as e:
            logger.error(f"Error in _use_webview: {e}")
            self.error_occurred.emit(f"WebView error: {str(e)}")

    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if self.is_playing:
            self.pause()
        else:
            self.play()

    def toggle_play(self):
        """Toggle between play and pause (alias for compatibility)"""
        self.toggle_play_pause()

    def seek_forward(self):
        """Seek forward by 5 seconds"""
        try:
            current_pos = self.player.position()
            new_pos = min(current_pos + 5000, self.player.duration())  # 5 seconds = 5000ms
            self.player.setPosition(new_pos)
        except Exception as e:
            logger.error(f"Error seeking forward: {e}")

    def seek_backward(self):
        """Seek backward by 5 seconds"""
        try:
            current_pos = self.player.position()
            new_pos = max(current_pos - 5000, 0)  # 5 seconds = 5000ms
            self.player.setPosition(new_pos)
        except Exception as e:
            logger.error(f"Error seeking backward: {e}")

    def play(self):
        """Play the media"""
        try:
            if not self.player:
                self.error_occurred.emit("Media player not initialized")
                return

            # Check if media is loaded
            media_status = self.player.mediaStatus()
            logger.info(f"Media status before play: {media_status}")

            if media_status == QMediaPlayer.NoMedia:
                self.error_occurred.emit("No media loaded. Please select a video file first.")
                return
            elif media_status == QMediaPlayer.InvalidMedia:
                self.error_occurred.emit("Invalid media format. Please try a different video file.")
                return
            elif media_status == QMediaPlayer.LoadingMedia:
                logger.info("Media still loading, waiting...")
                # Wait a bit and try again
                QTimer.singleShot(1000, self.play)
                return

            self.player.play()
            self.is_playing = True
            self.btn_play.setText("⏸️")
            if self.status_label:
                self.status_label.setText("▶️ Playing")
            logger.info("Play command sent successfully")

        except Exception as e:
            logger.error(f"Error in play method: {e}")
            self.error_occurred.emit(f"Play error: {str(e)}")

    def pause(self):
        """Pause the media"""
        try:
            if self.player:
                self.player.pause()
                self.is_playing = False
                self.btn_play.setText("▶️")
                if self.status_label:
                    self.status_label.setText("⏸️ Paused")
                logger.info("Video paused")
        except Exception as e:
            logger.error(f"Error pausing video: {e}")

    def stop(self):
        """Stop playback"""
        try:
            if self.player:
                self.player.stop()
                self.is_playing = False
                self.btn_play.setText("▶️")
                self.slider.setValue(0)
                if self.status_label:
                    self.status_label.setText("⏹️ Stopped")
                logger.info("Video stopped")
        except Exception as e:
            logger.error(f"Error stopping video: {e}")

    def _on_dur(self, duration):
        """Handle duration change"""
        if duration > 0:
            self.slider.setRange(0, duration)
            self.total_time_label.setText(self.format_time(duration))
            self.duration_loaded.emit(duration)

    def _on_pos(self, position):
        """Handle position change"""
        if self.player.duration() > 0:
            self.slider.setValue(position)
            self.current_time_label.setText(self.format_time(position))
            self.time_changed.emit(position)

    def _on_seek(self, position):
        """Handle seek slider"""
        if self.player.isSeekable():
            self.player.setPosition(position)

    def _on_volume_changed(self, value):
        """Handle volume slider"""
        self.player.setVolume(value)
        self.volume_label.setText(f"{value}%")
        self.volume_icon.setText("🔊" if value > 0 else "🔇")

    def _on_state_changed(self, state):
        """Handle player state change"""
        if state == QMediaPlayer.PlayingState:
            self.is_playing = True
            self.btn_play.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.btn_play.setText("⏸️")
            self.btn_play.setToolTip("Pause")
            if self.status_label:
                self.status_label.setText("▶️ Playing")
        elif state in (QMediaPlayer.PausedState, QMediaPlayer.StoppedState):
            self.is_playing = False
            self.btn_play.setIcon(QIcon.fromTheme("media-playback-start"))
            self.btn_play.setText("▶️")
            self.btn_play.setToolTip("Play")
            if self.status_label:
                self.status_label.setText("⏸️ Paused" if state == QMediaPlayer.PausedState else "⏹️ Stopped")

    def toggle_fullscreen(self):
        """Toggle fullscreen"""
        if self.parent() and hasattr(self.parent(), 'toggle_fullscreen'):
            self.parent().toggle_fullscreen()
        elif self.video_widget.isVisible():
            if self.video_widget.isFullScreen():
                self.video_widget.setFullScreen(False)
            else:
                self.video_widget.setFullScreen(True)
        else:
            if self.isFullScreen():
                self.showNormal()
            else:
                self.showFullScreen()

    def capture_current_frame(self):
        """Capture current frame"""
        try:
            if self.current_frame:
                self.frame_captured.emit(self.current_frame)
                QMessageBox.information(self, "Frame Captured", "Frame has been captured for editing")
            else:
                # Create a mock frame (in reality, you'd capture from the video)
                frame = QImage(320, 240, QImage.Format_RGB32)
                frame.fill(Qt.darkGray)
                
                painter = QPainter(frame)
                painter.setPen(Qt.white)
                painter.setFont(QFont("Arial", 20))
                painter.drawText(frame.rect(), Qt.AlignCenter, "Captured Frame")
                painter.end()
                
                self.current_frame = frame
                self.frame_captured.emit(frame)
                QMessageBox.information(self, "Frame Captured", "Frame has been captured for editing")
                
        except Exception as e:
            logger.error(f"Error capturing frame: {e}")
            self.error_occurred.emit(f"Frame capture error: {str(e)}")

    def add_marker(self):
        """Add a marker at current position"""
        current_pos = self.player.position()
        if current_pos > 0:
            self.markers.append(current_pos)
            self.update_markers_display()
            QMessageBox.information(self, "Marker Added", f"Marker added at {self.format_time(current_pos)}")
            logger.info(f"Marker added at {self.format_time(current_pos)}")

    def update_markers_display(self):
        """Update the markers visualization"""
        # Clear existing markers
        for child in self.markers_widget.findChildren(QLabel):
            child.deleteLater()

        # Add new markers
        for i, marker in enumerate(self.markers):
            marker_label = QLabel(f"{i+1}", self.markers_widget)
            marker_label.setStyleSheet("""
                QLabel {
                    background-color: #ffc107;
                    color: black;
                    border-radius: 7px;
                    padding: 2px 5px;
                    font-size: 10px;
                    font-weight: bold;
                }
            """)
            # Position marker based on percentage of total duration
            if self.player.duration() > 0:
                pos_percent = marker / self.player.duration()
                x_pos = int(pos_percent * self.markers_widget.width()) - 7
                marker_label.move(x_pos, 5)

    def seek_to_marker(self, marker_index):
        """Seek to a specific marker"""
        if 0 <= marker_index < len(self.markers):
            position = int(self.markers[marker_index])
            self.player.setPosition(position)

    def set_crop_start(self):
        """Set the start time for cropping"""
        if self.player:
            self.crop_start_time = self.player.position() / 1000.0  # Convert to seconds
            self.crop_start_label.setText(f"Start: {self.format_time(self.crop_start_time * 1000)}")
            self.update_crop_button()

    def set_crop_end(self):
        """Set the end time for cropping"""
        if self.player:
            self.crop_end_time = self.player.position() / 1000.0  # Convert to seconds
            self.crop_end_label.setText(f"End: {self.format_time(self.crop_end_time * 1000)}")
            self.update_crop_button()
            
            # Calculate duration
            if self.crop_start_time > 0 and self.crop_end_time > self.crop_start_time:
                duration = self.crop_end_time - self.crop_start_time
                self.crop_duration_label.setText(f"Duration: {self.format_time(duration * 1000)}")

    def update_crop_button(self):
        """Enable/disable crop button based on selection"""
        has_valid_selection = (self.crop_start_time > 0 and 
                              self.crop_end_time > self.crop_start_time)
        self.crop_btn.setEnabled(has_valid_selection)

    def add_crop_to_timeline(self):
        """Add the cropped segment to the timeline"""
        if (self.crop_start_time > 0 and self.crop_end_time > self.crop_start_time and
            self.current_video_path):
            
            # Emit signal to parent to add to timeline
            if hasattr(self.parent(), 'add_cropped_segment'):
                self.parent().add_cropped_segment(
                    self.current_video_path,
                    self.crop_start_time,
                    self.crop_end_time
                )
            
            # Reset crop selection
            self.crop_start_time = -1
            self.crop_end_time = -1
            self.crop_start_label.setText("Start: --:--")
            self.crop_end_label.setText("End: --:--")
            self.crop_duration_label.setText("Duration: --:--")
            self.crop_btn.setEnabled(False)

    def open_video_editor(self):
        """Open video editor for the current video"""
        try:
            # Get current video info
            current_media = self.player.media()
            if current_media.isNull():
                QMessageBox.warning(self, "No Video", "Please load a video first before editing.")
                return

            # Get video URL/path
            video_url = current_media.canonicalUrl().toString()
            if not video_url:
                QMessageBox.warning(self, "No Video", "No video is currently loaded.")
                return

            if self.current_video_path and os.path.exists(self.current_video_path):
                if hasattr(self.parent(), 'open_video_editor'):
                    self.parent().open_video_editor(self.current_video_path)
                else:
                    # Get current position for start time
                    current_pos = self.player.position() / 1000.0  # Convert to seconds
                    duration = self.player.duration() / 1000.0 if self.player.duration() > 0 else 60.0

                    # For now, use current position as start and +30 seconds as end
                    start_time = max(0, current_pos)
                    end_time = min(duration, current_pos + 30)

                    logger.info(f"🎬 Opening video editor for: {video_url}")
                    logger.info(f"⏱️ Segment: {start_time:.1f}s - {end_time:.1f}s")

                    # Import and create video editor
                    from .video_editor import VideoEditorWindow

                    # Create editor window
                    self.video_editor = VideoEditorWindow(
                        video_path=video_url,
                        start_time=start_time,
                        end_time=end_time,
                        parent=self
                    )

                    # Show editor
                    self.video_editor.show()

                    # Show info message
                    QMessageBox.information(self, "Video Editor",
                                          f"Video Editor opened!\n\n"
                                          f"Editing segment: {start_time:.1f}s - {end_time:.1f}s\n"
                                          f"Duration: {end_time - start_time:.1f}s\n\n"
                                          f"Use the timeline to adjust the segment and apply effects.")
            else:
                QMessageBox.warning(self, "No Video", "No video loaded to edit")

        except Exception as e:
            logger.error(f"❌ Error opening video editor: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open video editor:\n{str(e)}")

    def format_time(self, milliseconds):
        """Format milliseconds to HH:MM:SS"""
        seconds = milliseconds // 1000
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def _use_qt_player_safe(self, path):
        """Safe wrapper for _use_qt_player with enhanced error handling"""
        try:
            logger.info(f"🎬 _use_qt_player_safe called with: {path}")

            # Pre-validate video file
            if os.path.exists(path):
                validation_result = self._validate_video_file(path)
                if not validation_result['valid']:
                    self.error_occurred.emit(validation_result['error'])
                    return

            self._use_qt_player(path)
        except Exception as e:
            logger.error(f"❌ Error in _use_qt_player_safe: {e}")
            self.error_occurred.emit(f"Playback error: {str(e)}")

    def _use_qt_player(self, path):
        """Use QMediaPlayer for playback with enhanced error handling"""
        try:
            logger.info(f"_use_qt_player called with path: {path}")

            # Check if player still exists
            if not self.player:
                logger.warning("Player object is None - recreating player")
                self._recreate_player()
                if not self.player:
                    raise RuntimeError("Failed to create media player")

            if hasattr(self.webview, 'hide'):
                self.webview.hide()
            if hasattr(self.video_widget, 'show'):
                self.video_widget.show()

            # Stop any current playback safely
            try:
                if self.player.state() != QMediaPlayer.StoppedState:
                    self.player.stop()
                    # Wait a bit for the stop to complete
                    QTimer.singleShot(100, lambda: self._continue_loading(path))
                    return
            except Exception as e:
                logger.warning(f"Warning: Error stopping player: {e}")

            self._continue_loading(path)

        except Exception as e:
            logger.error(f"Error in _use_qt_player: {e}")
            import traceback
            traceback.print_exc()
            try:
                self.error_occurred.emit(f"Player error: {str(e)}")
            except:
                logger.error("Could not emit error signal")

    def _continue_loading(self, path):
        """Continue loading after player is stopped"""
        try:
            # Create media content (local file or URL)
            if os.path.exists(path):
                logger.info(f"Loading local file: {path}")
                abs_path = os.path.abspath(path)
                logger.info(f"Absolute path: {abs_path}")

                # Check file permissions
                if not os.access(abs_path, os.R_OK):
                    raise PermissionError(f"Cannot read file: {abs_path}")

                # Check file size
                file_size = os.path.getsize(abs_path)
                logger.info(f"File size: {file_size} bytes")

                if file_size == 0:
                    raise ValueError("File is empty")

                # Check file extension for supported formats
                _, ext = os.path.splitext(abs_path.lower())
                supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
                if ext not in supported_formats:
                    logger.warning(f"Warning: {ext} format may not be supported. Supported formats: {supported_formats}")

                # Create media content with enhanced URL handling
                file_url = QUrl.fromLocalFile(abs_path)

                # Ensure URL is properly formatted for Windows
                if not file_url.isValid():
                    # Try alternative URL creation
                    file_url = QUrl(f"file:///{abs_path.replace(os.sep, '/')}")
                media_content = QMediaContent(file_url)
            else:
                logger.info(f"Loading URL: {path}")
                # Validate URL format
                if not path.startswith(('http://', 'https://', 'ftp://', 'file://')):
                    raise ValueError(f"Invalid URL format: {path}")
                media_content = QMediaContent(QUrl(path))

            # Validate media content
            if media_content.isNull():
                raise ValueError("Failed to create valid media content")

            # Set media and check if it was successful
            self.player.setMedia(media_content)
            logger.info(f"Created media content for URL: {media_content.canonicalUrl()}")
            logger.info("Media content set successfully")

            # Check media status after setting
            QTimer.singleShot(100, self._check_media_status)

            # Auto-play after loading
            QTimer.singleShot(1000, self._try_auto_play)

            # Check player state safely
            try:
                logger.info(f"Player state: {self.player.state()}")
                logger.info(f"Player media status: {self.player.mediaStatus()}")
            except:
                logger.error("Could not get player state")

        except Exception as e:
            logger.error(f"Error in _continue_loading: {e}")
            self.error_occurred.emit(f"Loading error: {str(e)}")

    def _try_alternative_playback(self, file_path_or_url):
        """Try alternative playback methods when Qt multimedia fails"""
        try:
            logger.info("🔄 Attempting alternative playback...")
            if os.path.isfile(file_path_or_url):
                # System player
                if platform.system() == "Windows":
                    os.startfile(file_path_or_url)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", file_path_or_url])
                else:  # Linux
                    subprocess.run(["xdg-open", file_path_or_url])
                self.error_occurred.emit(f"Video opened in system player.\n\nFile: {os.path.basename(file_path_or_url)}\n\nNote: Qt multimedia components may need to be installed for in-app playback.")
                if self.status_label:
                    self.status_label.setText("▶️ Opened in system player")
            else:
                # Browser for URLs
                webbrowser.open(file_path_or_url)
                if self.status_label:
                    self.status_label.setText("▶️ Opened in browser")

        except Exception as e:
            logger.error(f"Alternative playback failed: {e}")
            self.error_occurred.emit(f"Unable to play video file.\n\nPlease install video codecs or try a different file format.")

    def _recreate_player(self):
        """Recreate the media player if it's corrupted"""
        try:
            logger.info("Recreating media player...")

            # Disconnect old signals if player exists
            if self.player:
                try:
                    self.player.error.disconnect()
                    self.player.durationChanged.disconnect()
                    self.player.positionChanged.disconnect()
                    self.player.stateChanged.disconnect()
                except:
                    pass

            # Create new player
            self.player = QMediaPlayer(self)
            self.player.setVideoOutput(self.video_widget)
            self.player.setVolume(80)

            # Reconnect signals
            self.player.error.connect(self.handle_player_error)
            self.player.durationChanged.connect(self._on_dur)
            self.player.positionChanged.connect(self._on_pos)
            self.player.stateChanged.connect(self._on_state_changed)

            logger.info("Media player recreated successfully")

        except Exception as e:
            logger.error(f"Error recreating player: {e}")

    def _validate_video_file(self, file_path):
        """Validate video file before attempting playback"""
        try:
            # Check file exists and is readable
            if not os.path.exists(file_path):
                return {'valid': False, 'error': f"File not found: {file_path}"}

            if not os.access(file_path, os.R_OK):
                return {'valid': False, 'error': f"Cannot read file: {file_path}"}

            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {'valid': False, 'error': "File is empty"}

            if file_size < 1024:  # Less than 1KB is suspicious
                return {'valid': False, 'error': "File appears to be corrupted (too small)"}

            # Check file extension
            _, ext = os.path.splitext(file_path.lower())
            supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']

            if ext not in supported_formats:
                return {
                    'valid': False,
                    'error': f"Unsupported format: {ext}\n\nSupported formats: {', '.join(supported_formats)}\n\n🔧 Try converting to MP4 format"
                }

            # Try to probe file with FFmpeg if available
            probe_result = self._probe_video_with_ffmpeg(file_path)
            if probe_result and not probe_result['valid']:
                return probe_result

            return {'valid': True, 'error': None}

        except Exception as e:
            return {'valid': False, 'error': f"Validation error: {str(e)}"}

    def _probe_video_with_ffmpeg(self, file_path):
        """Probe video file with FFmpeg to check if it's valid"""
        try:
            # Use ffprobe to check video file
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                file_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

            if result.returncode != 0:
                return {
                    'valid': False,
                    'error': f"Video file appears to be corrupted or unsupported.\n\n🔧 Possible solutions:\n• Try re-downloading the file\n• Convert to MP4 format\n• Check if file is complete"
                }

            # Parse JSON output to check for video streams
            try:
                data = json.loads(result.stdout)
                has_video = any(stream.get('codec_type') == 'video' for stream in data.get('streams', []))

                if not has_video:
                    return {
                        'valid': False,
                        'error': "File contains no video streams.\n\n🔧 This might be an audio-only file or corrupted video."
                    }

            except json.JSONDecodeError:
                pass  # Continue with basic validation

            return {'valid': True, 'error': None}

        except subprocess.TimeoutExpired:
            return {'valid': False, 'error': "File validation timed out - file might be corrupted"}
        except FileNotFoundError:
            # FFmpeg not available, skip probe
            return None
        except Exception as e:
            logger.error(f"FFmpeg probe error: {e}")
            return None

    def _try_auto_play(self):
        """Try to auto-play the loaded media"""
        try:
            if self.player and self.player.mediaStatus() == QMediaPlayer.LoadedMedia:
                logger.info("Auto-playing loaded media...")
                self.play()
            else:
                logger.info(f"Media not ready for auto-play. Status: {self.player.mediaStatus()}")
        except Exception as e:
            logger.error(f"Error in auto-play: {e}")

    def _check_media_status(self):
        """Check media status after loading"""
        try:
            if self.player:
                status = self.player.mediaStatus()
                logger.info(f"Media status: {status}")

                if status == QMediaPlayer.InvalidMedia:
                    self.error_occurred.emit(
                        "Invalid media format detected.\n\n"
                        "🔧 Possible solutions:\n"
                        "• Install Windows Media Feature Pack\n"
                        "• Install K-Lite Codec Pack\n"
                        "• Convert video to MP4 format\n"
                        "• Try a different video file"
                    )
                elif status == QMediaPlayer.UnknownMediaStatus:
                    logger.info("Unknown media status - waiting for load...")
                    # Check again in a moment
                    QTimer.singleShot(500, self._check_media_status)

        except Exception as e:
            logger.error(f"Error checking media status: {e}")

    def load_video(self, url_or_path):
        """Public method to load video (alias for load)"""
        self.load(url_or_path)

    def _check_playback_status(self):
        """Check if playback started; fallback if not"""
        if self.player.state() == QMediaPlayer.StoppedState and self.player.mediaStatus() in [QMediaPlayer.InvalidMedia, QMediaPlayer.NoMedia]:
            logger.warning("Playback failed - triggering fallback")
            self._try_alternative_playback(self.current_video_path)

    def _extract_youtube_stream(self, url):
        """Extract direct stream URL from YouTube video"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'format': 'best[ext=mp4]/best[height<=720]',  # Force MP4 for QMediaPlayer compatibility
                'extractaudio': False,
                'noplaylist': True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)

                if info and info.get('url'):
                    return info['url']

                # Try to get from formats
                if info and info.get('formats'):
                    for fmt in info['formats']:
                        if fmt.get('url') and fmt.get('vcodec') != 'none' and 'mp4' in fmt.get('ext', ''):
                            return fmt['url']

            return None

        except Exception as e:
            logger.error(f"Error extracting YouTube stream: {e}")
            return None
