"""
Video Editor Interface
Advanced video editing with timeline selection and effects
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class VideoEditorWindow(QMainWindow):
    """Advanced video editor window"""
    
    def __init__(self, video_path=None, start_time=0, end_time=0, parent=None):
        super().__init__(parent)
        self.video_path = video_path
        self.start_time = start_time
        self.end_time = end_time
        
        self.setWindowTitle("Video Editor - Edit Selected Segment")
        self.setGeometry(100, 100, 1400, 800)
        
        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
            QSplitter::handle {
                background-color: #444;
            }
        """)
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path, start_time, end_time)
            
    def setup_ui(self):
        """Setup editor interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create menu and toolbar
        self.create_menu_bar()
        self.create_toolbar()
        
        # Main splitter
        main_splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Tools and effects
        left_panel = self.create_left_panel()
        left_panel.setFixedWidth(250)
        
        # Center area - Preview and timeline
        center_area = self.create_center_area()
        
        # Right panel - Properties
        right_panel = self.create_right_panel()
        right_panel.setFixedWidth(200)
        
        # Add to splitter
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(center_area)
        main_splitter.addWidget(right_panel)
        main_splitter.setSizes([250, 900, 200])
        
        main_layout.addWidget(main_splitter)
        
        # Status bar
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #333;
                color: white;
                border-top: 1px solid #555;
            }
        """)
        self.status_bar.showMessage("Ready to edit video segment")
        
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #333;
                color: white;
                border-bottom: 1px solid #555;
            }
            QMenuBar::item {
                padding: 6px 12px;
            }
            QMenuBar::item:selected {
                background-color: #0078d4;
            }
            QMenu {
                background-color: #333;
                color: white;
                border: 1px solid #555;
            }
            QMenu::item {
                padding: 6px 20px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """)
        
        # File menu
        file_menu = menubar.addMenu('File')
        file_menu.addAction('Save Edit', self.save_edit)
        file_menu.addAction('Export Segment', self.export_segment)
        file_menu.addSeparator()
        file_menu.addAction('Close Editor', self.close)
        
        # Edit menu
        edit_menu = menubar.addMenu('Edit')
        edit_menu.addAction('Undo', self.undo)
        edit_menu.addAction('Redo', self.redo)
        edit_menu.addSeparator()
        edit_menu.addAction('Cut', self.cut_segment)
        edit_menu.addAction('Copy', self.copy_segment)
        edit_menu.addAction('Delete', self.delete_segment)
        
        # Effects menu
        effects_menu = menubar.addMenu('Effects')
        effects_menu.addAction('Add Transition', self.add_transition)
        effects_menu.addAction('Color Correction', self.color_correction)
        effects_menu.addAction('Audio Effects', self.audio_effects)
        
    def create_toolbar(self):
        """Create toolbar"""
        toolbar = self.addToolBar('Main')
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #404040;
                border-bottom: 1px solid #555;
                spacing: 4px;
            }
            QToolButton {
                background-color: #505050;
                border: 1px solid #666;
                color: white;
                padding: 6px;
                margin: 2px;
            }
            QToolButton:hover {
                background-color: #606060;
            }
            QToolButton:pressed {
                background-color: #707070;
            }
        """)
        
        # Playback controls
        toolbar.addAction('⏮️', self.go_to_start)
        toolbar.addAction('⏯️', self.play_pause)
        toolbar.addAction('⏭️', self.go_to_end)
        toolbar.addSeparator()
        
        # Edit tools
        toolbar.addAction('✂️', self.cut_tool)
        toolbar.addAction('🔧', self.selection_tool)
        toolbar.addAction('🎨', self.effects_tool)
        toolbar.addSeparator()
        
        # Timeline controls
        toolbar.addAction('🔍-', self.zoom_out)
        toolbar.addAction('🔍+', self.zoom_in)
        
    def create_left_panel(self):
        """Create left panel with tools and effects"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                border-right: 1px solid #555;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # Tools section
        tools_group = QGroupBox("🔧 EDITING TOOLS")
        tools_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        tools_layout = QVBoxLayout(tools_group)
        
        # Tool buttons
        tools = [
            ("✂️ Cut", self.cut_tool),
            ("🔧 Select", self.selection_tool),
            ("🎨 Effects", self.effects_tool),
            ("📝 Text", self.text_tool),
            ("🎵 Audio", self.audio_tool)
        ]
        
        for tool_name, tool_func in tools:
            btn = QPushButton(tool_name)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    color: white;
                    border: 1px solid #666;
                    padding: 8px;
                    text-align: left;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #0078d4;
                }
            """)
            btn.clicked.connect(tool_func)
            tools_layout.addWidget(btn)
            
        layout.addWidget(tools_group)
        
        # Effects section
        effects_group = QGroupBox("🎨 QUICK EFFECTS")
        effects_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        effects_layout = QVBoxLayout(effects_group)
        
        # Effect buttons
        effects = [
            ("🌈 Color Correct", self.color_correction),
            ("🌫️ Blur", self.add_blur),
            ("✨ Sharpen", self.add_sharpen),
            ("🔊 Volume", self.adjust_volume),
            ("🎵 Fade", self.add_fade)
        ]
        
        for effect_name, effect_func in effects:
            btn = QPushButton(effect_name)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #404040;
                    color: white;
                    border: 1px solid #666;
                    padding: 8px;
                    text-align: left;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #0078d4;
                }
            """)
            btn.clicked.connect(effect_func)
            effects_layout.addWidget(btn)
            
        layout.addWidget(effects_group)
        layout.addStretch()
        
        return panel
        
    def create_center_area(self):
        """Create center area with preview and timeline"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Video preview
        preview_group = QGroupBox("🎬 VIDEO PREVIEW")
        preview_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        preview_layout = QVBoxLayout(preview_group)
        
        # Video widget placeholder
        self.video_preview = QLabel("🎬 Video Preview\n\nLoad a video to start editing")
        self.video_preview.setAlignment(Qt.AlignCenter)
        self.video_preview.setMinimumHeight(300)
        self.video_preview.setStyleSheet("""
            QLabel {
                background-color: #222;
                border: 1px solid #444;
                border-radius: 5px;
                color: #888;
                font-size: 16px;
            }
        """)
        preview_layout.addWidget(self.video_preview)
        
        # Playback controls
        controls_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("▶️ Play")
        self.play_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        self.play_btn.clicked.connect(self.play_pause)
        controls_layout.addWidget(self.play_btn)
        
        # Time display
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setStyleSheet("color: white; font-weight: bold; padding: 10px;")
        controls_layout.addWidget(self.time_label)
        
        controls_layout.addStretch()
        
        # Volume control
        volume_label = QLabel("🔊")
        volume_label.setStyleSheet("color: white; padding: 5px;")
        controls_layout.addWidget(volume_label)
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)
        self.volume_slider.setFixedWidth(100)
        self.volume_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #555;
                height: 8px;
                background: #333;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #0078d4;
                border: 1px solid #0078d4;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
        """)
        controls_layout.addWidget(self.volume_slider)
        
        preview_layout.addLayout(controls_layout)
        layout.addWidget(preview_group, 2)
        
        # Timeline section
        timeline_group = QGroupBox("⏱️ TIMELINE EDITOR")
        timeline_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        timeline_layout = QVBoxLayout(timeline_group)
        
        # Timeline controls
        timeline_controls = QHBoxLayout()
        
        # Segment info
        self.segment_info = QLabel("Segment: Not selected")
        self.segment_info.setStyleSheet("color: white; font-weight: bold; padding: 5px;")
        timeline_controls.addWidget(self.segment_info)
        
        timeline_controls.addStretch()
        
        # Zoom controls
        zoom_out_btn = QPushButton("🔍-")
        zoom_out_btn.clicked.connect(self.zoom_out)
        timeline_controls.addWidget(zoom_out_btn)
        
        zoom_in_btn = QPushButton("🔍+")
        zoom_in_btn.clicked.connect(self.zoom_in)
        timeline_controls.addWidget(zoom_in_btn)
        
        timeline_layout.addLayout(timeline_controls)
        
        # Timeline widget placeholder
        self.timeline_widget = QLabel("📊 Timeline\n\nSelect start and end points for editing")
        self.timeline_widget.setAlignment(Qt.AlignCenter)
        self.timeline_widget.setMinimumHeight(150)
        self.timeline_widget.setStyleSheet("""
            QLabel {
                background-color: #333;
                border: 1px solid #555;
                border-radius: 5px;
                color: #888;
                font-size: 14px;
            }
        """)
        timeline_layout.addWidget(self.timeline_widget)
        
        layout.addWidget(timeline_group, 1)
        
        return widget
        
    def create_right_panel(self):
        """Create right panel with properties"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                border-left: 1px solid #555;
            }
        """)
        
        layout = QVBoxLayout(panel)
        
        # Properties section
        props_group = QGroupBox("📊 PROPERTIES")
        props_group.setStyleSheet("""
            QGroupBox {
                color: white;
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        props_layout = QVBoxLayout(props_group)
        
        # Property controls
        properties = [
            ("Start Time", "00:00"),
            ("End Time", "00:00"),
            ("Duration", "00:00"),
            ("Brightness", "0"),
            ("Contrast", "0"),
            ("Volume", "100%")
        ]
        
        for prop_name, prop_value in properties:
            prop_layout = QVBoxLayout()
            
            label = QLabel(prop_name)
            label.setStyleSheet("color: white; font-weight: bold; margin-bottom: 2px;")
            prop_layout.addWidget(label)
            
            value_edit = QLineEdit(prop_value)
            value_edit.setStyleSheet("""
                QLineEdit {
                    background-color: #333;
                    color: white;
                    border: 1px solid #555;
                    padding: 6px;
                    border-radius: 3px;
                }
            """)
            prop_layout.addWidget(value_edit)
            
            props_layout.addLayout(prop_layout)
            
        layout.addWidget(props_group)
        layout.addStretch()
        
        return panel
        
    def load_video(self, video_path, start_time, end_time):
        """Load video for editing"""
        self.video_path = video_path
        self.start_time = start_time
        self.end_time = end_time
        
        file_name = os.path.basename(video_path)
        self.video_preview.setText(f"🎬 {file_name}\n\nReady for editing")
        
        duration = end_time - start_time if end_time > start_time else 0
        self.segment_info.setText(f"Segment: {start_time:.1f}s - {end_time:.1f}s ({duration:.1f}s)")
        
        self.status_bar.showMessage(f"Loaded: {file_name} | Segment: {duration:.1f}s")
        
    # Tool methods
    def cut_tool(self):
        self.status_bar.showMessage("Cut tool selected - Click on timeline to cut")
        
    def selection_tool(self):
        self.status_bar.showMessage("Selection tool selected")
        
    def effects_tool(self):
        self.status_bar.showMessage("Effects tool selected")
        
    def text_tool(self):
        self.status_bar.showMessage("Text tool selected - Add text overlays")
        
    def audio_tool(self):
        self.status_bar.showMessage("Audio tool selected - Edit audio properties")
        
    # Effect methods
    def color_correction(self):
        QMessageBox.information(self, "Color Correction", "Color correction dialog coming soon!")
        
    def add_blur(self):
        QMessageBox.information(self, "Blur Effect", "Blur effect applied!")
        
    def add_sharpen(self):
        QMessageBox.information(self, "Sharpen Effect", "Sharpen effect applied!")
        
    def adjust_volume(self):
        QMessageBox.information(self, "Volume", "Volume adjustment dialog coming soon!")
        
    def add_fade(self):
        QMessageBox.information(self, "Fade Effect", "Fade effect applied!")
        
    # Playback methods
    def play_pause(self):
        if self.play_btn.text() == "▶️ Play":
            self.play_btn.setText("⏸️ Pause")
            self.status_bar.showMessage("Playing...")
        else:
            self.play_btn.setText("▶️ Play")
            self.status_bar.showMessage("Paused")
            
    def go_to_start(self):
        self.status_bar.showMessage("Jumped to start")
        
    def go_to_end(self):
        self.status_bar.showMessage("Jumped to end")
        
    def zoom_in(self):
        self.status_bar.showMessage("Timeline zoomed in")
        
    def zoom_out(self):
        self.status_bar.showMessage("Timeline zoomed out")
        
    # Menu methods
    def save_edit(self):
        QMessageBox.information(self, "Save Edit", "Edit saved to project!")
        
    def export_segment(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Segment", "", "Video Files (*.mp4);;All Files (*)")
        if file_path:
            QMessageBox.information(self, "Export", f"Exporting segment to: {os.path.basename(file_path)}")
            
    def undo(self):
        self.status_bar.showMessage("Undo")
        
    def redo(self):
        self.status_bar.showMessage("Redo")
        
    def cut_segment(self):
        self.status_bar.showMessage("Segment cut")
        
    def copy_segment(self):
        self.status_bar.showMessage("Segment copied")
        
    def delete_segment(self):
        self.status_bar.showMessage("Segment deleted")
        
    def add_transition(self):
        QMessageBox.information(self, "Transition", "Transition added!")
        
    def audio_effects(self):
        QMessageBox.information(self, "Audio Effects", "Audio effects dialog coming soon!")
