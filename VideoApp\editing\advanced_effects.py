# -*- coding: utf-8 -*-
"""
Advanced video effects for editing
"""

from core.libraries import *
import os
import moviepy.editor as mp
from typing import Optional, Dict, Any
import numpy as np


def apply_advanced_effect(
    video_path: str,
    effect_type: str,
    parameters: Dict[str, Any],
    output_path: Optional[str] = None
) -> str:
    """
    Apply advanced effects to a video.
    
    Args:
        video_path: Path to the input video
        effect_type: Type of effect to apply
        parameters: Effect parameters
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Apply effect based on type
        if effect_type == "blur":
            effect_video = apply_blur_effect(video, parameters)
        elif effect_type == "sharpen":
            effect_video = apply_sharpen_effect(video, parameters)
        elif effect_type == "noise":
            effect_video = apply_noise_effect(video, parameters)
        elif effect_type == "mirror":
            effect_video = apply_mirror_effect(video, parameters)
        elif effect_type == "rotate":
            effect_video = apply_rotate_effect(video, parameters)
        elif effect_type == "scale":
            effect_video = apply_scale_effect(video, parameters)
        elif effect_type == "crop":
            effect_video = apply_crop_effect(video, parameters)
        elif effect_type == "speed":
            effect_video = apply_speed_effect(video, parameters)
        elif effect_type == "reverse":
            effect_video = apply_reverse_effect(video, parameters)
        elif effect_type == "loop":
            effect_video = apply_loop_effect(video, parameters)
        else:
            # No effect applied
            effect_video = video
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_{effect_type}_effect.mp4"
        
        # Write output
        effect_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        if effect_video != video:
            effect_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to apply advanced effect: {str(e)}")


def apply_blur_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply blur effect to video"""
    blur_radius = parameters.get('radius', 2)
    
    # Apply blur using MoviePy's blur effect
    try:
        blurred_video = video.fx(mp.vfx.blur, blur_radius)
        return blurred_video
    except:
        # Fallback if blur effect is not available
        return video


def apply_sharpen_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply sharpen effect to video"""
    # MoviePy doesn't have a built-in sharpen effect, so return original
    return video


def apply_noise_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply noise effect to video"""
    noise_level = parameters.get('level', 0.1)
    
    def add_noise(get_frame, t):
        frame = get_frame(t)
        noise = np.random.normal(0, noise_level * 255, frame.shape).astype(np.uint8)
        noisy_frame = np.clip(frame.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        return noisy_frame
    
    try:
        noisy_video = video.fl(add_noise)
        return noisy_video
    except:
        # Fallback if numpy operations fail
        return video


def apply_mirror_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply mirror effect to video"""
    direction = parameters.get('direction', 'horizontal')
    
    if direction == 'horizontal':
        mirrored_video = video.fx(mp.vfx.mirror_x)
    elif direction == 'vertical':
        mirrored_video = video.fx(mp.vfx.mirror_y)
    else:
        mirrored_video = video
    
    return mirrored_video


def apply_rotate_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply rotation effect to video"""
    angle = parameters.get('angle', 90)
    
    rotated_video = video.rotate(angle)
    return rotated_video


def apply_scale_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply scaling effect to video"""
    scale_factor = parameters.get('factor', 1.0)
    
    scaled_video = video.resize(scale_factor)
    return scaled_video


def apply_crop_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply crop effect to video"""
    x1 = parameters.get('x1', 0)
    y1 = parameters.get('y1', 0)
    x2 = parameters.get('x2', video.w)
    y2 = parameters.get('y2', video.h)
    
    cropped_video = video.crop(x1=x1, y1=y1, x2=x2, y2=y2)
    return cropped_video


def apply_speed_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply speed effect to video"""
    speed_factor = parameters.get('factor', 1.0)
    
    speed_video = video.fx(mp.vfx.speedx, speed_factor)
    return speed_video


def apply_reverse_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply reverse effect to video"""
    reversed_video = video.fx(mp.vfx.time_mirror)
    return reversed_video


def apply_loop_effect(video: mp.VideoFileClip, parameters: Dict[str, Any]) -> mp.VideoFileClip:
    """Apply loop effect to video"""
    loop_count = parameters.get('count', 2)
    
    looped_video = video.loop(n=loop_count)
    return looped_video


def create_picture_in_picture_effect(
    main_video_path: str,
    overlay_video_path: str,
    position: str = "top-right",
    size_ratio: float = 0.25,
    output_path: Optional[str] = None
) -> str:
    """
    Create picture-in-picture effect.
    
    Args:
        main_video_path: Path to the main video
        overlay_video_path: Path to the overlay video
        position: Position of the overlay ("top-right", "top-left", etc.)
        size_ratio: Size of overlay relative to main video (0.0 to 1.0)
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load videos
        main_video = mp.VideoFileClip(main_video_path)
        overlay_video = mp.VideoFileClip(overlay_video_path)
        
        # Resize overlay video
        overlay_width = int(main_video.w * size_ratio)
        overlay_height = int(overlay_width * overlay_video.h / overlay_video.w)
        overlay_resized = overlay_video.resize((overlay_width, overlay_height))
        
        # Set position
        position_map = {
            'top-left': (10, 10),
            'top-right': (main_video.w - overlay_width - 10, 10),
            'bottom-left': (10, main_video.h - overlay_height - 10),
            'bottom-right': (main_video.w - overlay_width - 10, main_video.h - overlay_height - 10),
            'center': ('center', 'center')
        }
        
        pos = position_map.get(position, position_map['top-right'])
        overlay_positioned = overlay_resized.set_position(pos)
        
        # Composite videos
        final_video = mp.CompositeVideoClip([main_video, overlay_positioned])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(main_video_path)[0]
            output_path = f"{base_name}_pip.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        main_video.close()
        overlay_video.close()
        overlay_resized.close()
        overlay_positioned.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create picture-in-picture effect: {str(e)}")


def apply_chroma_key_effect(
    video_path: str,
    background_path: str,
    key_color: str = "#00FF00",
    tolerance: float = 0.1,
    output_path: Optional[str] = None
) -> str:
    """
    Apply chroma key (green screen) effect.
    
    Args:
        video_path: Path to the video with chroma key background
        background_path: Path to the background video/image
        key_color: Color to key out (hex format)
        tolerance: Color tolerance for keying
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video and background
        video = mp.VideoFileClip(video_path)
        
        if background_path.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
            background = mp.ImageClip(background_path, duration=video.duration)
        else:
            background = mp.VideoFileClip(background_path)
        
        # Resize background to match video
        background = background.resize(video.size)
        
        # Simple chroma key implementation (basic)
        # Note: This is a simplified version. Real chroma keying is more complex
        def chroma_key_mask(get_frame, t):
            frame = get_frame(t)
            # This is a placeholder - real chroma keying would analyze color values
            return frame
        
        # For now, just composite the videos with some transparency
        video_with_alpha = video.set_opacity(0.8)
        final_video = mp.CompositeVideoClip([background, video_with_alpha])
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_chroma_key.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        background.close()
        video_with_alpha.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to apply chroma key effect: {str(e)}")


def create_split_screen_effect(
    video1_path: str,
    video2_path: str,
    split_type: str = "vertical",
    output_path: Optional[str] = None
) -> str:
    """
    Create split screen effect with two videos.
    
    Args:
        video1_path: Path to the first video
        video2_path: Path to the second video
        split_type: Type of split ("vertical", "horizontal")
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load videos
        video1 = mp.VideoFileClip(video1_path)
        video2 = mp.VideoFileClip(video2_path)
        
        # Get dimensions
        w, h = video1.size
        
        if split_type == "vertical":
            # Split vertically (side by side)
            video1_cropped = video1.crop(x1=0, y1=0, x2=w//2, y2=h)
            video2_cropped = video2.crop(x1=w//2, y1=0, x2=w, y2=h).resize((w//2, h))
            video2_positioned = video2_cropped.set_position((w//2, 0))
        else:
            # Split horizontally (top and bottom)
            video1_cropped = video1.crop(x1=0, y1=0, x2=w, y2=h//2)
            video2_cropped = video2.crop(x1=0, y1=h//2, x2=w, y2=h).resize((w, h//2))
            video2_positioned = video2_cropped.set_position((0, h//2))
        
        # Composite videos
        final_video = mp.CompositeVideoClip([video1_cropped, video2_positioned])
        
        # Generate output path if not provided
        if output_path is None:
            base_name1 = os.path.splitext(os.path.basename(video1_path))[0]
            base_name2 = os.path.splitext(os.path.basename(video2_path))[0]
            output_path = f"{base_name1}_{base_name2}_split_screen.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video1.close()
        video2.close()
        video1_cropped.close()
        video2_cropped.close()
        video2_positioned.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create split screen effect: {str(e)}")


# Convenience functions for common effects
def quick_blur(video_path: str, radius: int = 2) -> str:
    """Quick blur effect with default settings"""
    return apply_advanced_effect(video_path, "blur", {"radius": radius})


def quick_mirror(video_path: str, direction: str = "horizontal") -> str:
    """Quick mirror effect with default settings"""
    return apply_advanced_effect(video_path, "mirror", {"direction": direction})


def quick_rotate(video_path: str, angle: float = 90) -> str:
    """Quick rotation effect with default settings"""
    return apply_advanced_effect(video_path, "rotate", {"angle": angle})


def quick_speed_up(video_path: str, factor: float = 2.0) -> str:
    """Quick speed up effect"""
    return apply_advanced_effect(video_path, "speed", {"factor": factor})


def quick_slow_down(video_path: str, factor: float = 0.5) -> str:
    """Quick slow down effect"""
    return apply_advanced_effect(video_path, "speed", {"factor": factor})
