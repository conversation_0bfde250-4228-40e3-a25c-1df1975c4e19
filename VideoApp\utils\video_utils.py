# -*- coding: utf-8 -*-
"""Utility functions for video processing."""

def to_seconds(timestamp: str) -> float:
    """
    Convert HH:MM:SS or MM:SS timestamp to seconds.
    
    Args:
        timestamp: Time string in format HH:MM:SS or MM:SS
        
    Returns:
        Time in seconds
    """
    parts = timestamp.split(':')
    if len(parts) == 3:  # HH:MM:SS
        hours, minutes, seconds = parts
        return int(hours) * 3600 + int(minutes) * 60 + float(seconds)
    elif len(parts) == 2:  # MM:SS
        minutes, seconds = parts
        return int(minutes) * 60 + float(seconds)
    elif len(parts) == 1:  # SS
        return float(parts[0])
    else:
        raise ValueError(f"Invalid timestamp format: {timestamp}")
