"""
Adobe Premiere Pro-style Main Interface
Professional video editing workspace with panels and timeline
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from .premiere_timeline import PremiereTimeline
from .unified_video_player import UnifiedVideoPlayer
from .search_interface import VideoSearchWidget
from .file_browser import FileBrowserWidget
from .text_overlay_editor import TextOverlayEditor
try:
    from ..dashboard.download_page import DownloadPage
    from ..dashboard.edit_page import EditPage
    from ..utils.video_downloader import VideoDownloader
    from ..social.upload import upload_video, UploadConfig
    from ..utils.audio_utils import AudioManager
except ImportError:
    # Fallback imports for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from dashboard.download_page import DownloadPage
    from dashboard.edit_page import EditPage
    from utils.video_downloader import VideoDownloader
    from social.upload import upload_video, UploadConfig
    try:
        from utils.audio_utils import AudioManager
    except ImportError:
        # Create a dummy AudioManager if not available
        class AudioManager:
            def __init__(self):
                pass

class ProjectTreeWidget(QTreeWidget):
    """Custom tree widget with proper drag and drop support"""

    def __init__(self, parent=None):
        super().__init__(parent)

    def startDrag(self, supportedActions):
        """Start drag operation with file path data"""
        item = self.currentItem()
        if item:
            file_path = item.data(0, Qt.UserRole)
            if file_path and os.path.exists(file_path):
                # Create drag object
                drag = QDrag(self)
                mimeData = QMimeData()

                # Set file URL for drag and drop
                url = QUrl.fromLocalFile(file_path)
                mimeData.setUrls([url])

                # Also set text data as fallback
                mimeData.setText(file_path)

                # Set custom data for internal use
                mimeData.setData("application/x-project-file", file_path.encode())

                drag.setMimeData(mimeData)

                # Set drag pixmap (optional)
                pixmap = QPixmap(100, 30)
                pixmap.fill(QColor(0, 120, 212, 180))
                painter = QPainter(pixmap)
                painter.setPen(QColor(255, 255, 255))
                painter.drawText(pixmap.rect(), Qt.AlignCenter, item.text(0)[:15] + "...")
                painter.end()
                drag.setPixmap(pixmap)

                # Execute drag
                result = drag.exec_(Qt.CopyAction | Qt.MoveAction, Qt.CopyAction)
                return

        # Fallback to default behavior
        super().startDrag(supportedActions)

class ProjectPanel(QWidget):
    """Project panel for managing media files"""

    # Signals
    file_selected = pyqtSignal(str)  # Emits file path when file is selected

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_project_path = None
        self.media_files = []
        self.timeline_ref = None  # Reference to timeline widget
        self.setup_ui()
        self.setup_drag_drop()
        
    def setup_ui(self):
        """Setup project panel UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Header with project controls
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(2)

        # Title
        header = QLabel("PROJECT")
        header.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-bottom: 1px solid #555;
            }
        """)
        header_layout.addWidget(header)

        # Project controls
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(4, 4, 4, 4)
        controls_layout.setSpacing(4)

        # New project button
        self.new_project_btn = QPushButton("📁 New")
        self.new_project_btn.setToolTip("Create new project")
        self.new_project_btn.clicked.connect(self.create_new_project)

        # Import media button
        self.import_btn = QPushButton("📥 Import")
        self.import_btn.setToolTip("Import media files")
        self.import_btn.clicked.connect(self.import_media)

        # Add to timeline button
        self.add_to_timeline_btn = QPushButton("➕ Add to Timeline")
        self.add_to_timeline_btn.setToolTip("Add selected media to timeline")
        self.add_to_timeline_btn.clicked.connect(self.add_selected_to_timeline)

        # Style buttons
        button_style = """
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """
        self.new_project_btn.setStyleSheet(button_style)
        self.import_btn.setStyleSheet(button_style)
        self.add_to_timeline_btn.setStyleSheet(button_style)

        controls_layout.addWidget(self.new_project_btn)
        controls_layout.addWidget(self.import_btn)
        controls_layout.addWidget(self.add_to_timeline_btn)
        controls_layout.addStretch()

        header_layout.addWidget(controls_widget)
        layout.addWidget(header_widget)

        # Search bar
        search_bar = QLineEdit()
        search_bar.setPlaceholderText("Search project...")
        search_bar.setStyleSheet("""
            QLineEdit {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 6px;
                margin: 4px;
            }
        """)
        search_bar.textChanged.connect(self.filter_items)
        layout.addWidget(search_bar)
        
        # File tree with custom drag and drop
        self.file_tree = ProjectTreeWidget()
        self.file_tree.setHeaderLabels(["Name", "Type", "Duration"])
        self.file_tree.setDragDropMode(QTreeWidget.DragOnly)
        self.file_tree.setDefaultDropAction(Qt.CopyAction)
        self.file_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #2b2b2b;
                color: white;
                border: none;
                alternate-background-color: #333;
            }
            QTreeWidget::item {
                padding: 4px;
            }
            QTreeWidget::item:selected {
                background-color: #0078d4;
            }
        """)

        # Connect tree signals
        self.file_tree.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.file_tree.itemSelectionChanged.connect(self.on_selection_changed)
        self.file_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.file_tree.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.file_tree)

        # Auto-add to timeline option
        auto_add_widget = QWidget()
        auto_add_layout = QHBoxLayout(auto_add_widget)
        auto_add_layout.setContentsMargins(8, 4, 8, 4)

        self.auto_add_checkbox = QCheckBox("Auto-add to timeline")
        self.auto_add_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ccc;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #333;
                border: 1px solid #555;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border: 1px solid #0078d4;
            }
        """)
        self.auto_add_checkbox.setToolTip("Automatically add imported files to timeline")
        self.auto_add_checkbox.toggled.connect(self.on_auto_add_toggled)

        auto_add_layout.addWidget(self.auto_add_checkbox)
        auto_add_layout.addStretch()
        layout.addWidget(auto_add_widget)

        # Status label
        self.status_label = QLabel("Drop media files here or click Import")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #888;
                padding: 8px;
                font-style: italic;
                text-align: center;
            }
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)

        # Initialize with empty project
        self.populate_sample_items()

    def setup_drag_drop(self):
        """Setup drag and drop functionality"""
        self.setAcceptDrops(True)

    def dragEnterEvent(self, event):
        """Handle drag enter event"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move event"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        """Handle drop event"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            for url in urls:
                file_path = url.toLocalFile()
                if file_path:
                    self.add_media_file(file_path)
            event.acceptProposedAction()
        else:
            event.ignore()

    def create_new_project(self):
        """Create a new project"""
        project_name, ok = QInputDialog.getText(self, "New Project", "Project Name:")
        if ok and project_name:
            # Clear current project
            self.file_tree.clear()
            self.media_files.clear()
            self.current_project_path = None

            # Create project structure
            self.populate_empty_project()
            self.status_label.setText(f"Project: {project_name}")
            print(f"Created new project: {project_name}")

    def import_media(self):
        """Import media files"""
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "Import Media Files",
            "",
            "Media Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.mp3 *.wav *.aac *.flac *.ogg *.m4a *.wma *.jpg *.jpeg *.png *.gif *.bmp *.tiff *.webp);;All Files (*)"
        )

        if file_paths:
            # Show progress for multiple files
            total_files = len(file_paths)
            auto_add_enabled = hasattr(self, 'auto_add_checkbox') and self.auto_add_checkbox.isChecked()

            for i, file_path in enumerate(file_paths, 1):
                self.status_label.setText(f"Importing {i}/{total_files}: {os.path.basename(file_path)}")
                QApplication.processEvents()  # Update UI
                self.add_media_file(file_path)

            # Final status message
            if auto_add_enabled:
                self.status_label.setText(f"Imported {total_files} files and added to timeline")
            else:
                self.status_label.setText(f"Imported {total_files} files to project")

            print(f"✅ Successfully imported {total_files} media files")

    def add_media_file(self, file_path):
        """Add a media file to the project"""
        if not os.path.exists(file_path):
            return

        # Avoid duplicates
        if file_path in self.media_files:
            return

        self.media_files.append(file_path)

        # Determine file type and add to appropriate folder
        file_name = os.path.basename(file_path)
        file_ext = file_path.lower().split('.')[-1] if '.' in file_path else ''

        # Get file info
        file_size = os.path.getsize(file_path)
        file_size_str = self.format_file_size(file_size)

        # Determine category
        if file_ext in ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v', '3gp']:
            category = 'Videos'
            file_type = 'Video'
            duration = self.get_video_duration(file_path)
        elif file_ext in ['mp3', 'wav', 'aac', 'flac', 'ogg', 'm4a', 'wma']:
            category = 'Audio'
            file_type = 'Audio'
            duration = self.get_audio_duration(file_path)
        elif file_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp']:
            category = 'Images'
            file_type = 'Image'
            duration = file_size_str
        else:
            category = 'Other'
            file_type = 'File'
            duration = file_size_str

        # Find or create category folder
        category_item = self.find_or_create_category(category)

        # Create file item
        file_item = QTreeWidgetItem([file_name, file_type, duration])
        file_item.setData(0, Qt.UserRole, file_path)  # Store file path
        file_item.setIcon(0, self.get_file_icon(file_type))

        category_item.addChild(file_item)
        category_item.setExpanded(True)

        self.status_label.setText(f"Added: {file_name}")
        print(f"Added media file: {file_name}")

        # Auto-add to timeline if option is enabled
        if hasattr(self, 'auto_add_checkbox') and self.auto_add_checkbox.isChecked():
            self.add_file_to_timeline(file_path)

    def on_auto_add_toggled(self, checked):
        """Handle auto-add to timeline toggle"""
        if checked:
            self.status_label.setText("Auto-add to timeline: ON")
        else:
            self.status_label.setText("Auto-add to timeline: OFF")

    def set_timeline_reference(self, timeline):
        """Set reference to timeline widget"""
        self.timeline_ref = timeline

    def add_selected_to_timeline(self):
        """Add selected media files to timeline"""
        selected_items = self.file_tree.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "No Selection", "Please select media files to add to timeline.")
            return

        for item in selected_items:
            file_path = item.data(0, Qt.UserRole)
            if file_path and os.path.exists(file_path):
                self.add_file_to_timeline(file_path)

    def add_file_to_timeline(self, file_path):
        """Add a file to the appropriate timeline track"""
        if not self.timeline_ref:
            QMessageBox.warning(self, "Timeline Error", "Timeline not available. Please try again.")
            return

        try:
            # Determine file type
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']:
                track_type = 'video'
            elif file_ext in ['.mp3', '.wav', '.aac', '.flac', '.ogg', '.m4a', '.wma']:
                track_type = 'audio'
            elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']:
                track_type = 'image'
            else:
                track_type = 'video'  # Default fallback

            # Add to timeline
            if hasattr(self.timeline_ref, 'add_clip'):
                self.timeline_ref.add_clip(file_path, track_type)
                self.status_label.setText(f"Added to timeline: {os.path.basename(file_path)}")
                print(f"Added to {track_type} timeline: {file_path}")
            else:
                print("Timeline does not support add_clip method")

        except Exception as e:
            print(f"Error adding file to timeline: {e}")
            QMessageBox.warning(self, "Timeline Error", f"Could not add file to timeline: {str(e)}")

    def find_or_create_category(self, category_name):
        """Find or create a category folder"""
        # Look for existing category
        for i in range(self.file_tree.topLevelItemCount()):
            item = self.file_tree.topLevelItem(i)
            if item.text(0) == category_name:
                return item

        # Create new category
        category_item = QTreeWidgetItem([category_name, "Folder", ""])
        category_item.setIcon(0, self.style().standardIcon(QStyle.SP_DirIcon))
        self.file_tree.addTopLevelItem(category_item)
        return category_item

    def get_file_icon(self, file_type):
        """Get appropriate icon for file type"""
        if file_type == 'Video':
            return self.style().standardIcon(QStyle.SP_MediaPlay)
        elif file_type == 'Audio':
            return self.style().standardIcon(QStyle.SP_MediaVolume)
        elif file_type == 'Image':
            return self.style().standardIcon(QStyle.SP_FileIcon)
        else:
            return self.style().standardIcon(QStyle.SP_FileIcon)

    def format_file_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def get_video_duration(self, file_path):
        """Get video duration (placeholder - would use actual video analysis)"""
        try:
            # This is a placeholder - in a real implementation, you'd use ffprobe or similar
            return "00:00:00"
        except:
            return "Unknown"

    def get_audio_duration(self, file_path):
        """Get audio duration (placeholder - would use actual audio analysis)"""
        try:
            # This is a placeholder - in a real implementation, you'd use audio libraries
            return "00:00:00"
        except:
            return "Unknown"

    def on_item_double_clicked(self, item, column):
        """Handle item double click"""
        file_path = item.data(0, Qt.UserRole)
        if file_path:
            self.file_selected.emit(file_path)
            print(f"Selected file: {file_path}")

    def on_selection_changed(self):
        """Handle selection change"""
        selected_items = self.file_tree.selectedItems()
        if selected_items:
            item = selected_items[0]
            file_path = item.data(0, Qt.UserRole)
            if file_path:
                self.status_label.setText(f"Selected: {item.text(0)}")

    def filter_items(self, text):
        """Filter items based on search text"""
        # Simple filter implementation
        for i in range(self.file_tree.topLevelItemCount()):
            category = self.file_tree.topLevelItem(i)
            category_visible = False

            for j in range(category.childCount()):
                child = category.child(j)
                if text.lower() in child.text(0).lower():
                    child.setHidden(False)
                    category_visible = True
                else:
                    child.setHidden(True)

            category.setHidden(not category_visible)

    def populate_empty_project(self):
        """Create empty project structure"""
        categories = ["Videos", "Audio", "Images", "Other"]
        for category in categories:
            self.find_or_create_category(category)

    def show_context_menu(self, position):
        """Show context menu for project items"""
        item = self.file_tree.itemAt(position)
        if not item:
            return

        file_path = item.data(0, Qt.UserRole)
        if not file_path or not os.path.exists(file_path):
            return

        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #333;
                color: white;
                border: 1px solid #555;
            }
            QMenu::item {
                padding: 6px 20px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """)

        # Add to timeline action
        add_to_timeline_action = menu.addAction("➕ Add to Timeline")
        add_to_timeline_action.triggered.connect(lambda: self.add_file_to_timeline(file_path))

        # Preview action
        preview_action = menu.addAction("👁️ Preview")
        preview_action.triggered.connect(lambda: self.file_selected.emit(file_path))

        menu.addSeparator()

        # Remove from project action
        remove_action = menu.addAction("🗑️ Remove from Project")
        remove_action.triggered.connect(lambda: self.remove_file_from_project(item))

        # Show properties action
        properties_action = menu.addAction("📋 Properties")
        properties_action.triggered.connect(lambda: self.show_file_properties(file_path))

        menu.exec_(self.file_tree.mapToGlobal(position))

    def remove_file_from_project(self, item):
        """Remove file from project"""
        file_path = item.data(0, Qt.UserRole)
        if file_path in self.media_files:
            self.media_files.remove(file_path)

        # Remove from tree
        parent = item.parent()
        if parent:
            parent.removeChild(item)
        else:
            index = self.file_tree.indexOfTopLevelItem(item)
            if index >= 0:
                self.file_tree.takeTopLevelItem(index)

        self.status_label.setText(f"Removed: {os.path.basename(file_path)}")

    def show_file_properties(self, file_path):
        """Show file properties dialog"""
        if not os.path.exists(file_path):
            return

        file_stat = os.stat(file_path)
        file_size = self.format_file_size(file_stat.st_size)

        msg = QMessageBox()
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("File Properties")
        msg.setText(f"Properties for: {os.path.basename(file_path)}")
        msg.setInformativeText(f"""
Path: {file_path}
Size: {file_size}
Modified: {QDateTime.fromSecsSinceEpoch(int(file_stat.st_mtime)).toString()}
Type: {os.path.splitext(file_path)[1].upper()[1:]} File
        """)
        msg.exec_()

    def populate_sample_items(self):
        """Add sample project items"""
        self.populate_empty_project()

class DownloadManagerPanel(QWidget):
    """Download manager panel for managing video downloads"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.downloader = VideoDownloader()
        self.download_items = {}  # Track download items by ID
        self.setup_ui()

    def setup_ui(self):
        """Setup download manager UI"""
        layout = QVBoxLayout(self)

        # Header
        header = QLabel("DOWNLOAD MANAGER")
        header.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-bottom: 1px solid #555;
            }
        """)
        layout.addWidget(header)

        # URL input section
        url_section = QWidget()
        url_layout = QVBoxLayout(url_section)

        url_input_layout = QHBoxLayout()
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter video URL to download...")
        self.url_input.setStyleSheet("""
            QLineEdit {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 8px;
                border-radius: 4px;
            }
        """)

        self.add_download_btn = QPushButton("📥 Add Download")
        self.add_download_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        self.add_download_btn.clicked.connect(self.add_download)

        url_input_layout.addWidget(self.url_input)
        url_input_layout.addWidget(self.add_download_btn)
        url_layout.addLayout(url_input_layout)

        # Quality selection
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("Quality:"))
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["Best", "720p", "480p", "360p", "Audio Only"])
        self.quality_combo.setStyleSheet("""
            QComboBox {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 4px;
                border-radius: 4px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: #444;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid white;
            }
            QComboBox QAbstractItemView {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                selection-background-color: #0078d4;
                outline: none;
            }
        """)
        quality_layout.addWidget(self.quality_combo)
        url_layout.addLayout(quality_layout)

        layout.addWidget(url_section)

        # Downloads list with progress bars
        self.downloads_list = QListWidget()
        self.downloads_list.setStyleSheet("""
            QListWidget {
                background-color: #2b2b2b;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #444;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
            }
        """)
        layout.addWidget(self.downloads_list)

        # Control buttons
        controls_layout = QHBoxLayout()

        self.pause_all_btn = QPushButton("⏸️ Pause All")
        self.resume_all_btn = QPushButton("▶️ Resume All")
        self.clear_completed_btn = QPushButton("🗑️ Clear Completed")

        for btn in [self.pause_all_btn, self.resume_all_btn, self.clear_completed_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #444;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #555;
                }
            """)

        controls_layout.addWidget(self.pause_all_btn)
        controls_layout.addWidget(self.resume_all_btn)
        controls_layout.addWidget(self.clear_completed_btn)
        layout.addLayout(controls_layout)

    def add_download(self):
        """Add a new download"""
        url = self.url_input.text().strip()
        if not url:
            return

        quality = self.quality_combo.currentText()
        format_id = self.get_format_id(quality)

        # Start download with progress callback
        download_id = self.downloader.download_video(
            url, 
            format_id=format_id,
            callback=self.download_progress_callback
        )

        # Create download item with progress bar
        item_widget = self.create_download_item(url, quality)
        list_item = QListWidgetItem(self.downloads_list)
        list_item.setSizeHint(item_widget.sizeHint())
        self.downloads_list.addItem(list_item)
        self.downloads_list.setItemWidget(list_item, item_widget)
        
        # Store reference to the item
        self.download_items[download_id] = {
            'list_item': list_item,
            'widget': item_widget,
            'url': url,
            'quality': quality
        }

        # Clear input
        self.url_input.clear()

    def create_download_item(self, url, quality):
        """Create a download item widget with progress bar"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # URL and quality info
        info_layout = QHBoxLayout()
        url_label = QLabel(f"{url[:50]}... - {quality}")
        url_label.setStyleSheet("color: white; font-weight: bold;")
        info_layout.addWidget(url_label)
        
        # Status label
        self.status_label = QLabel("Queued")
        self.status_label.setStyleSheet("color: #aaa;")
        info_layout.addWidget(self.status_label)
        info_layout.addStretch()
        
        layout.addLayout(info_layout)
        
        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 100)
        progress_bar.setValue(0)
        progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #555;
                border-radius: 3px;
                text-align: center;
                background-color: #333;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                width: 10px;
            }
        """)
        layout.addWidget(progress_bar)
        
        # Speed and ETA info
        info_layout2 = QHBoxLayout()
        self.speed_label = QLabel("Speed: 0 KB/s")
        self.speed_label.setStyleSheet("color: #888; font-size: 10px;")
        info_layout2.addWidget(self.speed_label)
        
        self.eta_label = QLabel("ETA: --:--")
        self.eta_label.setStyleSheet("color: #888; font-size: 10px;")
        info_layout2.addWidget(self.eta_label)
        info_layout2.addStretch()
        
        layout.addLayout(info_layout2)
        
        return widget

    def download_progress_callback(self, progress_info):
        """Callback for download progress updates"""
        download_id = None
        for did, info in self.downloader.active_downloads.items():
            if info.get('url') == progress_info.get('url'):
                download_id = did
                break
        
        if not download_id or download_id not in self.download_items:
            return
            
        item_info = self.download_items[download_id]
        widget = item_info['widget']
        
        # Update progress bar
        progress_bar = widget.findChild(QProgressBar)
        if progress_bar:
            progress_bar.setValue(int(progress_info.get('progress', 0)))
        
        # Update status label
        status_label = widget.findChild(QLabel)
        if status_label and status_label != widget.layout().itemAt(0).itemAt(1).widget():
            status_label = widget.layout().itemAt(0).itemAt(1).widget()
            status_label.setText(progress_info.get('status', 'Unknown').capitalize())
            
            # Set color based on status
            if progress_info.get('status') == 'downloading':
                status_label.setStyleSheet("color: #4CAF50;")
            elif progress_info.get('status') == 'completed':
                status_label.setStyleSheet("color: #2196F3;")
            elif progress_info.get('status') == 'failed':
                status_label.setStyleSheet("color: #F44336;")
        
        # Update speed and ETA
        speed_label = widget.findChild(QLabel, "speed_label")
        if speed_label:
            speed = progress_info.get('speed', 0)
            if speed > 0:
                if speed > 1024*1024:
                    speed_text = f"Speed: {speed/(1024*1024):.1f} MB/s"
                elif speed > 1024:
                    speed_text = f"Speed: {speed/1024:.1f} KB/s"
                else:
                    speed_text = f"Speed: {speed} B/s"
                speed_label.setText(speed_text)
        
        eta_label = widget.findChild(QLabel, "eta_label")
        if eta_label:
            eta = progress_info.get('eta', 0)
            if eta and eta > 0:
                minutes, seconds = divmod(eta, 60)
                hours, minutes = divmod(minutes, 60)
                if hours > 0:
                    eta_text = f"ETA: {hours}h {minutes}m {seconds}s"
                elif minutes > 0:
                    eta_text = f"ETA: {minutes}m {seconds}s"
                else:
                    eta_text = f"ETA: {seconds}s"
                eta_label.setText(eta_text)

    def get_format_id(self, quality):
        """Convert quality selection to format ID"""
        quality_map = {
            "Best": "best",
            "720p": "best[height<=720]",
            "480p": "best[height<=480]",
            "360p": "best[height<=360]",
            "Audio Only": "bestaudio"
        }
        return quality_map.get(quality, "best")

class SocialUploadPanel(QWidget):
    """Social media upload panel"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Setup social upload UI"""
        layout = QVBoxLayout(self)

        # Header
        header = QLabel("SOCIAL MEDIA UPLOAD")
        header.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-bottom: 1px solid #555;
            }
        """)
        layout.addWidget(header)

        # Video file selection
        file_section = QWidget()
        file_layout = QHBoxLayout(file_section)

        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("Select video file to upload...")
        self.file_path_input.setStyleSheet("""
            QLineEdit {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 8px;
                border-radius: 4px;
            }
        """)

        self.browse_btn = QPushButton("📁 Browse")
        self.browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #444;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        self.browse_btn.clicked.connect(self.browse_file)

        file_layout.addWidget(self.file_path_input)
        file_layout.addWidget(self.browse_btn)
        layout.addWidget(file_section)

        # Platform selection
        platform_layout = QHBoxLayout()
        platform_layout.addWidget(QLabel("Platform:"))
        self.platform_combo = QComboBox()
        self.platform_combo.addItems(["YouTube", "Facebook", "Instagram", "TikTok", "Twitter"])
        self.platform_combo.setStyleSheet("""
            QComboBox {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 4px;
                border-radius: 4px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: #444;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid white;
            }
            QComboBox QAbstractItemView {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                selection-background-color: #0078d4;
                outline: none;
            }
        """)
        platform_layout.addWidget(self.platform_combo)
        layout.addLayout(platform_layout)

        # Title and description
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("Video title...")
        self.title_input.setStyleSheet("""
            QLineEdit {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 8px;
                border-radius: 4px;
                margin: 4px 0;
            }
        """)
        layout.addWidget(self.title_input)

        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("Video description...")
        self.description_input.setMaximumHeight(100)
        self.description_input.setStyleSheet("""
            QTextEdit {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 8px;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.description_input)

        # Upload button
        self.upload_btn = QPushButton("🚀 Upload to Social Media")
        self.upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.upload_btn.clicked.connect(self.upload_video)
        layout.addWidget(self.upload_btn)

        # Upload status
        self.status_label = QLabel("Ready to upload")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                padding: 8px;
                font-style: italic;
            }
        """)
        layout.addWidget(self.status_label)

    def browse_file(self):
        """Browse for video file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Video File", "",
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv);;All Files (*)"
        )
        if file_path:
            self.file_path_input.setText(file_path)

    def upload_video(self):
        """Upload video to selected platform"""
        file_path = self.file_path_input.text().strip()
        if not file_path:
            QMessageBox.warning(self, "No File", "Please select a video file first.")
            return

        platform = self.platform_combo.currentText().lower()
        title = self.title_input.text().strip()
        description = self.description_input.toPlainText().strip()

        if not title:
            QMessageBox.warning(self, "No Title", "Please enter a video title.")
            return

        # Create upload config
        upload_config = UploadConfig(
            title=title,
            description=description,
            tags=[],
            privacy="public"
        )

        # Start upload
        self.status_label.setText(f"Uploading to {platform}...")
        self.upload_btn.setEnabled(False)

        try:
            result = upload_video(file_path, platform, upload_config)
            if result.get('success'):
                self.status_label.setText(f"✅ Upload successful! Video ID: {result.get('video_id')}")
                QMessageBox.information(self, "Upload Success",
                                      f"Video uploaded successfully to {platform}!\n\nVideo URL: {result.get('video_url')}")
            else:
                self.status_label.setText(f"❌ Upload failed: {result.get('error')}")
                QMessageBox.warning(self, "Upload Failed", result.get('error', 'Unknown error'))
        except Exception as e:
            self.status_label.setText(f"❌ Upload error: {str(e)}")
            QMessageBox.critical(self, "Upload Error", str(e))
        finally:
            self.upload_btn.setEnabled(True)

class AudioProcessingPanel(QWidget):
    """Audio processing and editing panel"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.audio_manager = AudioManager()
        self.setup_ui()

    def setup_ui(self):
        """Setup audio processing UI"""
        layout = QVBoxLayout(self)

        # Header
        header = QLabel("AUDIO PROCESSING")
        header.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-bottom: 1px solid #555;
            }
        """)
        layout.addWidget(header)

        # Audio file selection
        file_section = QWidget()
        file_layout = QHBoxLayout(file_section)

        self.audio_file_input = QLineEdit()
        self.audio_file_input.setPlaceholderText("Select audio file...")
        self.audio_file_input.setStyleSheet("""
            QLineEdit {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 8px;
                border-radius: 4px;
            }
        """)

        self.browse_audio_btn = QPushButton("📁 Browse")
        self.browse_audio_btn.setStyleSheet("""
            QPushButton {
                background-color: #444;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        self.browse_audio_btn.clicked.connect(self.browse_audio_file)

        file_layout.addWidget(self.audio_file_input)
        file_layout.addWidget(self.browse_audio_btn)
        layout.addWidget(file_section)

        # Audio effects
        effects_group = QGroupBox("Audio Effects")
        effects_group.setStyleSheet("""
            QGroupBox {
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                margin: 8px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        effects_layout = QVBoxLayout(effects_group)

        # Volume control
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("Volume:"))
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(-20, 20)
        self.volume_slider.setValue(0)
        self.volume_label = QLabel("0 dB")
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_label)
        effects_layout.addLayout(volume_layout)

        # Speed control
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("Speed:"))
        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(50, 200)
        self.speed_slider.setValue(100)
        self.speed_label = QLabel("1.0x")
        speed_layout.addWidget(self.speed_slider)
        speed_layout.addWidget(self.speed_label)
        effects_layout.addLayout(speed_layout)

        # Effect buttons
        effects_buttons_layout = QHBoxLayout()

        self.normalize_btn = QPushButton("🔊 Normalize")
        self.noise_reduction_btn = QPushButton("🔇 Noise Reduction")
        self.extract_audio_btn = QPushButton("🎵 Extract from Video")

        for btn in [self.normalize_btn, self.noise_reduction_btn, self.extract_audio_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #6f42c1;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #5a32a3;
                }
            """)

        effects_buttons_layout.addWidget(self.normalize_btn)
        effects_buttons_layout.addWidget(self.noise_reduction_btn)
        effects_buttons_layout.addWidget(self.extract_audio_btn)
        effects_layout.addLayout(effects_buttons_layout)

        layout.addWidget(effects_group)

        # Process button
        self.process_audio_btn = QPushButton("🎛️ Process Audio")
        self.process_audio_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.process_audio_btn.clicked.connect(self.process_audio)
        layout.addWidget(self.process_audio_btn)

        # Connect sliders
        self.volume_slider.valueChanged.connect(lambda v: self.volume_label.setText(f"{v} dB"))
        self.speed_slider.valueChanged.connect(lambda v: self.speed_label.setText(f"{v/100:.1f}x"))

    def browse_audio_file(self):
        """Browse for audio file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Audio File", "",
            "Audio Files (*.mp3 *.wav *.aac *.flac *.ogg);;All Files (*)"
        )
        if file_path:
            self.audio_file_input.setText(file_path)

    def process_audio(self):
        """Process audio with selected effects"""
        audio_file = self.audio_file_input.text().strip()
        if not audio_file:
            QMessageBox.warning(self, "No File", "Please select an audio file first.")
            return

        # Get effect parameters
        volume_change = self.volume_slider.value()
        speed_factor = self.speed_slider.value() / 100.0

        # Process audio (placeholder implementation)
        QMessageBox.information(self, "Audio Processing",
                              f"Processing audio with:\n"
                              f"Volume: {volume_change} dB\n"
                              f"Speed: {speed_factor}x\n\n"
                              f"Audio processing functionality will be implemented with the audio manager.")

class AdvancedEditorPanel(QWidget):
    """Advanced video editing tools panel"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Setup advanced editor UI"""
        # Create main scroll area to handle overflow
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Main container widget
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header
        header = QLabel("ADVANCED EDITOR")
        header.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-bottom: 1px solid #555;
                font-size: 14px;
            }
        """)
        header.setFixedHeight(40)
        layout.addWidget(header)

        # Video editing tools
        video_group = QGroupBox("Video Tools")
        video_group.setStyleSheet("""
            QGroupBox {
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                margin: 4px 0;
                padding-top: 15px;
                font-size: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                font-weight: bold;
            }
        """)
        video_layout = QVBoxLayout(video_group)
        video_layout.setSpacing(8)

        # Basic editing buttons
        basic_tools_layout = QHBoxLayout()
        basic_tools_layout.setSpacing(5)

        self.cut_btn = QPushButton("✂️ Cut")
        self.trim_btn = QPushButton("📏 Trim")
        self.merge_btn = QPushButton("🔗 Merge")

        for btn in [self.cut_btn, self.trim_btn, self.merge_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 11px;
                    min-height: 30px;
                    max-height: 30px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        basic_tools_layout.addWidget(self.cut_btn)
        basic_tools_layout.addWidget(self.trim_btn)
        basic_tools_layout.addWidget(self.merge_btn)
        video_layout.addLayout(basic_tools_layout)

        # Advanced tools
        advanced_tools_layout = QHBoxLayout()
        advanced_tools_layout.setSpacing(5)

        self.add_text_btn = QPushButton("📝 Text")
        self.add_image_btn = QPushButton("🖼️ Image")
        self.transitions_btn = QPushButton("🎬 Transitions")

        for btn in [self.add_text_btn, self.add_image_btn, self.transitions_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #fd7e14;
                    color: white;
                    border: none;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 11px;
                    min-height: 30px;
                    max-height: 30px;
                }
                QPushButton:hover {
                    background-color: #e8650e;
                }
            """)
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        advanced_tools_layout.addWidget(self.add_text_btn)
        advanced_tools_layout.addWidget(self.add_image_btn)
        advanced_tools_layout.addWidget(self.transitions_btn)
        video_layout.addLayout(advanced_tools_layout)

        layout.addWidget(video_group)

        # Color grading and filters
        color_group = QGroupBox("Color & Filters")
        color_group.setStyleSheet("""
            QGroupBox {
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                margin: 4px 0;
                padding-top: 15px;
                font-size: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                font-weight: bold;
            }
        """)
        color_layout = QVBoxLayout(color_group)
        color_layout.setSpacing(8)

        # Color adjustment sliders with improved layout
        def create_slider_row(label_text, slider_range=(-100, 100), default_value=0):
            row_layout = QHBoxLayout()
            row_layout.setSpacing(8)

            label = QLabel(label_text)
            label.setFixedWidth(80)
            label.setStyleSheet("color: white; font-size: 11px;")
            row_layout.addWidget(label)

            slider = QSlider(Qt.Horizontal)
            slider.setRange(slider_range[0], slider_range[1])
            slider.setValue(default_value)
            slider.setStyleSheet("""
                QSlider::groove:horizontal {
                    border: 1px solid #555;
                    height: 6px;
                    background: #333;
                    border-radius: 3px;
                }
                QSlider::handle:horizontal {
                    background: #007ACC;
                    border: 1px solid #005A9E;
                    width: 14px;
                    margin: -4px 0;
                    border-radius: 7px;
                }
            """)
            row_layout.addWidget(slider)

            value_label = QLabel(str(default_value))
            value_label.setFixedWidth(30)
            value_label.setStyleSheet("color: white; font-size: 11px;")
            row_layout.addWidget(value_label)

            return row_layout, slider, value_label

        # Create sliders
        brightness_layout, self.brightness_slider, self.brightness_label = create_slider_row("Brightness:")
        color_layout.addLayout(brightness_layout)

        contrast_layout, self.contrast_slider, self.contrast_label = create_slider_row("Contrast:")
        color_layout.addLayout(contrast_layout)

        saturation_layout, self.saturation_slider, self.saturation_label = create_slider_row("Saturation:")
        color_layout.addLayout(saturation_layout)

        # Filter buttons
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(5)

        self.blur_btn = QPushButton("🌫️ Blur")
        self.sharpen_btn = QPushButton("🔍 Sharpen")
        self.vintage_btn = QPushButton("📸 Vintage")

        for btn in [self.blur_btn, self.sharpen_btn, self.vintage_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #6f42c1;
                    color: white;
                    border: none;
                    padding: 6px 8px;
                    border-radius: 4px;
                    font-size: 11px;
                    min-height: 28px;
                    max-height: 28px;
                }
                QPushButton:hover {
                    background-color: #5a32a3;
                }
            """)
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        filter_layout.addWidget(self.blur_btn)
        filter_layout.addWidget(self.sharpen_btn)
        filter_layout.addWidget(self.vintage_btn)
        color_layout.addLayout(filter_layout)

        layout.addWidget(color_group)

        # Export options
        export_group = QGroupBox("Export")
        export_group.setStyleSheet("""
            QGroupBox {
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                margin: 4px 0;
                padding-top: 15px;
                font-size: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                font-weight: bold;
            }
        """)
        export_layout = QVBoxLayout(export_group)
        export_layout.setSpacing(8)

        # Export format selection
        format_layout = QHBoxLayout()
        format_layout.setSpacing(8)

        format_label = QLabel("Format:")
        format_label.setFixedWidth(60)
        format_label.setStyleSheet("color: white; font-size: 11px;")
        format_layout.addWidget(format_label)

        self.export_format_combo = QComboBox()
        self.export_format_combo.addItems(["MP4", "AVI", "MOV", "MKV", "WebM"])
        self.export_format_combo.setStyleSheet("""
            QComboBox {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 6px;
                border-radius: 4px;
                font-size: 11px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid white;
            }
        """)
        format_layout.addWidget(self.export_format_combo)
        export_layout.addLayout(format_layout)

        # Quality selection
        quality_layout = QHBoxLayout()
        quality_layout.setSpacing(8)

        quality_label = QLabel("Quality:")
        quality_label.setFixedWidth(60)
        quality_label.setStyleSheet("color: white; font-size: 11px;")
        quality_layout.addWidget(quality_label)

        self.export_quality_combo = QComboBox()
        self.export_quality_combo.addItems(["4K", "1080p", "720p", "480p", "360p"])
        self.export_quality_combo.setCurrentText("1080p")
        self.export_quality_combo.setStyleSheet("""
            QComboBox {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 6px;
                border-radius: 4px;
                font-size: 11px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid white;
            }
        """)
        quality_layout.addWidget(self.export_quality_combo)
        export_layout.addLayout(quality_layout)

        # Export button
        self.export_btn = QPushButton("🎬 Export Video")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.export_btn.clicked.connect(self.export_video)
        export_layout.addWidget(self.export_btn)

        layout.addWidget(export_group)

        # Add stretch to push everything to top
        layout.addStretch()

        # Set the container as the scroll area's widget
        scroll_area.setWidget(container)

        # Set the scroll area as the main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

        # Connect sliders
        self.brightness_slider.valueChanged.connect(lambda v: self.brightness_label.setText(str(v)))
        self.contrast_slider.valueChanged.connect(lambda v: self.contrast_label.setText(str(v)))
        self.saturation_slider.valueChanged.connect(lambda v: self.saturation_label.setText(str(v)))

        # Connect buttons
        self.cut_btn.clicked.connect(self.cut_video)
        self.trim_btn.clicked.connect(self.trim_video)
        self.merge_btn.clicked.connect(self.merge_videos)
        self.add_text_btn.clicked.connect(self.add_text)
        self.add_image_btn.clicked.connect(self.add_image)
        self.transitions_btn.clicked.connect(self.add_transitions)
        self.blur_btn.clicked.connect(self.apply_blur)
        self.sharpen_btn.clicked.connect(self.apply_sharpen)
        self.vintage_btn.clicked.connect(self.apply_vintage)

    def cut_video(self):
        """Cut video functionality"""
        QMessageBox.information(self, "Cut Video", "Cut video functionality will be implemented.")

    def trim_video(self):
        """Trim video functionality"""
        QMessageBox.information(self, "Trim Video", "Trim video functionality will be implemented.")

    def merge_videos(self):
        """Merge videos functionality"""
        QMessageBox.information(self, "Merge Videos", "Merge videos functionality will be implemented.")

    def add_text(self):
        """Add text overlay"""
        self.show_text_overlay_panel()

    def add_image(self):
        """Add image overlay"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Image",
            "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif *.tiff)"
        )

        if file_path:
            QMessageBox.information(self, "Image Added", f"Image selected: {os.path.basename(file_path)}\n\nImage overlay functionality will be implemented.")

    def add_transitions(self):
        """Add transitions"""
        QMessageBox.information(self, "Transitions", "Transitions functionality will be implemented.")

    def apply_blur(self):
        """Apply blur effect"""
        QMessageBox.information(self, "Blur Effect", "Blur effect will be applied.")

    def apply_sharpen(self):
        """Apply sharpen effect"""
        QMessageBox.information(self, "Sharpen Effect", "Sharpen effect will be applied.")

    def apply_vintage(self):
        """Apply vintage effect"""
        QMessageBox.information(self, "Vintage Effect", "Vintage effect will be applied.")

    def export_video(self):
        """Export the edited video"""
        format_type = self.export_format_combo.currentText()
        quality = self.export_quality_combo.currentText()

        # Show export dialog
        output_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Video",
            f"exported_video.{format_type.lower()}",
            f"{format_type} Files (*.{format_type.lower()})"
        )

        if output_path:
            # Create export dialog
            export_dialog = QDialog(self)
            export_dialog.setWindowTitle("Export Started")
            export_dialog.setModal(True)
            export_dialog.resize(400, 150)

            layout = QVBoxLayout(export_dialog)

            info_label = QLabel(f"Exporting video to:\n{output_path}\n\nFormat: {format_type}\nQuality: {quality}")
            info_label.setWordWrap(True)
            layout.addWidget(info_label)

            ok_btn = QPushButton("OK")
            ok_btn.clicked.connect(export_dialog.accept)
            layout.addWidget(ok_btn)

            export_dialog.exec_()

        # Get export path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Video", f"exported_video.{format_type.lower()}",
            f"{format_type} Files (*.{format_type.lower()});;All Files (*)"
        )

        if file_path:
            QMessageBox.information(self, "Export Started",
                                  f"Exporting video to:\n{file_path}\n\n"
                                  f"Format: {format_type}\n"
                                  f"Quality: {quality}\n\n"
                                  f"Export functionality will be implemented with FFmpeg integration.")

class EffectsPanel(QWidget):
    """Effects and transitions panel"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup effects panel UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("EFFECTS")
        header.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-bottom: 1px solid #555;
            }
        """)
        layout.addWidget(header)
        
        # Search bar
        search_bar = QLineEdit()
        search_bar.setPlaceholderText("Search effects...")
        search_bar.setStyleSheet("""
            QLineEdit {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                padding: 6px;
                margin: 4px;
            }
        """)
        layout.addWidget(search_bar)
        
        # Effects tree
        self.effects_tree = QTreeWidget()
        self.effects_tree.setHeaderLabel("Effects")
        self.effects_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #2b2b2b;
                color: white;
                border: none;
                alternate-background-color: #333;
            }
            QTreeWidget::item {
                padding: 4px;
            }
            QTreeWidget::item:selected {
                background-color: #0078d4;
            }
        """)
        layout.addWidget(self.effects_tree)
        
        # Add sample effects
        self.populate_sample_effects()
        
    def populate_sample_effects(self):
        """Add sample effects"""
        # Video effects
        video_fx = QTreeWidgetItem(["Video Effects"])
        self.effects_tree.addTopLevelItem(video_fx)
        
        # Color correction
        color_folder = QTreeWidgetItem(["Color Correction"])
        video_fx.addChild(color_folder)
        color_folder.addChild(QTreeWidgetItem(["Brightness & Contrast"]))
        color_folder.addChild(QTreeWidgetItem(["Color Balance"]))
        color_folder.addChild(QTreeWidgetItem(["Hue & Saturation"]))
        
        # Blur & Sharpen
        blur_folder = QTreeWidgetItem(["Blur & Sharpen"])
        video_fx.addChild(blur_folder)
        blur_folder.addChild(QTreeWidgetItem(["Gaussian Blur"]))
        blur_folder.addChild(QTreeWidgetItem(["Motion Blur"]))
        
        # Audio effects
        audio_fx = QTreeWidgetItem(["Audio Effects"])
        self.effects_tree.addTopLevelItem(audio_fx)
        audio_fx.addChild(QTreeWidgetItem(["Amplify"]))
        audio_fx.addChild(QTreeWidgetItem(["Noise Reduction"]))
        audio_fx.addChild(QTreeWidgetItem(["Reverb"]))
        
        # Transitions
        transitions = QTreeWidgetItem(["Video Transitions"])
        self.effects_tree.addTopLevelItem(transitions)
        transitions.addChild(QTreeWidgetItem(["Cross Dissolve"]))
        transitions.addChild(QTreeWidgetItem(["Fade to Black"]))
        transitions.addChild(QTreeWidgetItem(["Wipe"]))

class PropertiesPanel(QWidget):
    """Properties panel for selected clips"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup properties panel UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header = QLabel("PROPERTIES")
        header.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: white;
                padding: 8px;
                font-weight: bold;
                border-bottom: 1px solid #555;
            }
        """)
        layout.addWidget(header)
        
        # Properties scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #2b2b2b;
                border: none;
            }
        """)
        
        # Properties content
        content = QWidget()
        content_layout = QVBoxLayout(content)
        
        # Basic properties
        self.add_property_group(content_layout, "Basic", [
            ("Position X", "0"),
            ("Position Y", "0"),
            ("Scale", "100%"),
            ("Rotation", "0°"),
            ("Opacity", "100%")
        ])
        
        # Video properties
        self.add_property_group(content_layout, "Video", [
            ("Brightness", "0"),
            ("Contrast", "0"),
            ("Saturation", "0"),
            ("Hue", "0")
        ])
        
        # Audio properties
        self.add_property_group(content_layout, "Audio", [
            ("Volume", "100%"),
            ("Pan", "0"),
            ("Pitch", "0")
        ])
        
        content_layout.addStretch()
        scroll_area.setWidget(content)
        layout.addWidget(scroll_area)
        
    def add_property_group(self, layout, title, properties):
        """Add a group of properties"""
        # Group header
        group_header = QLabel(title)
        group_header.setStyleSheet("""
            QLabel {
                color: #ccc;
                font-weight: bold;
                padding: 8px 4px 4px 4px;
                border-bottom: 1px solid #444;
                margin-top: 8px;
            }
        """)
        layout.addWidget(group_header)
        
        # Properties
        for prop_name, prop_value in properties:
            prop_layout = QHBoxLayout()
            
            label = QLabel(prop_name)
            label.setStyleSheet("color: white; padding: 2px;")
            label.setFixedWidth(80)
            
            value_edit = QLineEdit(prop_value)
            value_edit.setStyleSheet("""
                QLineEdit {
                    background-color: #333;
                    color: white;
                    border: 1px solid #555;
                    padding: 4px;
                }
            """)
            
            prop_layout.addWidget(label)
            prop_layout.addWidget(value_edit)
            layout.addLayout(prop_layout)

class PremiereInterface(QMainWindow):
    """Main Premiere Pro-style interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Professional Video Editor - Premiere Style")
        self.setGeometry(100, 100, 1600, 900)

        # Initialize video editing state
        self.current_video_path = None

        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: white;
            }
            QSplitter::handle {
                background-color: #444;
            }
            QSplitter::handle:horizontal {
                width: 3px;
            }
            QSplitter::handle:vertical {
                height: 3px;
            }
        """)
        
        self.setup_ui()
        self.create_menu_bar()
        self.create_toolbar()
        self.create_status_bar()
        
    def setup_ui(self):
        """Setup main interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Main splitter (horizontal)
        main_splitter = QSplitter(Qt.Horizontal)
        
        # Left panel with tabs (Project, Search, Files) - improved sizing
        left_panel = QWidget()
        left_panel.setMinimumWidth(380)
        left_panel.setMaximumWidth(420)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(5)

        # Create tab widget for left panel
        self.left_tabs = QTabWidget()
        self.left_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #2b2b2b;
                margin-top: 2px;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #404040;
                color: white;
                padding: 8px 12px;
                margin-right: 2px;
                margin-bottom: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 60px;
                max-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
                margin-bottom: 0px;
            }
            QTabBar::tab:hover {
                background-color: #505050;
            }
        """)

        # Project panel
        self.project_panel = ProjectPanel()
        self.left_tabs.addTab(self.project_panel, "📁 Project")

        # Search panel
        self.search_panel = VideoSearchWidget()
        self.search_panel.video_selected.connect(self.load_video_from_search)
        self.left_tabs.addTab(self.search_panel, "🔍 Search")

        # File browser panel
        self.file_browser = FileBrowserWidget()
        self.file_browser.file_selected.connect(self.load_video_from_file)
        self.left_tabs.addTab(self.file_browser, "📁 Files")

        # Download manager panel
        self.download_manager = DownloadManagerPanel()
        self.left_tabs.addTab(self.download_manager, "📥 Downloads")

        # Social upload panel
        self.social_upload = SocialUploadPanel()
        self.left_tabs.addTab(self.social_upload, "🚀 Upload")

        # Text overlay panel
        self.text_overlay_editor = TextOverlayEditor()
        self.text_overlay_editor.overlay_changed.connect(self.add_text_overlay)
        self.left_tabs.addTab(self.text_overlay_editor, "📝 Text")

        # Audio processing panel
        self.audio_processing = AudioProcessingPanel()
        self.left_tabs.addTab(self.audio_processing, "🎵 Audio")

        # Advanced editor panel
        self.advanced_editor = AdvancedEditorPanel()
        self.left_tabs.addTab(self.advanced_editor, "✂️ Editor")

        # Effects panel (separate)
        self.effects_panel = EffectsPanel()

        # Add to left layout
        left_layout.addWidget(self.left_tabs, 2)
        left_layout.addWidget(self.effects_panel, 1)
        
        # Center area (Preview + Timeline)
        center_splitter = QSplitter(Qt.Vertical)
        
        # Preview area
        self.video_player = UnifiedVideoPlayer()
        self.video_player.setMinimumHeight(300)
        
        # Timeline
        self.timeline = PremiereTimeline()
        self.timeline.setMinimumHeight(250)

        # Connect project panel to timeline
        self.project_panel.set_timeline_reference(self.timeline)

        # Connect timeline signals to video player
        self.timeline.clip_added.connect(self.on_timeline_clip_added)
        self.timeline.playhead_moved.connect(self.on_playhead_moved)

        center_splitter.addWidget(self.video_player)
        center_splitter.addWidget(self.timeline)
        center_splitter.setSizes([400, 300])
        
        # Right panel (Properties) - improved sizing
        self.properties_panel = PropertiesPanel()
        self.properties_panel.setMinimumWidth(260)
        self.properties_panel.setMaximumWidth(320)
        
        # Add to main splitter with improved proportions
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(center_splitter)
        main_splitter.addWidget(self.properties_panel)
        main_splitter.setSizes([380, 1040, 280])

        # Prevent panels from collapsing to avoid overlapping
        main_splitter.setChildrenCollapsible(False)
        main_splitter.setStretchFactor(0, 0)  # Left panel fixed
        main_splitter.setStretchFactor(1, 1)  # Center stretches
        main_splitter.setStretchFactor(2, 0)  # Right panel fixed
        
        main_layout.addWidget(main_splitter)
        
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #333;
                color: white;
                border-bottom: 1px solid #555;
            }
            QMenuBar::item {
                padding: 6px 12px;
            }
            QMenuBar::item:selected {
                background-color: #0078d4;
            }
            QMenu {
                background-color: #333;
                color: white;
                border: 1px solid #555;
            }
            QMenu::item {
                padding: 6px 20px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """)
        
        # File menu
        file_menu = menubar.addMenu('File')
        file_menu.addAction('New Project', self.new_project)
        file_menu.addAction('Open Project', self.open_project)
        file_menu.addAction('Save Project', self.save_project)
        file_menu.addSeparator()
        file_menu.addAction('Import Media', self.import_media)
        file_menu.addAction('Export Video', self.export_video)

        # Edit menu
        edit_menu = menubar.addMenu('Edit')
        edit_menu.addAction('Undo', self.undo)
        edit_menu.addAction('Redo', self.redo)
        edit_menu.addSeparator()
        edit_menu.addAction('Cut', self.cut)
        edit_menu.addAction('Copy', self.copy)
        edit_menu.addAction('Paste', self.paste)

        # Search menu
        search_menu = menubar.addMenu('Search')
        search_menu.addAction('🔍 Video Search', self.show_search_panel)
        search_menu.addAction('📁 Browse Files', self.show_file_browser)
        search_menu.addSeparator()
        search_menu.addAction('⬇️ Download Manager', self.show_download_manager)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')
        tools_menu.addAction('🚀 Social Media Upload', self.show_upload_panel)
        tools_menu.addAction('🎵 Audio Processing', self.show_audio_panel)
        tools_menu.addAction('✂️ Advanced Editor', self.show_editor_panel)
        tools_menu.addSeparator()
        tools_menu.addAction('🎬 Export Video', self.export_video_advanced)

        # Window menu
        window_menu = menubar.addMenu('Window')
        window_menu.addAction('Reset Workspace', self.reset_workspace)
        window_menu.addSeparator()
        window_menu.addAction('Show Search Panel', self.show_search_panel)
        window_menu.addAction('Show File Browser', self.show_file_browser)
        
    def create_toolbar(self):
        """Create toolbar"""
        toolbar = self.addToolBar('Main')
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #404040;
                border-bottom: 1px solid #555;
                spacing: 4px;
            }
            QToolButton {
                background-color: #505050;
                border: 1px solid #666;
                color: white;
                padding: 6px;
                margin: 2px;
            }
            QToolButton:hover {
                background-color: #606060;
            }
            QToolButton:pressed {
                background-color: #707070;
            }
        """)
        
        # Import/Export
        toolbar.addAction('📁 Import', self.import_video)
        toolbar.addAction('💾 Export', self.export_video_quick)
        toolbar.addAction('📝💾 Export with Text', self.export_video_with_text)
        toolbar.addSeparator()

        # Playback controls
        toolbar.addAction('⏮️', self.previous_frame)
        toolbar.addAction('⏯️', self.play_pause)
        toolbar.addAction('⏭️', self.next_frame)
        toolbar.addSeparator()
        
        # Edit tools
        toolbar.addAction('✂️', self.cut_tool)
        toolbar.addAction('🔧', self.selection_tool)
        toolbar.addAction('🖐️', self.hand_tool)
        toolbar.addSeparator()

        # Zoom
        toolbar.addAction('🔍-', self.timeline.zoom_out)
        toolbar.addAction('🔍+', self.timeline.zoom_in)

    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #333;
                color: white;
                border-top: 1px solid #555;
            }
        """)
        self.status_bar.showMessage("Ready")
        
    # Video loading methods
    def load_video_from_search(self, video_info):
        """Load video from search results with enhanced handling"""
        try:
            playable_url = video_info.get('playable_url', '')
            video_url = video_info.get('url', '')
            video_title = video_info.get('title', 'Unknown Video')
            playback_method = video_info.get('playback_method', 'unknown')

            print(f"🔍 Loading video from search: {video_title}")
            print(f"📺 Platform: {video_info.get('platform', 'Unknown')}")
            print(f"🎬 Playback method: {playback_method}")

            if playable_url:
                print(f"✅ Loading with URL: {playable_url[:100]}...")

                # Load video in player
                self.video_player.load(playable_url)

                # Update status
                self.status_bar.showMessage(f"Loading: {video_title}")

                # Show loading message instead of blocking dialog
                self.show_video_loading_status(video_title, video_info.get('platform', 'Unknown'), playback_method)

            elif video_url:
                print(f"🔗 Using fallback URL: {video_url}")
                self.video_player.load(video_url)
                self.status_bar.showMessage(f"Loading: {video_title}")
                self.show_video_loading_status(video_title, video_info.get('platform', 'Unknown'), 'fallback')
            else:
                QMessageBox.warning(self, "Error", "No video URL available")

        except Exception as e:
            print(f"❌ Error loading video from search: {e}")
            self.status_bar.showMessage(f"Error loading video: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to load video from search: {str(e)}")

    def show_video_loading_status(self, title, platform, method):
        """Show non-blocking video loading status"""
        try:
            # Create a temporary status message
            status_msg = f"✅ Video Loaded: {title[:30]}{'...' if len(title) > 30 else ''}"
            self.status_bar.showMessage(status_msg)

            # Show brief notification
            print(f"📺 Video loaded successfully!")
            print(f"   Title: {title}")
            print(f"   Platform: {platform}")
            print(f"   Method: {method}")
            print(f"   Use player controls to play/pause")

        except Exception as e:
            print(f"Error showing status: {e}")

    def load_video_from_file(self, file_path):
        """Load video from file browser"""
        try:
            print(f"🎬 Loading video from file: {file_path}")

            if os.path.exists(file_path) and os.path.isfile(file_path):
                # Check if it's a video file
                video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
                file_ext = os.path.splitext(file_path)[1].lower()

                if file_ext in video_extensions:
                    # Load video directly with file path (not URL)
                    print(f"📁 Loading video file: {file_path}")

                    # Load video in player using the file path directly
                    self.video_player.load(file_path)

                    # Show success message
                    file_name = os.path.basename(file_path)
                    print(f"✅ Video loaded successfully: {file_name}")

                    # Update status bar
                    self.status_bar.showMessage(f"Loaded: {file_name}")

                    # Store current video path for editing
                    self.current_video_path = file_path

                else:
                    QMessageBox.warning(self, "Invalid File",
                                      f"File type not supported: {file_ext}\n\nSupported formats: MP4, AVI, MOV, MKV, WMV, FLV, WebM, M4V, 3GP")
            else:
                QMessageBox.warning(self, "Error", "File not found or is not a valid file")

        except Exception as e:
            print(f"❌ Error loading video: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load video: {str(e)}")

    def on_timeline_clip_added(self, clip_data):
        """Handle when a clip is added to the timeline"""
        try:
            file_path = clip_data.get('file_path')
            print(f"📋 Timeline clip added: {clip_data}")

            if not file_path:
                print("⚠️ No file path in clip data")
                self.status_bar.showMessage("Error: No file path provided")
                return

            if not os.path.exists(file_path):
                print(f"⚠️ File does not exist: {file_path}")
                self.status_bar.showMessage(f"Error: File not found - {os.path.basename(file_path)}")
                return

            print(f"🎬 Loading video from timeline: {os.path.basename(file_path)}")

            # Load the video in the player
            self.video_player.load(file_path)

            # Store current video path for other operations
            self.current_video_path = file_path

            # Update status
            self.status_bar.showMessage(f"Loaded: {os.path.basename(file_path)}")

        except Exception as e:
            print(f"❌ Error loading video from timeline: {e}")
            self.status_bar.showMessage(f"Error loading video: {str(e)}")

    def on_playhead_moved(self, time_seconds):
        """Handle playhead movement from timeline"""
        try:
            # Seek video player to the specified time
            if hasattr(self.video_player, 'seek'):
                position_ms = int(time_seconds * 1000)  # Convert to milliseconds
                self.video_player.seek(position_ms // 1000)  # Convert back to seconds for unified player
                print(f"🎬 Video player seeked to: {time_seconds:.2f}s")

                # Update status bar with current time
                minutes = int(time_seconds // 60)
                seconds = int(time_seconds % 60)
                self.status_bar.showMessage(f"Timeline: {minutes:02d}:{seconds:02d}")

        except Exception as e:
            print(f"❌ Error seeking video player: {e}")

    def import_video(self):
        """Import video file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Video",
            "",
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v *.3gp);;All Files (*)"
        )

        if file_path:
            self.load_video_from_file(file_path)
            # Also add to timeline
            self.timeline.add_clip(file_path)

    def export_video_quick(self):
        """Quick export video"""
        output_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Video",
            "exported_video.mp4",
            "Video Files (*.mp4 *.avi *.mov *.mkv);;All Files (*)"
        )

        if output_path:
            QMessageBox.information(
                self,
                "Export Started",
                f"Video export started to:\n{output_path}\n\nThis is a placeholder. Full export functionality will be implemented."
            )

    # Panel management methods
    def show_search_panel(self):
        """Show search panel"""
        self.left_tabs.setCurrentWidget(self.search_panel)

    def show_file_browser(self):
        """Show file browser panel"""
        self.left_tabs.setCurrentWidget(self.file_browser)

    def show_download_manager(self):
        """Show download manager panel"""
        self.left_tabs.setCurrentWidget(self.download_manager)

    def show_upload_panel(self):
        """Show social media upload panel"""
        self.left_tabs.setCurrentWidget(self.social_upload)

    def show_audio_panel(self):
        """Show audio processing panel"""
        self.left_tabs.setCurrentWidget(self.audio_processing)

    def show_editor_panel(self):
        """Show advanced editor panel"""
        self.left_tabs.setCurrentWidget(self.advanced_editor)

    # Toolbar action methods
    def previous_frame(self):
        """Go to previous frame"""
        self.video_player.seek_backward()

    def play_pause(self):
        """Toggle play/pause"""
        self.video_player.toggle_play_pause()

    def next_frame(self):
        """Go to next frame"""
        self.video_player.seek_forward()

    def cut_tool(self):
        """Activate cut tool"""
        self.status_bar.showMessage("Cut tool activated")

    def selection_tool(self):
        """Activate selection tool"""
        self.status_bar.showMessage("Selection tool activated")

    def hand_tool(self):
        """Activate hand tool"""
        self.status_bar.showMessage("Hand tool activated")

    def export_video_advanced(self):
        """Show advanced export options"""
        self.show_editor_panel()
        QMessageBox.information(self, "Export Video",
                              "Advanced export options are available in the Editor panel.\n\n"
                              "Use the Editor tab to configure export settings and export your video.")

    # Menu actions (placeholder implementations)
    def new_project(self):
        reply = QMessageBox.question(self, "New Project", "Create a new project?\n(Current work will be lost)")
        if reply == QMessageBox.Yes:
            # TODO: Reset timeline and clear all tracks
            QMessageBox.information(self, "New Project", "New project created!")

    def open_project(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Open Project", "", "Project Files (*.vep);;All Files (*)")
        if file_path:
            QMessageBox.information(self, "Open Project", f"Opening project: {os.path.basename(file_path)}")

    def save_project(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Project", "", "Project Files (*.vep);;All Files (*)")
        if file_path:
            QMessageBox.information(self, "Save Project", f"Project saved: {os.path.basename(file_path)}")

    def import_media(self):
        files, _ = QFileDialog.getOpenFileNames(self, "Import Media", "",
                                               "Media Files (*.mp4 *.avi *.mov *.mkv *.mp3 *.wav *.jpg *.png);;All Files (*)")
        if files:
            QMessageBox.information(self, "Import Media", f"Imported {len(files)} files to project")

    def export_video(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Video", "", "Video Files (*.mp4);;All Files (*)")
        if file_path:
            QMessageBox.information(self, "Export Video", f"Exporting to: {os.path.basename(file_path)}")

    def undo(self):
        QMessageBox.information(self, "Undo", "Undo functionality coming soon!")

    def redo(self):
        QMessageBox.information(self, "Redo", "Redo functionality coming soon!")

    def cut(self):
        QMessageBox.information(self, "Cut", "Cut functionality coming soon!")

    def copy(self):
        QMessageBox.information(self, "Copy", "Copy functionality coming soon!")

    def paste(self):
        QMessageBox.information(self, "Paste", "Paste functionality coming soon!")

    def reset_workspace(self):
        reply = QMessageBox.question(self, "Reset Workspace", "Reset workspace to default layout?")
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "Reset Workspace", "Workspace reset!")

    def previous_frame(self):
        # TODO: Implement frame navigation
        pass

    def play_pause(self):
        # TODO: Implement play/pause
        pass

    def next_frame(self):
        # TODO: Implement frame navigation
        pass

    def cut_tool(self):
        QMessageBox.information(self, "Cut Tool", "Cut tool selected!")

    def selection_tool(self):
        QMessageBox.information(self, "Selection Tool", "Selection tool selected!")

    def hand_tool(self):
        QMessageBox.information(self, "Hand Tool", "Hand tool selected!")

    def show_text_overlay_panel(self):
        """Show text overlay panel"""
        self.left_tabs.setCurrentWidget(self.text_overlay_editor)

    def add_text_overlay(self, text_overlay):
        """Add text overlay to the current video"""
        try:
            if not hasattr(self, 'text_overlays'):
                self.text_overlays = []

            self.text_overlays.append(text_overlay)

            # Update status
            self.status_bar.showMessage(f"Text overlay added: '{text_overlay.text[:20]}...'")

            # Show confirmation
            QMessageBox.information(
                self,
                "Text Overlay Added",
                f"Text overlay '{text_overlay.text}' has been added to the video!\n\n"
                f"Font: {text_overlay.font_family}\n"
                f"Size: {text_overlay.font_size}px\n"
                f"Position: {text_overlay.position_x}%, {text_overlay.position_y}%\n"
                f"Duration: {text_overlay.start_time}s - {text_overlay.end_time}s\n\n"
                f"Use 'Export with Text' to render the video with text overlays."
            )

        except Exception as e:
            print(f"Error adding text overlay: {e}")
            QMessageBox.warning(self, "Error", f"Failed to add text overlay: {str(e)}")

    def export_video_with_text(self):
        """Export video with text overlays"""
        try:
            if not hasattr(self, 'current_video_path') or not self.current_video_path:
                QMessageBox.warning(self, "No Video", "Please load a video first.")
                return

            if not hasattr(self, 'text_overlays') or not self.text_overlays:
                QMessageBox.warning(self, "No Text Overlays", "Please add text overlays first.")
                return

            # Get output path
            output_path, _ = QFileDialog.getSaveFileName(
                self,
                "Export Video with Text",
                "video_with_text.mp4",
                "Video Files (*.mp4 *.avi *.mov);;All Files (*)"
            )

            if output_path:
                # Show progress dialog
                progress_dialog = QProgressDialog("Rendering video with text overlays...", "Cancel", 0, 100, self)
                progress_dialog.setWindowModality(Qt.WindowModal)
                progress_dialog.show()

                def progress_callback(progress):
                    progress_dialog.setValue(int(progress))
                    QApplication.processEvents()

                # Render video with text overlays
                try:
                    from ..utils.text_renderer import get_text_renderer
                    text_renderer = get_text_renderer()

                    success = text_renderer.render_text_overlays_on_video(
                        self.current_video_path,
                        output_path,
                        self.text_overlays,
                        progress_callback
                    )

                    progress_dialog.close()

                    if success:
                        QMessageBox.information(
                            self,
                            "Export Complete",
                            f"Video with text overlays exported successfully to:\n{output_path}"
                        )
                    else:
                        QMessageBox.warning(
                            self,
                            "Export Failed",
                            "Failed to export video with text overlays. Please check the logs for details."
                        )
                except ImportError:
                    progress_dialog.close()
                    QMessageBox.warning(
                        self,
                        "Feature Not Available",
                        "Text rendering feature requires additional dependencies. Please install opencv-python and Pillow."
                    )

        except Exception as e:
            print(f"Error exporting video with text: {e}")
            QMessageBox.critical(self, "Export Error", f"Failed to export video: {str(e)}")
