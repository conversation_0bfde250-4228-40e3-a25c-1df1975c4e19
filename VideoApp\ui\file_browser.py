"""
File Browser for Video Selection
Browse and select video files from local disk drives
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from typing import List

class FileBrowserWidget(QWidget):
    """File browser widget for selecting video files"""
    
    file_selected = pyqtSignal(str)  # Emits file path when selected
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_path = os.path.expanduser("~")  # Start from home directory
        self.video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
        self.setup_ui()
        self.load_directory(self.current_path)
        
    def setup_ui(self):
        """Setup file browser interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Header
        header = QLabel("📁 FILE BROWSER")
        header.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #0078d4;
                padding: 10px;
                border-bottom: 2px solid #0078d4;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(header)
        
        # Navigation bar
        nav_bar = self.create_navigation_bar()
        layout.addWidget(nav_bar)
        
        # Quick access buttons
        quick_access = self.create_quick_access()
        layout.addWidget(quick_access)
        
        # File tree
        self.file_tree = self.create_file_tree()
        layout.addWidget(self.file_tree)
        
        # File info panel
        self.info_panel = self.create_info_panel()
        layout.addWidget(self.info_panel)
        
        # Action buttons
        action_buttons = self.create_action_buttons()
        layout.addWidget(action_buttons)
        
    def create_navigation_bar(self):
        """Create navigation bar with path and controls"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Back button
        back_btn = QPushButton("⬅️")
        back_btn.setFixedSize(30, 30)
        back_btn.setToolTip("Go Back")
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                border-radius: 15px;
                color: white;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        back_btn.clicked.connect(self.go_back)
        layout.addWidget(back_btn)
        
        # Up button
        up_btn = QPushButton("⬆️")
        up_btn.setFixedSize(30, 30)
        up_btn.setToolTip("Go Up")
        up_btn.setStyleSheet("""
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                border-radius: 15px;
                color: white;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        up_btn.clicked.connect(self.go_up)
        layout.addWidget(up_btn)
        
        # Path display
        self.path_label = QLineEdit()
        self.path_label.setReadOnly(True)
        self.path_label.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                background-color: #333;
                color: white;
                border: 1px solid #555;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.path_label)
        
        # Refresh button
        refresh_btn = QPushButton("🔄")
        refresh_btn.setFixedSize(30, 30)
        refresh_btn.setToolTip("Refresh")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                border-radius: 15px;
                color: white;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_directory)
        layout.addWidget(refresh_btn)
        
        return widget
        
    def create_quick_access(self):
        """Create quick access buttons for common locations"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Common locations
        locations = [
            ("🏠 Home", os.path.expanduser("~")),
            ("🖥️ Desktop", os.path.join(os.path.expanduser("~"), "Desktop")),
            ("📁 Documents", os.path.join(os.path.expanduser("~"), "Documents")),
            ("⬇️ Downloads", os.path.join(os.path.expanduser("~"), "Downloads")),
            ("🎬 Videos", os.path.join(os.path.expanduser("~"), "Videos")),
        ]
        
        for name, path in locations:
            if os.path.exists(path):
                btn = QPushButton(name)
                btn.setStyleSheet("""
                    QPushButton {
                        padding: 6px 12px;
                        background-color: #444;
                        color: white;
                        border: 1px solid #666;
                        border-radius: 3px;
                        margin: 2px;
                    }
                    QPushButton:hover {
                        background-color: #555;
                    }
                """)
                btn.clicked.connect(lambda checked, p=path: self.load_directory(p))
                layout.addWidget(btn)
                
        layout.addStretch()
        
        # Drive selection (Windows)
        if sys.platform == "win32":
            drives_label = QLabel("Drives:")
            drives_label.setStyleSheet("color: white; font-weight: bold;")
            layout.addWidget(drives_label)
            
            import string
            for drive in string.ascii_uppercase:
                drive_path = f"{drive}:\\"
                if os.path.exists(drive_path):
                    drive_btn = QPushButton(f"{drive}:")
                    drive_btn.setFixedSize(30, 25)
                    drive_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #666;
                            color: white;
                            border: 1px solid #888;
                            border-radius: 3px;
                            font-size: 10px;
                            margin: 1px;
                        }
                        QPushButton:hover {
                            background-color: #777;
                        }
                    """)
                    drive_btn.clicked.connect(lambda checked, p=drive_path: self.load_directory(p))
                    layout.addWidget(drive_btn)
        
        return widget
        
    def create_file_tree(self):
        """Create file tree widget"""
        tree = QTreeWidget()
        tree.setHeaderLabels(["Name", "Size", "Type", "Modified"])
        tree.setStyleSheet("""
            QTreeWidget {
                background-color: #2b2b2b;
                color: white;
                border: 1px solid #555;
                alternate-background-color: #333;
                outline: none;
            }
            QTreeWidget::item {
                padding: 5px;
                border-bottom: 1px solid #444;
                min-height: 20px;
            }
            QTreeWidget::item:selected {
                background-color: #0078d4;
                border: none;
            }
            QTreeWidget::item:hover {
                background-color: #404040;
            }
            QTreeWidget::branch {
                background-color: transparent;
            }
        """)
        
        # Enable sorting
        tree.setSortingEnabled(True)
        tree.sortByColumn(0, Qt.AscendingOrder)
        
        # Connect signals
        tree.itemDoubleClicked.connect(self.on_item_double_clicked)
        tree.itemSelectionChanged.connect(self.on_selection_changed)
        
        return tree
        
    def create_info_panel(self):
        """Create file information panel"""
        widget = QWidget()
        widget.setFixedHeight(80)
        widget.setStyleSheet("""
            QWidget {
                background-color: #333;
                border: 1px solid #555;
                border-radius: 3px;
                margin-top: 5px;
            }
        """)
        
        layout = QHBoxLayout(widget)
        
        # File icon
        self.file_icon = QLabel("📄")
        self.file_icon.setFixedSize(50, 50)
        self.file_icon.setAlignment(Qt.AlignCenter)
        self.file_icon.setStyleSheet("""
            QLabel {
                background-color: #222;
                border: 1px solid #444;
                border-radius: 25px;
                font-size: 20px;
            }
        """)
        layout.addWidget(self.file_icon)
        
        # File details
        details_layout = QVBoxLayout()
        
        self.file_name_label = QLabel("No file selected")
        self.file_name_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        details_layout.addWidget(self.file_name_label)
        
        self.file_details_label = QLabel("")
        self.file_details_label.setStyleSheet("""
            QLabel {
                color: #aaa;
                font-size: 12px;
            }
        """)
        details_layout.addWidget(self.file_details_label)
        
        layout.addLayout(details_layout, 1)
        
        return widget
        
    def create_action_buttons(self):
        """Create action buttons"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Play button
        self.play_btn = QPushButton("▶️ Play Video")
        self.play_btn.setEnabled(False)
        self.play_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover:enabled {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #555;
                color: #888;
            }
        """)
        self.play_btn.clicked.connect(self.play_selected_file)
        layout.addWidget(self.play_btn)
        
        # Edit button
        self.edit_btn = QPushButton("✂️ Edit Video")
        self.edit_btn.setEnabled(False)
        self.edit_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #ffc107;
                color: black;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover:enabled {
                background-color: #e0a800;
            }
            QPushButton:disabled {
                background-color: #555;
                color: #888;
            }
        """)
        self.edit_btn.clicked.connect(self.edit_selected_file)
        layout.addWidget(self.edit_btn)
        
        layout.addStretch()
        
        # Browse button
        browse_btn = QPushButton("📁 Browse...")
        browse_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        browse_btn.clicked.connect(self.browse_for_file)
        layout.addWidget(browse_btn)
        
        return widget
        
    def load_directory(self, path):
        """Load directory contents"""
        try:
            self.current_path = os.path.abspath(path)
            self.path_label.setText(self.current_path)
            
            # Clear tree
            self.file_tree.clear()
            
            # Add parent directory item (if not root)
            if os.path.dirname(self.current_path) != self.current_path:
                parent_item = QTreeWidgetItem(["📁 ..", "", "Folder", ""])
                parent_item.setData(0, Qt.UserRole, os.path.dirname(self.current_path))
                self.file_tree.addTopLevelItem(parent_item)
            
            # List directory contents
            try:
                items = os.listdir(self.current_path)
                items.sort()
                
                for item_name in items:
                    item_path = os.path.join(self.current_path, item_name)
                    
                    try:
                        stat = os.stat(item_path)
                        is_dir = os.path.isdir(item_path)
                        
                        # Format size
                        if is_dir:
                            size_str = ""
                            type_str = "Folder"
                            icon = "📁"
                        else:
                            size = stat.st_size
                            if size < 1024:
                                size_str = f"{size} B"
                            elif size < 1024 * 1024:
                                size_str = f"{size / 1024:.1f} KB"
                            elif size < 1024 * 1024 * 1024:
                                size_str = f"{size / (1024 * 1024):.1f} MB"
                            else:
                                size_str = f"{size / (1024 * 1024 * 1024):.1f} GB"
                            
                            # Determine file type and icon
                            ext = os.path.splitext(item_name)[1].lower()
                            if ext in self.video_extensions:
                                type_str = "Video File"
                                icon = "🎬"
                            elif ext in ['.mp3', '.wav', '.flac', '.aac']:
                                type_str = "Audio File"
                                icon = "🎵"
                            elif ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                                type_str = "Image File"
                                icon = "🖼️"
                            else:
                                type_str = "File"
                                icon = "📄"
                        
                        # Format modified time
                        import datetime
                        modified = datetime.datetime.fromtimestamp(stat.st_mtime)
                        modified_str = modified.strftime("%Y-%m-%d %H:%M")
                        
                        # Create tree item
                        tree_item = QTreeWidgetItem([f"{icon} {item_name}", size_str, type_str, modified_str])
                        tree_item.setData(0, Qt.UserRole, item_path)
                        
                        # Highlight video files
                        if not is_dir and os.path.splitext(item_name)[1].lower() in self.video_extensions:
                            tree_item.setBackground(0, QColor(0, 120, 212, 30))
                        
                        self.file_tree.addTopLevelItem(tree_item)
                        
                    except (OSError, PermissionError):
                        # Skip files we can't access
                        continue
                        
            except PermissionError:
                error_item = QTreeWidgetItem(["❌ Access Denied", "", "", ""])
                self.file_tree.addTopLevelItem(error_item)
                
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load directory: {str(e)}")
            
    def on_item_double_clicked(self, item, column):
        """Handle item double click"""
        item_path = item.data(0, Qt.UserRole)
        if item_path:
            if os.path.isdir(item_path):
                self.load_directory(item_path)
            else:
                # Check if it's a video file
                ext = os.path.splitext(item_path)[1].lower()
                if ext in self.video_extensions:
                    self.file_selected.emit(item_path)
                    
    def on_selection_changed(self):
        """Handle selection change"""
        selected_items = self.file_tree.selectedItems()
        if selected_items:
            item = selected_items[0]
            item_path = item.data(0, Qt.UserRole)
            
            if item_path and os.path.exists(item_path):
                self.update_info_panel(item_path)
                
                # Enable/disable buttons based on file type
                is_video = os.path.splitext(item_path)[1].lower() in self.video_extensions
                self.play_btn.setEnabled(is_video and not os.path.isdir(item_path))
                self.edit_btn.setEnabled(is_video and not os.path.isdir(item_path))
            else:
                self.clear_info_panel()
        else:
            self.clear_info_panel()
            
    def update_info_panel(self, file_path):
        """Update file information panel"""
        try:
            file_name = os.path.basename(file_path)
            stat = os.stat(file_path)
            
            # Update icon
            if os.path.isdir(file_path):
                self.file_icon.setText("📁")
            else:
                ext = os.path.splitext(file_name)[1].lower()
                if ext in self.video_extensions:
                    self.file_icon.setText("🎬")
                elif ext in ['.mp3', '.wav', '.flac', '.aac']:
                    self.file_icon.setText("🎵")
                elif ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                    self.file_icon.setText("🖼️")
                else:
                    self.file_icon.setText("📄")
            
            # Update name
            self.file_name_label.setText(file_name)
            
            # Update details
            if os.path.isdir(file_path):
                try:
                    item_count = len(os.listdir(file_path))
                    details = f"Folder • {item_count} items"
                except PermissionError:
                    details = "Folder • Access denied"
            else:
                size = stat.st_size
                if size < 1024:
                    size_str = f"{size} B"
                elif size < 1024 * 1024:
                    size_str = f"{size / 1024:.1f} KB"
                elif size < 1024 * 1024 * 1024:
                    size_str = f"{size / (1024 * 1024):.1f} MB"
                else:
                    size_str = f"{size / (1024 * 1024 * 1024):.1f} GB"
                
                import datetime
                modified = datetime.datetime.fromtimestamp(stat.st_mtime)
                modified_str = modified.strftime("%Y-%m-%d %H:%M")
                
                details = f"{size_str} • Modified: {modified_str}"
            
            self.file_details_label.setText(details)
            
        except Exception as e:
            self.clear_info_panel()
            
    def clear_info_panel(self):
        """Clear file information panel"""
        self.file_icon.setText("📄")
        self.file_name_label.setText("No file selected")
        self.file_details_label.setText("")
        self.play_btn.setEnabled(False)
        self.edit_btn.setEnabled(False)
        
    def go_back(self):
        """Go back to previous directory"""
        # TODO: Implement history
        self.go_up()
        
    def go_up(self):
        """Go up one directory level"""
        parent_dir = os.path.dirname(self.current_path)
        if parent_dir != self.current_path:
            self.load_directory(parent_dir)
            
    def refresh_directory(self):
        """Refresh current directory"""
        self.load_directory(self.current_path)
        
    def play_selected_file(self):
        """Play selected video file"""
        selected_items = self.file_tree.selectedItems()
        if selected_items:
            item = selected_items[0]
            item_path = item.data(0, Qt.UserRole)
            if item_path and os.path.exists(item_path) and not os.path.isdir(item_path):
                # Check if it's a video file
                ext = os.path.splitext(item_path)[1].lower()
                if ext in self.video_extensions:
                    self.file_selected.emit(item_path)
                else:
                    QMessageBox.warning(self, "Invalid File", "Please select a video file.")
            else:
                QMessageBox.warning(self, "Invalid Selection", "Please select a valid video file.")
                
    def edit_selected_file(self):
        """Edit selected video file"""
        # TODO: Implement edit functionality
        selected_items = self.file_tree.selectedItems()
        if selected_items:
            item = selected_items[0]
            item_path = item.data(0, Qt.UserRole)
            if item_path:
                QMessageBox.information(self, "Edit Video", f"Edit functionality for:\n{item_path}\n\nComing soon!")
                
    def browse_for_file(self):
        """Open file dialog to browse for video"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Video File",
            self.current_path,
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v *.3gp);;All Files (*)"
        )
        
        if file_path:
            # Load the directory containing the selected file
            directory = os.path.dirname(file_path)
            self.load_directory(directory)
            
            # Select the file in the tree
            for i in range(self.file_tree.topLevelItemCount()):
                item = self.file_tree.topLevelItem(i)
                if item.data(0, Qt.UserRole) == file_path:
                    self.file_tree.setCurrentItem(item)
                    break
            
            # Emit the selection
            self.file_selected.emit(file_path)
