# -*- coding: utf-8 -*-
"""
Video transition effects for editing
"""

from core.libraries import *
import os
import moviepy.editor as mp
from typing import Optional, List


def apply_transition(
    video1_path: str,
    video2_path: str,
    transition_type: str = "fade",
    transition_duration: float = 1.0,
    output_path: Optional[str] = None
) -> str:
    """
    Apply a transition effect between two videos.
    
    Args:
        video1_path: Path to the first video
        video2_path: Path to the second video
        transition_type: Type of transition ("fade", "slide", "wipe", "dissolve")
        transition_duration: Duration of the transition in seconds
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load videos
        video1 = mp.VideoFileClip(video1_path)
        video2 = mp.VideoFileClip(video2_path)
        
        # Apply transition based on type
        if transition_type == "fade":
            final_video = create_fade_transition(video1, video2, transition_duration)
        elif transition_type == "slide":
            final_video = create_slide_transition(video1, video2, transition_duration)
        elif transition_type == "wipe":
            final_video = create_wipe_transition(video1, video2, transition_duration)
        elif transition_type == "dissolve":
            final_video = create_dissolve_transition(video1, video2, transition_duration)
        else:
            # Default to simple concatenation
            final_video = mp.concatenate_videoclips([video1, video2])
        
        # Generate output path if not provided
        if output_path is None:
            base_name1 = os.path.splitext(os.path.basename(video1_path))[0]
            base_name2 = os.path.splitext(os.path.basename(video2_path))[0]
            output_path = f"{base_name1}_{base_name2}_transition.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video1.close()
        video2.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to apply transition: {str(e)}")


def create_fade_transition(video1: mp.VideoFileClip, video2: mp.VideoFileClip, duration: float) -> mp.VideoFileClip:
    """Create a fade transition between two videos"""
    
    # Fade out the first video
    video1_fadeout = video1.fadeout(duration)
    
    # Fade in the second video
    video2_fadein = video2.fadein(duration)
    
    # Overlap the videos during transition
    video2_delayed = video2_fadein.set_start(video1.duration - duration)
    
    # Composite the videos
    final_video = mp.CompositeVideoClip([video1_fadeout, video2_delayed])
    
    return final_video


def create_slide_transition(video1: mp.VideoFileClip, video2: mp.VideoFileClip, duration: float) -> mp.VideoFileClip:
    """Create a slide transition between two videos"""
    
    # Get video dimensions
    w, h = video1.size
    
    # Create slide effect for video2 (slide in from right)
    video2_slide = video2.set_position(lambda t: (max(0, w - w*t/duration), 0) if t < duration else (0, 0))
    video2_slide = video2_slide.set_start(video1.duration - duration)
    
    # Composite the videos
    final_video = mp.CompositeVideoClip([video1, video2_slide])
    
    return final_video


def create_wipe_transition(video1: mp.VideoFileClip, video2: mp.VideoFileClip, duration: float) -> mp.VideoFileClip:
    """Create a wipe transition between two videos"""
    
    # Get video dimensions
    w, h = video1.size
    
    # Create wipe effect using a mask
    def make_mask(t):
        if t < duration:
            # Create a mask that reveals video2 progressively
            mask_width = int(w * t / duration)
            mask = mp.ColorClip(size=(mask_width, h), color=(255, 255, 255), duration=0.1)
            return mask.set_position((0, 0))
        else:
            return mp.ColorClip(size=(w, h), color=(255, 255, 255), duration=0.1)
    
    # Apply wipe effect
    video2_wiped = video2.set_start(video1.duration - duration)
    
    # Simple concatenation for now (complex masking requires more setup)
    final_video = mp.concatenate_videoclips([video1, video2])
    
    return final_video


def create_dissolve_transition(video1: mp.VideoFileClip, video2: mp.VideoFileClip, duration: float) -> mp.VideoFileClip:
    """Create a dissolve transition between two videos"""
    
    # Create crossfade effect
    video1_part = video1.subclip(0, video1.duration - duration/2)
    video1_fade = video1.subclip(video1.duration - duration/2).fadeout(duration/2)
    
    video2_fade = video2.subclip(0, duration/2).fadein(duration/2)
    video2_part = video2.subclip(duration/2)
    
    # Composite the fade parts
    crossfade = mp.CompositeVideoClip([video1_fade, video2_fade.set_start(0)])
    
    # Concatenate all parts
    final_video = mp.concatenate_videoclips([video1_part, crossfade, video2_part])
    
    return final_video


def create_multiple_transitions(
    video_paths: List[str],
    transition_type: str = "fade",
    transition_duration: float = 1.0,
    output_path: Optional[str] = None
) -> str:
    """
    Create transitions between multiple videos.
    
    Args:
        video_paths: List of video file paths
        transition_type: Type of transition to apply
        transition_duration: Duration of each transition
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        if len(video_paths) < 2:
            raise ValueError("At least 2 videos are required for transitions")
        
        # Load all videos
        videos = [mp.VideoFileClip(path) for path in video_paths]
        
        # Create transitions between consecutive videos
        result_video = videos[0]
        
        for i in range(1, len(videos)):
            if transition_type == "fade":
                result_video = create_fade_transition(result_video, videos[i], transition_duration)
            elif transition_type == "slide":
                result_video = create_slide_transition(result_video, videos[i], transition_duration)
            elif transition_type == "dissolve":
                result_video = create_dissolve_transition(result_video, videos[i], transition_duration)
            else:
                # Simple concatenation
                result_video = mp.concatenate_videoclips([result_video, videos[i]])
        
        # Generate output path if not provided
        if output_path is None:
            output_path = f"multi_video_transitions.mp4"
        
        # Write output
        result_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        for video in videos:
            video.close()
        result_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create multiple transitions: {str(e)}")


def add_fade_in(video_path: str, fade_duration: float = 1.0, output_path: Optional[str] = None) -> str:
    """
    Add fade-in effect to the beginning of a video.
    
    Args:
        video_path: Path to the input video
        fade_duration: Duration of the fade-in effect
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Apply fade-in
        faded_video = video.fadein(fade_duration)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_fadein.mp4"
        
        # Write output
        faded_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        faded_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to add fade-in: {str(e)}")


def add_fade_out(video_path: str, fade_duration: float = 1.0, output_path: Optional[str] = None) -> str:
    """
    Add fade-out effect to the end of a video.
    
    Args:
        video_path: Path to the input video
        fade_duration: Duration of the fade-out effect
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load video
        video = mp.VideoFileClip(video_path)
        
        # Apply fade-out
        faded_video = video.fadeout(fade_duration)
        
        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(video_path)[0]
            output_path = f"{base_name}_fadeout.mp4"
        
        # Write output
        faded_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video.close()
        faded_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to add fade-out: {str(e)}")


def create_crossfade(
    video1_path: str,
    video2_path: str,
    crossfade_duration: float = 2.0,
    output_path: Optional[str] = None
) -> str:
    """
    Create a crossfade between two videos.
    
    Args:
        video1_path: Path to the first video
        video2_path: Path to the second video
        crossfade_duration: Duration of the crossfade effect
        output_path: Output file path (optional)
    
    Returns:
        Path to the output video file
    """
    try:
        # Load videos
        video1 = mp.VideoFileClip(video1_path)
        video2 = mp.VideoFileClip(video2_path)
        
        # Create crossfade
        final_video = create_dissolve_transition(video1, video2, crossfade_duration)
        
        # Generate output path if not provided
        if output_path is None:
            base_name1 = os.path.splitext(os.path.basename(video1_path))[0]
            base_name2 = os.path.splitext(os.path.basename(video2_path))[0]
            output_path = f"{base_name1}_{base_name2}_crossfade.mp4"
        
        # Write output
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )
        
        # Clean up
        video1.close()
        video2.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        raise Exception(f"Failed to create crossfade: {str(e)}")


# Convenience functions
def quick_fade_transition(video1_path: str, video2_path: str) -> str:
    """Quick fade transition with default settings"""
    return apply_transition(video1_path, video2_path, "fade", 1.0)


def quick_slide_transition(video1_path: str, video2_path: str) -> str:
    """Quick slide transition with default settings"""
    return apply_transition(video1_path, video2_path, "slide", 1.0)


def quick_dissolve_transition(video1_path: str, video2_path: str) -> str:
    """Quick dissolve transition with default settings"""
    return apply_transition(video1_path, video2_path, "dissolve", 2.0)
