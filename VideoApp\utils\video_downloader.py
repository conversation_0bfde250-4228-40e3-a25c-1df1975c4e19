# -*- coding: utf-8 -*-
import os, datetime
from typing import List, Dict, Optional, Any, Callable
import yt_dlp
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

class VideoDownloader:
    def __init__(self, max_workers: int = 3):
        self.download_queue = []
        self.active_downloads = {}
        self.completed_downloads = []
        self.failed_downloads = []
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.download_dir = "downloads"
        self.progress_callbacks = {}
        
        # Create download directory if it doesn't exist
        os.makedirs(self.download_dir, exist_ok=True)

    def list_formats(self, url: str) -> List[Dict]:
        ydl_opts = {'quiet': True, 'skip_download': True, 'no_warnings': True}
        out = []
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            for f in info.get('formats', []):
                out.append({
                    'format_id': f.get('format_id'),
                    'ext': f.get('ext'),
                    'resolution': f.get('format_note') or f.get('resolution'),
                    'abr': f.get('abr'),
                    'vbr': f.get('vbr'),
                    'filesize': f.get('filesize'),
                    'format': f.get('format'),
                    'height': f.get('height'),
                    'width': f.get('width'),
                })
        return out

    def download_video(self, url: str, format_id: str = 'best', 
                      output_path: str = None, callback: Callable = None, **kwargs) -> str:
        """Download a video with progress callback support"""
        if output_path is None:
            ts = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            outtmpl = f"{self.download_dir}/%(title)s_{ts}.%(ext)s"
        else:
            outtmpl = output_path
        
        # Store callback for this download
        download_id = f"{url}_{format_id}_{time.time()}"
        if callback:
            self.progress_callbacks[download_id] = callback
        
        ydl_opts = {
            'format': format_id,
            'outtmpl': outtmpl,
            'progress_hooks': [self._progress_hook],
        }
        
        # Add postprocessor args if provided
        if 'postprocessor_args' in kwargs:
            ydl_opts['postprocessor_args'] = kwargs['postprocessor_args']
        
        # Add to active downloads
        self.active_downloads[download_id] = {
            'url': url,
            'format': format_id,
            'status': 'queued',
            'progress': 0,
            'start_time': time.time(),
            'filename': os.path.basename(outtmpl.replace('%(title)s', 'video').replace('%(ext)s', 'mp4'))
        }
        
        # Submit download to thread pool
        future = self.executor.submit(self._download_task, download_id, ydl_opts, url)
        future.add_done_callback(lambda f: self._download_complete(download_id, f))
        
        return download_id

    def _download_task(self, download_id: str, ydl_opts: dict, url: str):
        """The actual download task running in a thread"""
        try:
            self.active_downloads[download_id]['status'] = 'downloading'
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                return ydl.download([url])
        except Exception as e:
            return e

    def _download_complete(self, download_id: str, future):
        """Handle download completion"""
        try:
            result = future.result()
            download_info = self.active_downloads[download_id]
            
            if isinstance(result, Exception):
                download_info['status'] = 'failed'
                download_info['error'] = str(result)
                self.failed_downloads.append(download_info)
                
                # Call progress callback with error
                if download_id in self.progress_callbacks:
                    error_info = download_info.copy()
                    error_info['status'] = 'failed'
                    error_info['progress'] = 0
                    self.progress_callbacks[download_id](error_info)
            else:
                download_info['status'] = 'completed'
                download_info['end_time'] = time.time()
                download_info['duration'] = download_info['end_time'] - download_info['start_time']
                download_info['progress'] = 100
                self.completed_downloads.append(download_info)
                
                # Call progress callback with completion
                if download_id in self.progress_callbacks:
                    complete_info = download_info.copy()
                    complete_info['progress'] = 100
                    self.progress_callbacks[download_id](complete_info)
            
            # Remove from active downloads
            del self.active_downloads[download_id]
            
            # Remove callback
            if download_id in self.progress_callbacks:
                del self.progress_callbacks[download_id]
                
        except Exception as e:
            print(f"Error handling download completion: {e}")

    def _progress_hook(self, d):
        """Progress hook for yt-dlp"""
        if d['status'] == 'downloading':
            # Find the download by filename pattern
            for download_id, info in self.active_downloads.items():
                if info['status'] == 'downloading':
                    # Calculate progress percentage
                    if 'total_bytes' in d:
                        progress = (d['downloaded_bytes'] / d['total_bytes']) * 100
                    elif 'total_bytes_estimate' in d:
                        progress = (d['downloaded_bytes'] / d['total_bytes_estimate']) * 100
                    else:
                        progress = 0
                    
                    info['progress'] = progress
                    info['speed'] = d.get('speed', 0)
                    info['eta'] = d.get('eta', 0)
                    
                    # Call progress callback if registered
                    if download_id in self.progress_callbacks:
                        self.progress_callbacks[download_id](info)
        
        elif d['status'] == 'finished':
            for download_id, info in self.active_downloads.items():
                if info['status'] == 'downloading':
                    info['progress'] = 100
                    info['status'] = 'completed'
                    if download_id in self.progress_callbacks:
                        self.progress_callbacks[download_id](info)

    def download_audio_only(self, url: str, audio_format: str = 'mp3', 
                           output_path: str = None, callback: Callable = None,
                           bitrate: Optional[int] = None) -> str:
        """Download audio only with progress callback support"""
        if output_path is None:
            ts = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            outtmpl = f"{self.download_dir}/%(title)s_{ts}.%(ext)s"
        else:
            outtmpl = output_path
        
        # Store callback for this download
        download_id = f"{url}_{audio_format}_{time.time()}"
        if callback:
            self.progress_callbacks[download_id] = callback
        
        ydl_opts = {
            'format': 'bestaudio/best',
            'outtmpl': outtmpl,
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': audio_format,
                'preferredquality': str(bitrate) if bitrate else '192',
            }],
            'progress_hooks': [self._progress_hook],
        }
        
        # Add to active downloads
        self.active_downloads[download_id] = {
            'url': url,
            'format': f'audio_{audio_format}',
            'status': 'queued',
            'progress': 0,
            'start_time': time.time(),
            'filename': os.path.basename(outtmpl.replace('%(title)s', 'audio').replace('%(ext)s', audio_format))
        }
        
        # Submit download to thread pool
        future = self.executor.submit(self._download_task, download_id, ydl_opts, url)
        future.add_done_callback(lambda f: self._download_complete(download_id, f))
        
        return download_id

    def download_playlist(self, playlist_url: str, format_id: str = 'best', 
                         max_videos: int = None, callback: Callable = None) -> List[str]:
        """Download all videos in a playlist"""
        ydl_opts = {'quiet': True, 'extract_flat': True, 'no_warnings': True}
        download_ids = []
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(playlist_url, download=False)
            if 'entries' in info:
                videos = info['entries'][:max_videos] if max_videos else info['entries']
                for video in videos:
                    if 'url' in video:
                        download_id = self.download_video(video['url'], format_id, callback=callback)
                        download_ids.append(download_id)
        
        return download_ids

    def get_download_status(self, download_id: str) -> Optional[Dict]:
        """Get the status of a download"""
        return self.active_downloads.get(download_id)

    def cancel_download(self, download_id: str) -> bool:
        """Cancel an active download"""
        # Note: yt-dlp doesn't support direct cancellation, but we can mark it for cleanup
        if download_id in self.active_downloads:
            self.active_downloads[download_id]['status'] = 'cancelled'
            # The download will eventually complete but we ignore the result
            return True
        return False

    def get_completed_downloads(self) -> List[Dict]:
        """Get list of completed downloads"""
        return self.completed_downloads

    def get_failed_downloads(self) -> List[Dict]:
        """Get list of failed downloads"""
        return self.failed_downloads

    def cleanup_old_downloads(self, max_age_hours: int = 24):
        """Clean up download records older than specified hours"""
        cutoff_time = time.time() - (max_age_hours * 3600)
        
        self.completed_downloads = [
            d for d in self.completed_downloads 
            if d.get('end_time', 0) > cutoff_time
        ]
        
        self.failed_downloads = [
            d for d in self.failed_downloads 
            if d.get('end_time', 0) > cutoff_time
        ]

    def get_best_format(self, url: str, prefer_mp4: bool = True) -> str:
        """Get the best format ID for a video"""
        formats = self.list_formats(url)
        if not formats:
            return 'best'
        
        # Prefer MP4 format
        if prefer_mp4:
            mp4_formats = [f for f in formats if f.get('ext') == 'mp4' and f.get('height')]
            if mp4_formats:
                # Get the highest resolution MP4
                mp4_formats.sort(key=lambda x: x.get('height', 0), reverse=True)
                return mp4_formats[0]['format_id']
        
        # Fallback to highest resolution overall
        video_formats = [f for f in formats if f.get('height')]
        if video_formats:
            video_formats.sort(key=lambda x: x.get('height', 0), reverse=True)
            return video_formats[0]['format_id']
        
        return 'best'

    def search_videos(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for videos.

        Args:
            query: Search query
            **kwargs: Additional options

        Returns:
            List of search results
        """
        max_results = kwargs.get('max_results', 10)
        platform = kwargs.get('platform', 'youtube')

        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'ignoreerrors': True,
        }

        # Format search query properly
        if platform == 'youtube':
            search_query = f"ytsearch{max_results}:{query}"
        elif platform == 'vimeo':
            search_query = f"vimeosearch{max_results}:{query}"
        else:
            search_query = f"ytsearch{max_results}:{query}"  # Default to YouTube

        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(search_query, download=False)
                return info.get('entries', [])[:max_results]
        except Exception as e:
            print(f"Search error: {e}")
            return []

    def get_trending_videos(self, platform: str = "youtube", **kwargs) -> List[Dict[str, Any]]:
        """
        Get trending videos from a platform.
        
        Args:
            platform: Platform to get trending videos from
            **kwargs: Additional options
            
        Returns:
            List of trending videos
        """
        ydl_opts = {
            'extract_flat': True,
            'no_warnings': True,
        }
        
        # Platform-specific trending URLs
        trending_urls = {
            'youtube': 'https://www.youtube.com/feed/trending',
            'vimeo': 'https://vimeo.com/channels/staffpicks',
        }
        
        url = trending_urls.get(platform, trending_urls['youtube'])
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return info.get('entries', [])[:kwargs.get('max_results', 20)]

    def search_channels(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for channels.
        
        Args:
            query: Search query
            **kwargs: Additional options
            
        Returns:
            List of channel results
        """
        ydl_opts = {
            'extract_flat': True,
            'no_warnings': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(f"ytsearchchannel:{query}", download=False)
            return info.get('entries', [])[:kwargs.get('max_results', 10)]

    def download_playlist_advanced(self, url: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Download a playlist with advanced options.
        
        Args:
            url: Playlist URL
            **kwargs: Additional options
            
        Returns:
            List of downloaded videos
        """
        ydl_opts = {
            'outtmpl': kwargs.get('outtmpl', f"{self.download_dir}/%(title)s.%(ext)s"),
            'format': kwargs.get('format', 'best'),
            'playlistend': kwargs.get('max_downloads', 10),
            'no_warnings': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=True)
            return info.get('entries', [])

# Standalone utility functions
def list_formats_standalone(url: str) -> List[Dict[str, Any]]:
    """List available formats for a video URL"""
    ydl_opts = {'quiet': True, 'no_warnings': True, 'skip_download': True}
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(url, download=False)
        return info.get('formats', [])

def download_video_standalone(url: str, **kwargs) -> str:
    """
    Download a video using yt-dlp.
    
    Args:
        url: URL of the video to download
        **kwargs: Additional options for yt-dlp
        
    Returns:
        Path to the downloaded video file
    """
    download_dir = "downloads"
    os.makedirs(download_dir, exist_ok=True)
    
    ydl_opts = {
        'outtmpl': kwargs.get('outtmpl', f'{download_dir}/%(title)s.%(ext)s'),
        'format': kwargs.get('format', 'best'),
        'no_warnings': True,
    }
    
    # Add postprocessor args if provided
    if 'postprocessor_args' in kwargs:
        ydl_opts['postprocessor_args'] = kwargs['postprocessor_args']
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info = ydl.extract_info(url, download=True)
        return ydl.prepare_filename(info)

def download_audio_only_standalone(url: str, audio_format: str = 'mp3', 
                                 bitrate: Optional[int] = None,
                                 output_dir: Optional[str] = None) -> str:
    """Download audio only from a video"""
    if output_dir is None:
        output_dir = "downloads"
        os.makedirs(output_dir, exist_ok=True)
    
    opts = {
        'format': 'bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': audio_format,
            'preferredquality': str(bitrate) if bitrate else '192',
        }],
        'outtmpl': os.path.join(output_dir, '%(title)s.%(ext)s'),
        'no_warnings': True,
    }
    
    with yt_dlp.YoutubeDL(opts) as ydl:
        info = ydl.extract_info(url, download=True)
        filename = ydl.prepare_filename(info)
        # Replace extension with the desired audio format
        return filename.replace('.webm', f'.{audio_format}').replace('.m4a', f'.{audio_format}')

def download_playlist_standalone(url: str, format_id: str = 'best', max_downloads: int = 10) -> List[str]:
    """Download a playlist"""
    download_dir = "downloads"
    os.makedirs(download_dir, exist_ok=True)
    
    opts = {
        'format': format_id,
        'outtmpl': os.path.join(download_dir, '%(playlist_index)s_%(title)s.%(ext)s'),
        'max_downloads': max_downloads,
        'no_warnings': True,
    }
    
    with yt_dlp.YoutubeDL(opts) as ydl:
        info = ydl.extract_info(url, download=True)
        return [ydl.prepare_filename(entry) for entry in info.get('entries', [])]

# Legacy functions for backward compatibility
def list_formats(url: str) -> List[Dict]:
    downloader = VideoDownloader()
    return downloader.list_formats(url)

def download_video(url: str, format_id: str = 'best') -> str:
    downloader = VideoDownloader()
    download_id = downloader.download_video(url, format_id)
    
    # Wait for download to complete (simple blocking version)
    while download_id in downloader.active_downloads:
        time.sleep(0.1)
    
    if downloader.get_download_status(download_id):
        return "Download failed"
    else:
        return "Download completed"

def download_audio_only(url: str, audio_format: str = 'mp3') -> str:
    downloader = VideoDownloader()
    download_id = downloader.download_audio_only(url, audio_format)
    
    # Wait for download to complete (simple blocking version)
    while download_id in downloader.active_downloads:
        time.sleep(0.1)
    
    if downloader.get_download_status(download_id):
        return "Download failed"
    else:
        return "Audio downloaded"

# Additional standalone functions for backward compatibility
def search_videos(query: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Search for videos.
    
    Args:
        query: Search query
        **kwargs: Additional options
        
    Returns:
        List of search results
    """
    downloader = VideoDownloader()
    return downloader.search_videos(query, **kwargs)

def search_videos_standalone(query: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Search for videos.
    
    Args:
        query: Search query
        **kwargs: Additional options
        
    Returns:
        List of search results
    """
    downloader = VideoDownloader()
    return downloader.search_videos(query, **kwargs)

def get_trending_videos_standalone(platform: str = "youtube", **kwargs) -> List[Dict[str, Any]]:
    """
    Get trending videos from a platform.
    
    Args:
        platform: Platform to get trending videos from
        **kwargs: Additional options
        
    Returns:
        List of trending videos
    """
    downloader = VideoDownloader()
    return downloader.get_trending_videos(platform, **kwargs)

def search_channels_standalone(query: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Search for channels.
    
    Args:
        query: Search query
        **kwargs: Additional options
        
    Returns:
        List of channel results
    """
    downloader = VideoDownloader()
    return downloader.search_channels(query, **kwargs)

def download_playlist_advanced_standalone(url: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Download a playlist.
    
    Args:
        url: Playlist URL
        **kwargs: Additional options
        
    Returns:
        List of downloaded videos
    """
    downloader = VideoDownloader()
    return downloader.download_playlist_advanced(url, **kwargs)

# New standalone functions to fix the import issue
def get_trending_videos(platform: str = "youtube", **kwargs) -> List[Dict[str, Any]]:
    """
    Get trending videos from a platform.
    
    Args:
        platform: Platform to get trending videos from
        **kwargs: Additional options
        
    Returns:
        List of trending videos
    """
    downloader = VideoDownloader()
    return downloader.get_trending_videos(platform, **kwargs)

def search_channels(query: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Search for channels.
    
    Args:
        query: Search query
        **kwargs: Additional options
        
    Returns:
        List of channel results
    """
    downloader = VideoDownloader()
    return downloader.search_channels(query, **kwargs)

def download_playlist_advanced(url: str, **kwargs) -> List[Dict[str, Any]]:
    """
    Download a playlist.
    
    Args:
        url: Playlist URL
        **kwargs: Additional options
        
    Returns:
        List of downloaded videos
    """
    downloader = VideoDownloader()
    return downloader.download_playlist_advanced(url, **kwargs)

# Add the missing download_playlist function
def download_playlist(url: str, format_id: str = 'best', max_downloads: int = 10) -> List[str]:
    """Download a playlist - wrapper for download_playlist_standalone"""
    return download_playlist_standalone(url, format_id, max_downloads)
